package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ETO基础信息实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_Basicinfo")
public class EtoBasicinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("EtoId")
    private Integer etoId;
    
    @TableField("SizeLiSi")
    private String sizeLiSi;
    
    @TableField("Pattern")
    private String pattern;
    
    @TableField("ProductCode")
    private String productCode;
    
    @TableField("Status")
    private String status;
    
    @TableField("SerialNumber")
    private String serialNumber;
    
    @TableField("SAP")
    private String sap;
    
    @TableField("Market")
    private String market;
    
    @TableField("ConstructionGroup")
    private String constructionGroup;
    
    @TableField("CurrentTest")
    private String currentTest;
    
    @TableField("Subject")
    private String subject;
    
    @TableField("MainTestItems")
    private String mainTestItems;
    
    @TableField("RequiredNumber")
    private String requiredNumber;
    
    @TableField("MoldDrawingUpdate")
    private String moldDrawingUpdate;
    
    @TableField("ElectronPrevulcanization")
    private String electronPrevulcanization;
    
    @TableField("FullSpringVent")
    private String fullSpringVent;
    
    @TableField("ImpactTest")
    private String impactTest;
    
    @TableField("StandardRimWidth")
    private String standardRimWidth;
    
    @TableField("UfdbUpdate")
    private String ufdbUpdate;
    
    @TableField("R2g")
    private String r2g;
    
    @TableField("TestResult")
    private String testResult;
    
    @TableField("VehicleRimWidth")
    private String vehicleRimWidth;
    
    @TableField("Signature")
    private String signature;
    
    @TableField("PlantInfo")
    private String plantInfo;
    
    @TableField("BuildingTime")
    private String buildingTime;
    
    @TableField("Engineer")
    private String engineer;
    
    @TableField("SubApprover")
    private String subApprover;
    
    @TableField("Approver")
    private String approver;
    
    @TableField("TireSize")
    private String tireSize;
    
    @TableField("CreateTime")
    private Date createTime;
    
    @TableField("UpdateTime")
    private Date updateTime;
}
