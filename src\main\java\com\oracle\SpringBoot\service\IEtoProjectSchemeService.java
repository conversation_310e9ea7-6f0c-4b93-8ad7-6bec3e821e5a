package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoProjectScheme;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * ETO项目方案服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoProjectSchemeService extends IService<EtoProjectScheme> {
    
    /**
     * 根据项目ID获取方案列表
     * @param projectId 项目ID
     * @return 方案列表
     */
    List<EtoProjectScheme> listByProjectId(Long projectId);
} 