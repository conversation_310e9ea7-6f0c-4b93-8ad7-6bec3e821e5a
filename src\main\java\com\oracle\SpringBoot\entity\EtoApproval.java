package com.oracle.SpringBoot.entity;

import java.util.Date;

import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ETO审批记录实体类
 */
@Data
@Table(name = "eto_approval")
public class EtoApproval {
    @Id
    private Long id;
    private Long projectId;      // 关联项目ID
    private String approver;     // 审批人
    private Date approvalTime;   // 审批时间
    private String status;       // 状态(approved/rejected)
    private String comments;     // 审批意见
} 