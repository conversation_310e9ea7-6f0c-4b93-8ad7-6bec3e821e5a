package com.oracle.SpringBoot.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;

@Component
public class LogBackupScheduler {

    private static final Logger logger = LoggerFactory.getLogger(LogBackupScheduler.class);

    @Scheduled(cron = "0 0 0 * * SUN") // 每周日的午夜执行
    public void backupLogsAutomatically() {
        try {
            String sourceLogFile = "logs/spring-boot.log";
            String backupFolder = "backup-logs";
            File sourceFile = new File(sourceLogFile);
            File backupFile = new File(backupFolder + "/spring-boot-backup-" + LocalDateTime.now() + ".log");
            File backupDir = new File(backupFolder);
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }
            copyFileUsingStream(sourceFile, backupFile);
            logger.info("自动日志备份成功");
        } catch (IOException e) {
            logger.error("自动日志备份失败：", e);
        }
    }

    private void copyFileUsingStream(File source, File dest) throws IOException {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer))!= -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
    }
}