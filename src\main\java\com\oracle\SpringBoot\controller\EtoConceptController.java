package com.oracle.SpringBoot.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.oracle.SpringBoot.common.R;
import com.oracle.SpringBoot.entity.EtoConceptParam;
import com.oracle.SpringBoot.entity.EtoConceptTarget;
import com.oracle.SpringBoot.entity.EtoConceptCompetitor;
import com.oracle.SpringBoot.entity.EtoConceptCompetitorValue;
import com.oracle.SpringBoot.mapper.EtoConceptTargetMapper;
import com.oracle.SpringBoot.mapper.EtoConceptCompetitorMapper;
import com.oracle.SpringBoot.mapper.EtoConceptCompetitorValueMapper;
import com.oracle.SpringBoot.service.EtoConceptService;
import com.oracle.SpringBoot.vo.CompetitorFormVO;
import com.oracle.SpringBoot.vo.ConceptDataVO;
import com.oracle.SpringBoot.vo.TargetFormVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

/**
 * ETO概念控制器
 */
@RestController
@RequestMapping({"/api", "/boot/api"})
public class EtoConceptController {
    
    @Autowired
    private EtoConceptService conceptService;
    
    @Autowired
    private EtoConceptTargetMapper targetMapper;
    
    @Autowired
    private EtoConceptCompetitorMapper competitorMapper;
    
    @Autowired
    private EtoConceptCompetitorValueMapper competitorValueMapper;
    
    /**
     * 添加参数
     */
    @PostMapping("/eto-project-params")
    public R<Long> addParam(@RequestBody EtoConceptParam param) {
        try {
            Long paramId = conceptService.addParam(param);
            return R.success(paramId);
        } catch (Exception e) {
            return R.error("添加参数失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取项目参数列表
     */
    @GetMapping("/eto-project-params/{projectId}")
    public R<List<EtoConceptParam>> getProjectParams(@PathVariable Long projectId) {
        try {
            List<EtoConceptParam> params = conceptService.getProjectParams(projectId);
            return R.success(params);
        } catch (Exception e) {
            return R.error("获取参数列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 设置目标值
     */
    @PostMapping("/eto-project-targets")
    public R<Void> setTargetValues(@RequestBody TargetFormVO targetForm) {
        try {
            conceptService.setTargetValues(targetForm);
            return R.success();
        } catch (Exception e) {
            return R.error("设置目标值失败：" + e.getMessage());
        }
    }
    
    /**
     * 添加竞品及其参数值
     */
    @PostMapping("/eto-project-competitors")
    public R<Long> addCompetitor(@RequestBody CompetitorFormVO competitorForm) {
        try {
            Long competitorId = conceptService.addCompetitor(competitorForm);
            return R.success(competitorId);
        } catch (Exception e) {
            return R.error("添加竞品失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除竞品
     */
    @DeleteMapping("/eto-project-competitors/{competitorId}")
    public R<Void> deleteCompetitor(@PathVariable Long competitorId) {
        try {
            // 先删除竞品的参数值
            competitorValueMapper.delete(
                new LambdaQueryWrapper<EtoConceptCompetitorValue>()
                    .eq(EtoConceptCompetitorValue::getCompetitorId, competitorId)
            );
            
            // 再删除竞品
            competitorMapper.deleteById(competitorId);
            
            return R.success();
        } catch (Exception e) {
            return R.error("删除竞品失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除参数
     */
    @DeleteMapping("/eto-project-params/{paramId}")
    public R<Void> deleteParam(@PathVariable Long paramId) {
        try {
            // 1. 获取参数信息，确认参数存在
            EtoConceptParam param = conceptService.getParamById(paramId);
            if (param == null) {
                return R.error("参数不存在");
            }
            
            // 2. 删除相关的目标值
            targetMapper.delete(
                new LambdaQueryWrapper<EtoConceptTarget>()
                    .eq(EtoConceptTarget::getParamId, paramId)
            );
            
            // 3. 删除相关的竞品参数值
            competitorValueMapper.delete(
                new LambdaQueryWrapper<EtoConceptCompetitorValue>()
                    .eq(EtoConceptCompetitorValue::getParamId, paramId)
            );
            
            // 4. 删除参数本身
            conceptService.deleteParamById(paramId);
            
            return R.success();
        } catch (Exception e) {
            return R.error("删除参数失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新竞品参数值
     */
    @PostMapping("/eto-project-competitor-values")
    public R<Void> updateCompetitorValues(@RequestBody Map<String, Object> requestData) {
        try {
            Long projectId = Long.parseLong(requestData.get("projectId").toString());
            Long competitorId = Long.parseLong(requestData.get("competitorId").toString());
            List<Map<String, Object>> paramValues = (List<Map<String, Object>>) requestData.get("paramValues");
            
            if (paramValues == null || paramValues.isEmpty()) {
                return R.success(); // 没有参数值要更新
            }
            
            LocalDateTime now = LocalDateTime.now();
            
            for (Map<String, Object> paramValue : paramValues) {
                Long paramId = Long.parseLong(paramValue.get("paramId").toString());
                String value = paramValue.get("value").toString();
                
                // 查找是否已存在该参数值
                LambdaQueryWrapper<EtoConceptCompetitorValue> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(EtoConceptCompetitorValue::getCompetitorId, competitorId)
                         .eq(EtoConceptCompetitorValue::getParamId, paramId);
                
                EtoConceptCompetitorValue existingValue = competitorValueMapper.selectOne(queryWrapper);
                
                if (existingValue != null) {
                    // 更新现有的参数值
                    existingValue.setParamValue(value);
                    existingValue.setUpdateTime(now);
                    competitorValueMapper.updateById(existingValue);
                } else {
                    // 添加新的参数值
                    EtoConceptCompetitorValue newValue = new EtoConceptCompetitorValue();
                    newValue.setCompetitorId(competitorId);
                    newValue.setParamId(paramId);
                    newValue.setParamValue(value);
                    newValue.setCreateTime(now);
                    newValue.setUpdateTime(now);
                    competitorValueMapper.insert(newValue);
                }
            }
            
            return R.success();
        } catch (Exception e) {
            return R.error("更新竞品参数值失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取项目概念数据
     */
    @GetMapping("/eto-project-concept/{projectId}")
    public R<ConceptDataVO> getProjectConceptData(@PathVariable Long projectId) {
        try {
            ConceptDataVO conceptData = conceptService.getProjectConceptData(projectId);
            return R.success(conceptData);
        } catch (Exception e) {
            return R.error("获取概念数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 用于调试查看产品概念原始数据
     */
    @GetMapping("/eto-project-concept-debug/{projectId}")
    public R<Map<String, Object>> getProjectConceptDebugData(@PathVariable Long projectId) {
        try {
            // 获取所有原始数据，方便前端调试
            Map<String, Object> debugData = new HashMap<>();
            
            // 1. 获取参数列表
            List<EtoConceptParam> params = conceptService.getProjectParams(projectId);
            debugData.put("params_raw", params);
            
            // 2. 将参数ID和名称映射为简单的键值对，方便调试
            Map<Long, String> paramMap = new HashMap<>();
            for (EtoConceptParam param : params) {
                paramMap.put(param.getId(), param.getParamName());
            }
            debugData.put("params_map", paramMap);
            
            // 3. 获取目标值原始数据
            LambdaQueryWrapper<EtoConceptTarget> targetWrapper = new LambdaQueryWrapper<>();
            targetWrapper.eq(EtoConceptTarget::getProjectId, projectId);
            List<EtoConceptTarget> targets = targetMapper.selectList(targetWrapper);
            debugData.put("targets_raw", targets);
            
            // 4. 获取目标值Map格式
            Map<Long, String> targetMap = new HashMap<>();
            for (EtoConceptTarget target : targets) {
                targetMap.put(target.getParamId(), target.getTargetValue());
            }
            debugData.put("targets_map", targetMap);
            
            // 5. 获取竞品原始数据
            LambdaQueryWrapper<EtoConceptCompetitor> competitorWrapper = new LambdaQueryWrapper<>();
            competitorWrapper.eq(EtoConceptCompetitor::getProjectId, projectId);
            List<EtoConceptCompetitor> competitors = competitorMapper.selectList(competitorWrapper);
            debugData.put("competitors_raw", competitors);
            
            // 6. 获取竞品值原始数据
            List<Long> competitorIds = competitors.stream()
                .map(EtoConceptCompetitor::getId)
                .collect(Collectors.toList());
            
            if (!competitorIds.isEmpty()) {
                LambdaQueryWrapper<EtoConceptCompetitorValue> valueWrapper = new LambdaQueryWrapper<>();
                valueWrapper.in(EtoConceptCompetitorValue::getCompetitorId, competitorIds);
                List<EtoConceptCompetitorValue> competitorValues = competitorValueMapper.selectList(valueWrapper);
                debugData.put("competitor_values_raw", competitorValues);
            }
            
            // 7. 最后也获取标准格式的数据
            ConceptDataVO conceptData = conceptService.getProjectConceptData(projectId);
            debugData.put("concept_data", conceptData);
            
            return R.success(debugData);
        } catch (Exception e) {
            return R.error("获取调试数据失败：" + e.getMessage());
        }
    }
}