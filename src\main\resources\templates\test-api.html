<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TireDesign BuildInno Platform API测试</h1>
        
        <div class="test-section">
            <h2>获取项目列表</h2>
            <button id="getProjects">测试</button>
            <pre id="projectsResult">结果将显示在这里...</pre>
        </div>
        
        <div class="test-section">
            <h2>创建新项目</h2>
            <button id="createProject">测试</button>
            <pre id="createResult">结果将显示在这里...</pre>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 测试获取项目列表
            $('#getProjects').click(function() {
                $.ajax({
                    url: '/boot/api/eto-projects',
                    type: 'GET',
                    success: function(res) {
                        $('#projectsResult').text(JSON.stringify(res, null, 2));
                    },
                    error: function(xhr, status, error) {
                        $('#projectsResult').text('错误: ' + error + '\n' + xhr.responseText);
                    }
                });
            });

            // 测试创建项目
            $('#createProject').click(function() {
                var newProject = {
                    projectNo: 'TEST-' + Math.floor(Math.random() * 1000),
                    projectName: '测试项目',
                    tireGroup: 'SUV',
                    size: '255/50R20',
                    market: 'China',
                    brand: 'LL',
                    status: 'active'
                };

                $.ajax({
                    url: '/boot/api/eto-project',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(newProject),
                    success: function(res) {
                        $('#createResult').text(JSON.stringify(res, null, 2));
                    },
                    error: function(xhr, status, error) {
                        $('#createResult').text('错误: ' + error + '\n' + xhr.responseText);
                    }
                });
            });
        });
    </script>
</body>
</html> 