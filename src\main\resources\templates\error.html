<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出错了 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
            color: #1e1e2d;
        }

        .error-container {
            text-align: center;
            padding: 40px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .error-icon {
            font-size: 64px;
            color: #dc2626;
            margin-bottom: 24px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        .error-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1e1e2d;
        }

        .error-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .error-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .error-actions .layui-btn {
            height: 40px;
            line-height: 40px;
            padding: 0 24px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .layui-btn-primary {
            border: 1px solid #e2e8f0;
            background: #fff;
            color: #4b5563;
        }

        .layui-btn-primary:hover {
            border-color: #3B82F6;
            color: #3B82F6;
        }

        .layui-btn-normal {
            background: #3B82F6;
        }

        .layui-btn-normal:hover {
            background: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .error-footer {
            margin-top: 32px;
            font-size: 14px;
            color: #666;
        }

        .error-footer a {
            color: #3B82F6;
            text-decoration: none;
            transition: color 0.3s;
        }

        .error-footer a:hover {
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <i class="fas fa-exclamation-circle error-icon"></i>
        <h1 class="error-title">抱歉，出现了一些问题</h1>
        <p class="error-message">
            ${error!"系统遇到了一些技术问题，我们正在努力修复中。"}
            <br>
            请稍后重试或联系系统管理员。
        </p>
        <div class="error-actions">
            <button class="layui-btn layui-btn-primary" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>&nbsp;返回上一页
            </button>
            <button class="layui-btn layui-btn-normal" onclick="window.location.href='index'">
                <i class="fas fa-home"></i>&nbsp;返回首页
            </button>
        </div>
        <div class="error-footer">
            <p>如果问题持续存在，请 <a href="mailto:<EMAIL>">联系技术支持</a></p>
            <p>错误代码：${errorCode!"500"}</p>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
</body>
</html>