<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案设计 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .scheme-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #EBEEF5;
        }

        .header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .header h2 i {
            margin-right: 10px;
            color: #409EFF;
        }

        .project-info {
            background: #F5F7FA;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .project-info .info-row {
            display: flex;
            margin-bottom: 8px;
        }

        .project-info .info-row:last-child {
            margin-bottom: 0;
        }

        .project-info .info-label {
            width: 120px;
            color: #606266;
            font-weight: 600;
        }

        .project-info .info-value {
            color: #333;
        }

        .scheme-tabs {
            margin: 20px 0;
        }

        .scheme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .scheme-actions {
            display: flex;
            gap: 10px;
        }

        .param-table {
            margin-top: 20px;
        }

        .param-table .layui-table {
            margin: 0;
        }

        .param-group {
            margin-bottom: 25px;
        }

        .param-group-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #EBEEF5;
            display: flex;
            align-items: center;
        }

        .param-group-title i {
            margin-right: 5px;
            color: #409EFF;
        }

        .comparison-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #EBEEF5;
        }

        .diff-highlight {
            background-color: #FFF7E6;
        }
        
        .scheme-form .layui-form-item {
            margin-bottom: 15px;
        }
        
        .scheme-form .param-input {
            width: 100%;
        }
        
        .scheme-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .status-draft {
            background-color: #909399;
        }
        
        .status-active {
            background-color: #67C23A;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="scheme-container">
            <div class="header">
                <h2><i class="fas fa-sitemap"></i>方案设计</h2>
                <div class="layui-btn-group">
                    <button class="layui-btn" id="addSchemeBtn">
                        <i class="fas fa-plus"></i> 添加方案
                    </button>
                    <button class="layui-btn layui-btn-normal" id="compareBtn">
                        <i class="fas fa-code-compare"></i> 对比方案
                    </button>
                    <button class="layui-btn layui-btn-primary" id="backBtn">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                </div>
            </div>

            <!-- 项目信息 -->
            <div class="project-info">
                <div class="info-row">
                    <div class="info-label">项目编号：</div>
                    <div class="info-value" id="projectNo"></div>
                </div>
                <div class="info-row">
                    <div class="info-label">项目名称：</div>
                    <div class="info-value" id="projectName"></div>
                </div>
                <div class="info-row">
                    <div class="info-label">轮胎尺寸：</div>
                    <div class="info-value" id="tireSize"></div>
                </div>
            </div>

            <!-- 方案选项卡 -->
            <div class="scheme-tabs">
                <div class="layui-tab" lay-filter="schemeTabs">
                    <ul class="layui-tab-title" id="schemeTabsList">
                        <!-- 方案选项卡由JavaScript动态加载 -->
                    </ul>
                    <div class="layui-tab-content" id="schemeTabsContent">
                        <!-- 方案内容由JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增方案弹窗 -->
    <div class="layui-form scheme-form" id="addSchemeForm" style="display: none; padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">方案名称</label>
            <div class="layui-input-block">
                <input type="text" name="schemeName" required lay-verify="required" placeholder="请输入方案名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">方案描述</label>
            <div class="layui-input-block">
                <textarea name="schemeDescription" placeholder="请输入方案描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">参考方案</label>
            <div class="layui-input-block">
                <input type="checkbox" name="isReference" lay-skin="switch" lay-text="是|否">
            </div>
        </div>
    </div>

    <!-- 方案参数编辑弹窗 -->
    <div class="layui-form scheme-form" id="editParamForm" style="display: none; padding: 20px;">
        <div class="layui-form-item">
            <label class="layui-form-label">参数名称</label>
            <div class="layui-input-block">
                <input type="text" name="paramName" required lay-verify="required" placeholder="请输入参数名称" autocomplete="off" class="layui-input" readonly>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">参数值</label>
            <div class="layui-input-block">
                <input type="text" name="paramValue" required lay-verify="required" placeholder="请输入参数值" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">参数单位</label>
            <div class="layui-input-block">
                <input type="text" name="paramUnit" placeholder="请输入参数单位" autocomplete="off" class="layui-input">
            </div>
        </div>
    </div>
    
    <!-- 方案对比弹窗 -->
    <div id="compareSchemeDiv" style="display: none; padding: 20px;">
        <div class="comparison-header">
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">基准方案</label>
                        <div class="layui-input-inline">
                            <select id="baseScheme" lay-filter="baseScheme"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">比较方案</label>
                        <div class="layui-input-inline">
                            <select id="compareScheme" lay-filter="compareScheme"></select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="comparisonResult">
            <!-- 比较结果由JavaScript动态加载 -->
        </div>
    </div>

    <!-- 方案参数模板 -->
    <script type="text/html" id="schemeTabTemplate">
        <div class="scheme-header">
            <h3>{{d.schemeName}} <span class="scheme-status status-{{d.status}}">{{d.statusText}}</span></h3>
            <div class="scheme-actions">
                <button class="layui-btn layui-btn-normal layui-btn-sm edit-scheme-btn" data-id="{{d.id}}">
                    <i class="fas fa-edit"></i> 编辑方案
                </button>
                <button class="layui-btn layui-btn-danger layui-btn-sm delete-scheme-btn" data-id="{{d.id}}">
                    <i class="fas fa-trash-alt"></i> 删除方案
                </button>
            </div>
        </div>
        <div class="scheme-description">{{d.schemeDescription || '暂无描述'}}</div>
        
        <div class="param-table">
            {{# layui.each(d.paramGroups, function(groupIndex, group){ }}
            <div class="param-group">
                <div class="param-group-title">
                    <i class="fas fa-{{group.icon}}"></i> {{group.name}}
                </div>
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th width="30%">参数名称</th>
                            <th width="30%">参数值</th>
                            <th width="20%">单位</th>
                            <th width="20%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{# layui.each(group.params, function(paramIndex, param){ }}
                        <tr>
                            <td>{{param.paramName}}</td>
                            <td>{{param.paramValue || '--'}}</td>
                            <td>{{param.paramUnit || '--'}}</td>
                            <td>
                                <button class="layui-btn layui-btn-xs edit-param-btn" 
                                        data-scheme-id="{{d.id}}" 
                                        data-param-group="{{group.key}}" 
                                        data-param-name="{{param.paramName}}"
                                        data-param-value="{{param.paramValue}}"
                                        data-param-unit="{{param.paramUnit}}">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                            </td>
                        </tr>
                        {{# }); }}
                    </tbody>
                </table>
            </div>
            {{# }); }}
        </div>
    </script>
    
    <!-- 对比结果模板 -->
    <script type="text/html" id="comparisonTemplate">
        {{# layui.each(d.groups, function(groupIndex, group){ }}
        <div class="param-group">
            <div class="param-group-title">
                <i class="fas fa-{{group.icon}}"></i> {{group.name}}
            </div>
            <table class="layui-table">
                <thead>
                    <tr>
                        <th width="25%">参数名称</th>
                        <th width="30%">{{d.baseScheme.schemeName}}</th>
                        <th width="30%">{{d.compareScheme.schemeName}}</th>
                        <th width="15%">差异</th>
                    </tr>
                </thead>
                <tbody>
                    {{# layui.each(group.params, function(paramIndex, param){ }}
                    <tr class="{{param.isDiff ? 'diff-highlight' : ''}}">
                        <td>{{param.paramName}}</td>
                        <td>{{param.baseValue || '--'}}{{param.baseUnit ? ' ' + param.baseUnit : ''}}</td>
                        <td>{{param.compareValue || '--'}}{{param.compareUnit ? ' ' + param.compareUnit : ''}}</td>
                        <td>{{param.isDiff ? '不同' : '相同'}}</td>
                    </tr>
                    {{# }); }}
                </tbody>
            </table>
        </div>
        {{# }); }}
    </script>

    <script src="../layui/layui.js"></script>
    <script>
    layui.use(['layer', 'form', 'element', 'laytpl'], function(){
        var layer = layui.layer;
        var form = layui.form;
        var element = layui.element;
        var laytpl = layui.laytpl;
        var $ = layui.jquery;
        
        // 获取项目ID
        var projectId = getProjectIdFromUrl();
        
        // 初始化加载项目信息和方案列表
        loadProjectInfo(projectId);
        loadSchemes(projectId);
        
        // 返回按钮事件
        $('#backBtn').on('click', function(){
            window.location.href = '/boot/eto-project/detail/' + projectId;
        });
        
        // 添加方案按钮事件
        $('#addSchemeBtn').on('click', function(){
            layer.open({
                type: 1,
                title: '添加方案',
                area: ['500px', '300px'],
                content: $('#addSchemeForm'),
                btn: ['确定', '取消'],
                yes: function(index){
                    var formData = form.val('addSchemeForm');
                    if(!formData.schemeName){
                        layer.msg('请输入方案名称', {icon: 2});
                        return;
                    }
                    
                    // 添加项目ID
                    formData.projectId = projectId;
                    formData.isReference = formData.isReference === 'on';
                    
                    layer.close(index);
                    layer.load(2);
                    
                    // 发送请求创建方案
                    $.ajax({
                        url: '/boot/api/eto-project-scheme',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(formData),
                        success: function(res){
                            layer.closeAll('loading');
                            if(res.code === 0){
                                layer.msg('方案创建成功', {icon: 1});
                                // 重新加载方案列表
                                loadSchemes(projectId);
                                // 重置表单
                                $('#addSchemeForm')[0].reset();
                                form.render();
                            } else {
                                layer.msg(res.msg || '创建失败，请重试', {icon: 2});
                            }
                        },
                        error: function(){
                            layer.closeAll('loading');
                            layer.msg('系统错误，请重试', {icon: 2});
                        }
                    });
                }
            });
        });
        
        // 对比方案按钮事件
        $('#compareBtn').on('click', function(){
            var schemes = window.schemeData;
            if(!schemes || schemes.length < 2){
                layer.msg('至少需要两个方案才能进行对比', {icon: 0});
                return;
            }
            
            // 清空选择框
            $('#baseScheme').empty();
            $('#compareScheme').empty();
            
            // 填充选择框
            $.each(schemes, function(i, scheme){
                var option = '<option value="' + scheme.id + '">' + scheme.schemeName + '</option>';
                $('#baseScheme').append(option);
                $('#compareScheme').append(option);
            });
            
            // 默认选择前两个方案
            $('#baseScheme').val(schemes[0].id);
            $('#compareScheme').val(schemes[1].id);
            
            form.render('select');
            
            // 打开弹窗
            layer.open({
                type: 1,
                title: '方案对比',
                area: ['900px', '600px'],
                content: $('#compareSchemeDiv'),
                success: function(){
                    // 加载对比结果
                    compareSchemes();
                    
                    // 监听选择框变化
                    form.on('select(baseScheme)', function(){
                        compareSchemes();
                    });
                    
                    form.on('select(compareScheme)', function(){
                        compareSchemes();
                    });
                }
            });
        });
        
        // 监听编辑方案按钮
        $(document).on('click', '.edit-scheme-btn', function(){
            var schemeId = $(this).data('id');
            var scheme = getSchemeById(schemeId);
            
            if(!scheme){
                layer.msg('方案信息不存在', {icon: 2});
                return;
            }
            
            layer.open({
                type: 1,
                title: '编辑方案',
                area: ['500px', '300px'],
                content: $('#addSchemeForm'),
                btn: ['确定', '取消'],
                success: function(){
                    // 填充表单数据
                    form.val('addSchemeForm', {
                        schemeName: scheme.schemeName,
                        schemeDescription: scheme.schemeDescription,
                        isReference: scheme.isReference ? 'on' : ''
                    });
                    form.render();
                },
                yes: function(index){
                    var formData = form.val('addSchemeForm');
                    if(!formData.schemeName){
                        layer.msg('请输入方案名称', {icon: 2});
                        return;
                    }
                    
                    // 添加项目ID和方案ID
                    formData.projectId = projectId;
                    formData.id = schemeId;
                    formData.isReference = formData.isReference === 'on';
                    
                    layer.close(index);
                    layer.load(2);
                    
                    // 发送请求更新方案
                    $.ajax({
                        url: '/boot/api/eto-project-scheme/' + schemeId,
                        type: 'PUT',
                        contentType: 'application/json',
                        data: JSON.stringify(formData),
                        success: function(res){
                            layer.closeAll('loading');
                            if(res.code === 0){
                                layer.msg('方案更新成功', {icon: 1});
                                // 重新加载方案列表
                                loadSchemes(projectId);
                            } else {
                                layer.msg(res.msg || '更新失败，请重试', {icon: 2});
                            }
                        },
                        error: function(){
                            layer.closeAll('loading');
                            layer.msg('系统错误，请重试', {icon: 2});
                        }
                    });
                }
            });
        });
        
        // 监听删除方案按钮
        $(document).on('click', '.delete-scheme-btn', function(){
            var schemeId = $(this).data('id');
            
            layer.confirm('确认删除此方案？<br>删除后将无法恢复！', {
                icon: 3,
                title: '删除确认'
            }, function(index){
                layer.close(index);
                layer.load(2);
                
                $.ajax({
                    url: '/boot/api/eto-project-scheme/' + schemeId,
                    type: 'DELETE',
                    success: function(res){
                        layer.closeAll('loading');
                        if(res.code === 0){
                            layer.msg('方案删除成功', {icon: 1});
                            loadSchemes(projectId);
                        } else {
                            layer.msg(res.msg || '删除失败，请重试', {icon: 2});
                        }
                    },
                    error: function(){
                        layer.closeAll('loading');
                        layer.msg('系统错误，请重试', {icon: 2});
                    }
                });
            });
        });
        
        // 监听参数编辑按钮
        $(document).on('click', '.edit-param-btn', function(){
            var schemeId = $(this).data('scheme-id');
            var paramGroup = $(this).data('param-group');
            var paramName = $(this).data('param-name');
            var paramValue = $(this).data('param-value') || '';
            var paramUnit = $(this).data('param-unit') || '';
            
            // 填充编辑表单
            form.val('editParamForm', {
                paramName: paramName,
                paramValue: paramValue,
                paramUnit: paramUnit
            });
            
            layer.open({
                type: 1,
                title: '编辑参数',
                area: ['500px', '300px'],
                content: $('#editParamForm'),
                btn: ['确定', '取消'],
                yes: function(index){
                    var formData = form.val('editParamForm');
                    
                    if(!formData.paramValue){
                        layer.msg('请输入参数值', {icon: 2});
                        return;
                    }
                    
                    var paramData = {
                        schemeId: schemeId,
                        paramGroup: paramGroup,
                        paramName: paramName,
                        paramValue: formData.paramValue,
                        paramUnit: formData.paramUnit
                    };
                    
                    layer.close(index);
                    layer.load(2);
                    
                    // 发送请求更新参数
                    $.ajax({
                        url: '/boot/api/eto-project-scheme/' + schemeId + '/param',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(paramData),
                        success: function(res){
                            layer.closeAll('loading');
                            if(res.code === 0){
                                layer.msg('参数更新成功', {icon: 1});
                                loadSchemes(projectId);
                            } else {
                                layer.msg(res.msg || '更新失败，请重试', {icon: 2});
                            }
                        },
                        error: function(){
                            layer.closeAll('loading');
                            layer.msg('系统错误，请重试', {icon: 2});
                        }
                    });
                }
            });
        });
        
        // 加载项目信息
        function loadProjectInfo(projectId){
            $.ajax({
                url: '/boot/api/eto-project/' + projectId,
                type: 'GET',
                success: function(res){
                    if(res.code === 0){
                        var project = res.data;
                        $('#projectNo').text(project.projectNo || '--');
                        $('#projectName').text(project.projectName || '--');
                        $('#tireSize').text(project.size || '--');
                        
                        // 设置页面标题
                        document.title = project.projectName + ' - 方案设计';
                    } else {
                        layer.msg(res.msg || '加载项目信息失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('系统错误，请重试', {icon: 2});
                }
            });
        }
        
        // 加载方案列表
        function loadSchemes(projectId){
            layer.load(2);
            $.ajax({
                url: '/boot/api/eto-project/' + projectId + '/schemes',
                type: 'GET',
                success: function(res){
                    layer.closeAll('loading');
                    if(res.code === 0){
                        renderSchemes(res.data);
                    } else {
                        layer.msg(res.msg || '加载方案列表失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.closeAll('loading');
                    layer.msg('系统错误，请重试', {icon: 2});
                }
            });
        }
        
        // 渲染方案选项卡
        function renderSchemes(schemes){
            // 保存方案数据到全局变量
            window.schemeData = schemes;
            
            // 清空选项卡
            $('#schemeTabsList').empty();
            $('#schemeTabsContent').empty();
            
            if(!schemes || schemes.length === 0){
                $('#schemeTabsContent').html('<div class="layui-none" style="padding: 20px;">暂无方案数据，请点击"添加方案"按钮创建</div>');
                return;
            }
            
            // 处理方案数据
            $.each(schemes, function(i, scheme){
                // 添加状态文本
                scheme.statusText = scheme.status === 'draft' ? '草稿' : '已保存';
                
                // 分组参数
                scheme.paramGroups = organizeParamsByGroup(scheme.params);
                
                // 添加选项卡标题
                var tabTitle = '<li'+ (i === 0 ? ' class="layui-this"' : '') +'>' + scheme.schemeName + '</li>';
                $('#schemeTabsList').append(tabTitle);
                
                // 添加选项卡内容
                var tabContent = '<div class="layui-tab-item' + (i === 0 ? ' layui-show' : '') + '" id="schemeTab_' + scheme.id + '"></div>';
                $('#schemeTabsContent').append(tabContent);
                
                // 渲染方案内容
                laytpl($('#schemeTabTemplate').html()).render(scheme, function(html){
                    $('#schemeTab_' + scheme.id).html(html);
                });
            });
            
            // 重新初始化选项卡
            element.render('tab', 'schemeTabs');
        }
        
        // 对比方案
        function compareSchemes(){
            var baseSchemeId = $('#baseScheme').val();
            var compareSchemeId = $('#compareScheme').val();
            
            if(baseSchemeId === compareSchemeId){
                $('#comparisonResult').html('<div class="layui-none" style="padding: 20px;">请选择两个不同的方案进行对比</div>');
                return;
            }
            
            var baseScheme = getSchemeById(baseSchemeId);
            var compareScheme = getSchemeById(compareSchemeId);
            
            if(!baseScheme || !compareScheme){
                $('#comparisonResult').html('<div class="layui-none" style="padding: 20px;">方案数据加载失败</div>');
                return;
            }
            
            // 生成比较结果
            var comparisonData = generateComparisonData(baseScheme, compareScheme);
            
            // 渲染比较结果
            laytpl($('#comparisonTemplate').html()).render(comparisonData, function(html){
                $('#comparisonResult').html(html);
            });
        }
        
        // 生成方案比较数据
        function generateComparisonData(baseScheme, compareScheme){
            // 组织所有参数组
            var allGroups = {};
            var paramGroupIcons = {
                'basic': 'info-circle',
                'rubber': 'flask',
                'skeleton': 'layer-group',
                'design': 'drafting-compass',
                'test': 'vial',
                'mold': 'cube'
            };
            
            // 处理基准方案的参数
            $.each(baseScheme.params || [], function(i, param){
                if(!allGroups[param.paramGroup]){
                    allGroups[param.paramGroup] = {
                        key: param.paramGroup,
                        name: getGroupDisplayName(param.paramGroup),
                        icon: paramGroupIcons[param.paramGroup] || 'cog',
                        params: {}
                    };
                }
                
                allGroups[param.paramGroup].params[param.paramName] = {
                    paramName: param.paramName,
                    baseValue: param.paramValue,
                    baseUnit: param.paramUnit,
                    compareValue: null,
                    compareUnit: null,
                    isDiff: true
                };
            });
            
            // 处理比较方案的参数
            $.each(compareScheme.params || [], function(i, param){
                if(!allGroups[param.paramGroup]){
                    allGroups[param.paramGroup] = {
                        key: param.paramGroup,
                        name: getGroupDisplayName(param.paramGroup),
                        icon: paramGroupIcons[param.paramGroup] || 'cog',
                        params: {}
                    };
                }
                
                if(!allGroups[param.paramGroup].params[param.paramName]){
                    // 基准方案没有此参数
                    allGroups[param.paramGroup].params[param.paramName] = {
                        paramName: param.paramName,
                        baseValue: null,
                        baseUnit: null,
                        compareValue: param.paramValue,
                        compareUnit: param.paramUnit,
                        isDiff: true
                    };
                } else {
                    // 基准方案有此参数，比较值
                    var baseParam = allGroups[param.paramGroup].params[param.paramName];
                    baseParam.compareValue = param.paramValue;
                    baseParam.compareUnit = param.paramUnit;
                    baseParam.isDiff = baseParam.baseValue !== param.paramValue;
                }
            });
            
            // 将参数对象转换为数组
            var groups = [];
            $.each(allGroups, function(groupKey, group){
                var params = [];
                $.each(group.params, function(paramName, param){
                    params.push(param);
                });
                
                group.params = params;
                groups.push(group);
            });
            
            // 返回比较结果
            return {
                baseScheme: baseScheme,
                compareScheme: compareScheme,
                groups: groups
            };
        }
        
        // 根据分组名显示中文名称
        function getGroupDisplayName(groupKey){
            var groupNames = {
                'basic': '基本参数',
                'rubber': '胶料参数',
                'skeleton': '骨架材料',
                'design': '设计参数',
                'test': '检测数据',
                'mold': '模具信息'
            };
            return groupNames[groupKey] || groupKey;
        }
        
        // 根据方案ID获取方案数据
        function getSchemeById(id){
            var schemes = window.schemeData || [];
            for(var i = 0; i < schemes.length; i++){
                if(schemes[i].id == id){
                    return schemes[i];
                }
            }
            return null;
        }
        
        // 将参数按组织归类
        function organizeParamsByGroup(params){
            var groups = [];
            var groupMap = {};
            
            // 预定义组顺序和图标
            var predefinedGroups = [
                {key: 'basic', name: '基本参数', icon: 'info-circle'},
                {key: 'rubber', name: '胶料参数', icon: 'flask'},
                {key: 'skeleton', name: '骨架材料', icon: 'layer-group'},
                {key: 'design', name: '设计参数', icon: 'drafting-compass'},
                {key: 'test', name: '检测数据', icon: 'vial'},
                {key: 'mold', name: '模具信息', icon: 'cube'}
            ];
            
            // 先创建预定义的组
            $.each(predefinedGroups, function(i, group){
                groupMap[group.key] = {
                    key: group.key,
                    name: group.name,
                    icon: group.icon,
                    params: []
                };
                groups.push(groupMap[group.key]);
            });
            
            // 归类参数
            $.each(params || [], function(i, param){
                var groupKey = param.paramGroup || 'basic';
                
                // 如果组不存在，创建一个新组
                if(!groupMap[groupKey]){
                    groupMap[groupKey] = {
                        key: groupKey,
                        name: getGroupDisplayName(groupKey),
                        icon: 'cog',
                        params: []
                    };
                    groups.push(groupMap[groupKey]);
                }
                
                groupMap[groupKey].params.push(param);
            });
            
            // 过滤掉没有参数的组
            return groups.filter(function(group){
                return group.params.length > 0;
            });
        }
        
        // 从URL获取项目ID
        function getProjectIdFromUrl(){
            var url = window.location.pathname;
            var parts = url.split('/');
            return parts[parts.length - 1];
        }
        
        // 表单初始化
        form.render();
    });
    </script>
</body>
</html> 