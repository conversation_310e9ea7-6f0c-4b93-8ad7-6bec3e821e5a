package com.oracle.SpringBoot.entity;

import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ETO方案参数实体类 - 对应新表的方案参数，关联到EtoProjectScheme
 */
@Data
@Table(name = "eto_scheme_param")
public class EtoSchemeParam {
    @Id
    private Long id;
    private Long schemeId;         // 关联项目方案ID (EtoProjectScheme.id)
    private String paramGroup;     // 参数分组(模具/基本/胶料/骨架/设计/检测)
    private String paramName;      // 参数名称
    private String paramValue;     // 参数值
    private String paramUnit;      // 参数单位
} 