package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 冠带层部件实体类
 */
@Data
@TableName("PartCapPly")
public class PartCapPly {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 冠带层材料
     */
    @TableField("CapPlyMaterial")
    private String capPlyMaterial;
    
    /**
     * 密度
     */
    @TableField("Density")
    private String density;
    
    /**
     * 冠带层胶料
     */
    @TableField("CapPlyCompound")
    private String capPlyCompound;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
