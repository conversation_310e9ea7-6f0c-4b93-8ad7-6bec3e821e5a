package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoBasicinfo;
import com.oracle.SpringBoot.mapper.EtoBasicinfoMapper;
import com.oracle.SpringBoot.service.IEtoBasicinfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO基础信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoBasicinfoServiceImpl extends ServiceImpl<EtoBasicinfoMapper, EtoBasicinfo> implements IEtoBasicinfoService {

    @Override
    public EtoBasicinfo getByEtoId(Integer etoId) {
        return this.lambdaQuery()
                .eq(EtoBasicinfo::getEtoId, etoId)
                .one();
    }
}
