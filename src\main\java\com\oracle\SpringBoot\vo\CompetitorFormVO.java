package com.oracle.SpringBoot.vo;

import lombok.Data;

import java.util.List;

/**
 * 竞品表单数据VO，用于接收前端提交的竞品数据
 */
@Data
public class CompetitorFormVO {
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 竞品名称
     */
    private String competitorName;
    
    /**
     * 参数值列表
     */
    private List<ParamValueVO> paramValues;
    
    @Data
    public static class ParamValueVO {
        /**
         * 参数ID
         */
        private Long paramId;
        
        /**
         * 参数值
         */
        private String value;
    }
} 