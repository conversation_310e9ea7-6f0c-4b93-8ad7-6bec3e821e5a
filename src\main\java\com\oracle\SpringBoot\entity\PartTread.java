package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 胎面部件实体类
 */
@Data
@TableName("PartTread")
public class PartTread {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 工艺尺寸图
     */
    @TableField("ProcessSizeChart")
    private String processSizeChart;
    
    /**
     * 中心厚度
     */
    @TableField("CenterThickness")
    private BigDecimal centerThickness;
    
    /**
     * 胎面胶料
     */
    @TableField("TreadCompound")
    private String treadCompound;
    
    /**
     * 基部胶料
     */
    @TableField("BaseCompound")
    private String baseCompound;
    
    /**
     * 下胎面胶料
     */
    @TableField("LowerTreadCompound")
    private String lowerTreadCompound;
    
    /**
     * 结构尺寸图
     */
    @TableField("StructureSizeChart")
    private String structureSizeChart;
    
    /**
     * 肩部厚度
     */
    @TableField("ShoulderThickness")
    private BigDecimal shoulderThickness;
    
    /**
     * 对称类型
     */
    @TableField("SymmetryType")
    private String symmetryType;
    
    /**
     * 翼胶料
     */
    @TableField("WingCompound")
    private String wingCompound;
    
    /**
     * 导电胶料
     */
    @TableField("ConductiveCompound")
    private String conductiveCompound;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
