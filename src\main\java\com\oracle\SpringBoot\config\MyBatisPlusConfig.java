package com.oracle.SpringBoot.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Configuration
public class MyBatisPlusConfig {

    /**
     * 配置分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 添加分页插件，并指定数据库类型为SQL Server
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.SQL_SERVER);
        // 配置分页插件
        paginationInnerInterceptor.setOptimizeJoin(false); // 禁用SQL优化
        paginationInnerInterceptor.setOverflow(true); // 溢出总页数后是否进行处理
        paginationInnerInterceptor.setMaxLimit(500L); // 单页限制数量
        
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }
} 