package com.oracle.SpringBoot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oracle.SpringBoot.entity.Users;
import com.oracle.SpringBoot.mapper.UsersMapper;
import com.oracle.SpringBoot.service.IUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Service
public class UsersServiceImpl extends ServiceImpl<UsersMapper, Users> implements IUsersService {

    @Autowired
    private UsersMapper usersMapper;
    public boolean validateUsers(String username, String password) {
        QueryWrapper<Users> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username).eq("password", password);
        Users users = usersMapper.selectOne(wrapper);
        if (users != null) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean validateStatus(String username, String password) {
        QueryWrapper<Users> wrapper = new QueryWrapper<>();
        wrapper.eq("username", username).eq("password", password);
        Users users = usersMapper.selectOne(wrapper);
        if (users != null && users.getStatus() == 1) {
            return true;
        } else {
            return false;
        }
    }

    @SuppressWarnings("null")
    @Override
    public boolean Verificationlevel(String username) {
        Users user = usersMapper.selectOne(new QueryWrapper<Users>().eq("username", username));
        if (user != null && user.getLevel() == 0 || user.getLevel() == 1) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean Verificationlevel2(String username) {
        Users user = usersMapper.selectOne(new QueryWrapper<Users>().eq("username", username));
        if (user != null && user.getLevel() == 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean updatePassword(String username, String password, String password1) {
        Users user = usersMapper.findByUsername(username);
        if (user != null && user.getPassword().equals(password)) {
            user.setPassword(password1);
            usersMapper.updateById(user);
            return true;
        }
        return false;
    }
}
