-- 插入测试半部件数据
USE [ETO]
GO

-- 插入胎面部件测试数据
INSERT INTO [dbo].[PartTread] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [ProcessSizeChart], [CenterThickness], [TreadCompound], [BaseCompound], [LowerTreadCompound], [StructureSizeChart], [ShoulderThickness], [SymmetryType], [WingCompound], [ConductiveCompound], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准胎面_PCR_01', 'TREAD001', 'V1.0', '标准PCR胎面部件', 'PCR_PROC_01', 12.5, 'TR_COMP_001', 'BASE_001', 'LOWER_001', 'PCR_STRUCT_01', 8.5, '对称', 'WING_001', 'COND_001', 'system', GETDATE(), 1),
('高性能胎面_PCR_01', 'TREAD002', 'V1.0', '高性能PCR胎面部件', 'PCR_PROC_02', 13.0, 'TR_COMP_002', 'BASE_002', 'LOWER_002', 'PCR_STRUCT_02', 9.0, '对称', 'WING_002', 'COND_002', 'system', GETDATE(), 1),
('SUV胎面_01', 'TREAD003', 'V1.0', 'SUV专用胎面部件', 'SUV_PROC_01', 14.0, 'TR_COMP_003', 'BASE_003', 'LOWER_003', 'SUV_STRUCT_01', 10.0, '对称', 'WING_003', 'COND_003', 'system', GETDATE(), 1),
('冬季胎面_01', 'TREAD004', 'V1.0', '冬季轮胎胎面部件', 'WINTER_PROC_01', 11.5, 'TR_COMP_004', 'BASE_004', 'LOWER_004', 'WINTER_STRUCT_01', 8.0, '对称', 'WING_004', 'COND_004', 'system', GETDATE(), 1);

-- 插入胎侧部件测试数据
INSERT INTO [dbo].[PartSidewall] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [ProcessSizeChart], [Width], [WearResistantCompound], [SidewallCompound], [StructureSizeChart], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准胎侧_PCR_01', 'SIDE001', 'V1.0', '标准PCR胎侧部件', 'PCR_SIDE_PROC_01', 85.0, 'WEAR_COMP_001', 'SIDE_COMP_001', 'PCR_SIDE_STRUCT_01', 'system', GETDATE(), 1),
('加强胎侧_PCR_01', 'SIDE002', 'V1.0', '加强型PCR胎侧部件', 'PCR_SIDE_PROC_02', 90.0, 'WEAR_COMP_002', 'SIDE_COMP_002', 'PCR_SIDE_STRUCT_02', 'system', GETDATE(), 1),
('SUV胎侧_01', 'SIDE003', 'V1.0', 'SUV专用胎侧部件', 'SUV_SIDE_PROC_01', 95.0, 'WEAR_COMP_003', 'SIDE_COMP_003', 'SUV_SIDE_STRUCT_01', 'system', GETDATE(), 1),
('运动胎侧_01', 'SIDE004', 'V1.0', '运动型胎侧部件', 'SPORT_SIDE_PROC_01', 88.0, 'WEAR_COMP_004', 'SIDE_COMP_004', 'SPORT_SIDE_STRUCT_01', 'system', GETDATE(), 1);

-- 插入胎体部件测试数据
INSERT INTO [dbo].[PartCarcass] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [Width], [Angle], [Density], [CarcassMaterial], [CarcassCompound], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准胎体_PCR_01', 'CARC001', 'V1.0', '标准PCR胎体部件', 180.0, 90.0, '1440', 'RAYON_1440', 'CARC_COMP_001', 'system', GETDATE(), 1),
('高强胎体_PCR_01', 'CARC002', 'V1.0', '高强度PCR胎体部件', 185.0, 90.0, '1670', 'RAYON_1670', 'CARC_COMP_002', 'system', GETDATE(), 1),
('SUV胎体_01', 'CARC003', 'V1.0', 'SUV专用胎体部件', 200.0, 90.0, '1840', 'RAYON_1840', 'CARC_COMP_003', 'system', GETDATE(), 1),
('轻量胎体_01', 'CARC004', 'V1.0', '轻量化胎体部件', 175.0, 90.0, '1330', 'RAYON_1330', 'CARC_COMP_004', 'system', GETDATE(), 1);

-- 插入内衬层部件测试数据
INSERT INTO [dbo].[PartInnerLiner] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [AirTightLayerWidth], [TransitionLayerWidth], [AirTightLayerThickness], [TransitionLayerThickness], [TransitionLayerCompound], [AirTightLayerCompound], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准内衬_PCR_01', 'INNER001', 'V1.0', '标准PCR内衬层部件', 150.0, 160.0, 1.2, 0.8, 'TRANS_COMP_001', 'AIR_COMP_001', 'system', GETDATE(), 1),
('增强内衬_PCR_01', 'INNER002', 'V1.0', '增强型PCR内衬层部件', 155.0, 165.0, 1.4, 1.0, 'TRANS_COMP_002', 'AIR_COMP_002', 'system', GETDATE(), 1),
('SUV内衬_01', 'INNER003', 'V1.0', 'SUV专用内衬层部件', 170.0, 180.0, 1.5, 1.2, 'TRANS_COMP_003', 'AIR_COMP_003', 'system', GETDATE(), 1);

-- 插入冠带层部件测试数据
INSERT INTO [dbo].[PartCapPly] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [CapPlyMaterial], [Density], [CapPlyCompound], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准冠带_PCR_01', 'CAP001', 'V1.0', '标准PCR冠带层部件', 'NYLON_1400', '1400', 'CAP_COMP_001', 'system', GETDATE(), 1),
('高强冠带_PCR_01', 'CAP002', 'V1.0', '高强度PCR冠带层部件', 'NYLON_1670', '1670', 'CAP_COMP_002', 'system', GETDATE(), 1),
('SUV冠带_01', 'CAP003', 'V1.0', 'SUV专用冠带层部件', 'NYLON_1840', '1840', 'CAP_COMP_003', 'system', GETDATE(), 1);

-- 插入带束层部件测试数据
INSERT INTO [dbo].[PartBeltLayer] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [BeltLayerType], [EdgeRubberWidth], [SteelMaterial], [EdgeRubberThickness], [CutAngle], [RubberCompound], [Density], [SteelThickness], [SteelCoatingCompound], [BeltWidth], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准带束_1#_PCR_01', 'BELT001', 'V1.0', '标准PCR带束层1#部件', 1, 5.0, 'STEEL_2+2x0.25', 1.5, 22.0, 'BELT_COMP_001', '2+2x0.25', 0.25, 'STEEL_COAT_001', 140.0, 'system', GETDATE(), 1),
('标准带束_2#_PCR_01', 'BELT002', 'V1.0', '标准PCR带束层2#部件', 2, 5.0, 'STEEL_2+2x0.25', 1.5, -22.0, 'BELT_COMP_001', '2+2x0.25', 0.25, 'STEEL_COAT_001', 120.0, 'system', GETDATE(), 1),
('高强带束_1#_PCR_01', 'BELT003', 'V1.0', '高强度PCR带束层1#部件', 1, 6.0, 'STEEL_3+3x0.25', 1.8, 24.0, 'BELT_COMP_002', '3+3x0.25', 0.25, 'STEEL_COAT_002', 145.0, 'system', GETDATE(), 1),
('高强带束_2#_PCR_01', 'BELT004', 'V1.0', '高强度PCR带束层2#部件', 2, 6.0, 'STEEL_3+3x0.25', 1.8, -24.0, 'BELT_COMP_002', '3+3x0.25', 0.25, 'STEEL_COAT_002', 125.0, 'system', GETDATE(), 1);

-- 插入钢丝圈部件测试数据
INSERT INTO [dbo].[PartBeadWire] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [CompoundFormula], [ArrangementMethod], [BeadWireInnerDiameter], [WireDiameter], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准钢丝圈_15寸', 'BEAD001', 'V1.0', '15寸标准钢丝圈部件', 'BEAD_FORM_001', '六角排列', 381.0, 1.0, 'system', GETDATE(), 1),
('标准钢丝圈_16寸', 'BEAD002', 'V1.0', '16寸标准钢丝圈部件', 'BEAD_FORM_001', '六角排列', 406.4, 1.0, 'system', GETDATE(), 1),
('标准钢丝圈_17寸', 'BEAD003', 'V1.0', '17寸标准钢丝圈部件', 'BEAD_FORM_001', '六角排列', 431.8, 1.0, 'system', GETDATE(), 1),
('高强钢丝圈_18寸', 'BEAD004', 'V1.0', '18寸高强度钢丝圈部件', 'BEAD_FORM_002', '六角排列', 457.2, 1.2, 'system', GETDATE(), 1);

-- 插入三角胶部件测试数据
INSERT INTO [dbo].[PartApex] 
([PART_NAME], [PART_SAPCODE], [PART_VERSION], [PART_DESC], [ProcessSizeChart], [HardCoreCompound], [StructureSizeChart], [Height], [CREATOR], [CREATE_TIME], [FLAG])
VALUES 
('标准三角胶_PCR_01', 'APEX001', 'V1.0', '标准PCR三角胶部件', 'PCR_APEX_PROC_01', 'HARD_COMP_001', 'PCR_APEX_STRUCT_01', 15.0, 'system', GETDATE(), 1),
('硬质三角胶_PCR_01', 'APEX002', 'V1.0', '硬质PCR三角胶部件', 'PCR_APEX_PROC_02', 'HARD_COMP_002', 'PCR_APEX_STRUCT_02', 18.0, 'system', GETDATE(), 1),
('SUV三角胶_01', 'APEX003', 'V1.0', 'SUV专用三角胶部件', 'SUV_APEX_PROC_01', 'HARD_COMP_003', 'SUV_APEX_STRUCT_01', 20.0, 'system', GETDATE(), 1),
('软质三角胶_01', 'APEX004', 'V1.0', '软质三角胶部件', 'SOFT_APEX_PROC_01', 'SOFT_COMP_001', 'SOFT_APEX_STRUCT_01', 12.0, 'system', GETDATE(), 1);

PRINT '测试半部件数据插入完成！'
GO
