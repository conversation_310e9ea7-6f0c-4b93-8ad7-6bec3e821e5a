-- ETO概念参数表
CREATE TABLE eto_concept_param (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    project_id BIGINT NOT NULL,
    param_name NVARCHAR(100) NOT NULL,
    create_time DATETIME NULL,
    update_time DATETIME NULL,
    create_by NVARCHAR(50) NULL,
    update_by NVARCHAR(50) NULL
);

-- 创建索引
CREATE INDEX idx_param_project ON eto_concept_param (project_id);

-- ETO概念目标值表
CREATE TABLE eto_concept_target (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    project_id BIGINT NOT NULL,
    param_id BIGINT NOT NULL,
    target_value NVARCHAR(255) NULL,
    create_time DATETIME NULL,
    update_time DATETIME NULL,
    create_by NVARCHAR(50) NULL,
    update_by NVARCHAR(50) NULL
);

-- 创建唯一约束
ALTER TABLE eto_concept_target
ADD CONSTRAINT uk_project_param UNIQUE (project_id, param_id);

-- 创建索引
CREATE INDEX idx_target_param ON eto_concept_target (param_id);

-- ETO概念竞品表
CREATE TABLE eto_concept_competitor (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    project_id BIGINT NOT NULL,
    competitor_name NVARCHAR(100) NOT NULL,
    create_time DATETIME NULL,
    update_time DATETIME NULL,
    create_by NVARCHAR(50) NULL,
    update_by NVARCHAR(50) NULL
);

-- 创建索引
CREATE INDEX idx_competitor_project ON eto_concept_competitor (project_id);

-- ETO概念竞品参数值表
CREATE TABLE eto_concept_competitor_value (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    competitor_id BIGINT NOT NULL,
    param_id BIGINT NOT NULL,
    param_value NVARCHAR(255) NULL,
    create_time DATETIME NULL,
    update_time DATETIME NULL,
    create_by NVARCHAR(50) NULL,
    update_by NVARCHAR(50) NULL
);

-- 创建唯一约束
ALTER TABLE eto_concept_competitor_value
ADD CONSTRAINT uk_competitor_param UNIQUE (competitor_id, param_id);

-- 创建索引
CREATE INDEX idx_cv_param ON eto_concept_competitor_value (param_id); 