package com.oracle.SpringBoot.common;

import lombok.Data;
import java.io.Serializable;

/**
 * 通用返回对象
 */
@Data
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 返回码
     */
    private Integer code;

    /**
     * 返回消息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 成功返回结果
     */
    public static <T> R<T> success() {
        return success(null);
    }

    /**
     * 成功返回结果
     *
     * @param data 返回数据
     */
    public static <T> R<T> success(T data) {
        return success(data, "操作成功");
    }

    /**
     * 成功返回结果
     *
     * @param data 返回数据
     * @param msg  返回消息
     */
    public static <T> R<T> success(T data, String msg) {
        R<T> result = new R<>();
        result.setCode(0);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    /**
     * 失败返回结果
     */
    public static <T> R<T> error() {
        return error("操作失败");
    }

    /**
     * 失败返回结果
     *
     * @param msg 返回消息
     */
    public static <T> R<T> error(String msg) {
        return error(-1, msg);
    }

    /**
     * 失败返回结果
     *
     * @param code 返回码
     * @param msg  返回消息
     */
    public static <T> R<T> error(Integer code, String msg) {
        R<T> result = new R<>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
} 