<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDBIP - TireDesign BuildInno Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .layout-header {
            background: #fff;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            height: 60px;
            position: fixed;
            top: 0;
            left: 220px;
            right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            transition: all 0.3s ease;
        }

        .layout-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 220px;
            background: #1e1e2d;
            box-shadow: 2px 0 8px rgba(0,0,0,0.15);
            z-index: 1001;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar-logo {
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            background: rgba(255,255,255,0.05);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-logo img {
            height: 35px;
            width: 35px;
            margin-right: 12px;
        }

        .sidebar-logo span {
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 1px;
        }

        .menu-item {
            padding: 0;
            margin: 4px 0;
        }

        .menu-title {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.7);                                                                                               
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 4px;
            margin: 0 8px;
        }

        .menu-title:hover {
            color: #fff;
            background: rgba(255,255,255,0.1);
        }

        .menu-title i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
        }

        .submenu {
            background: rgba(0,0,0,0.2);
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
            max-height: 0;
            margin: 0 8px;
            border-radius: 4px;
        }

        .submenu.active {
            max-height: 1000px;
            margin-top: 4px;
            margin-bottom: 4px;
        }

        .submenu a {
            display: block;
            padding: 10px 20px 10px 48px;
            color: rgba(255,255,255,0.7);
            font-size: 14px;
            text-decoration: none;
            transition: all 0.3s;
            position: relative;
        }

        .submenu a:hover {
            color: #fff;
            background: rgba(255,255,255,0.1);
        }

        .submenu a::before {
            content: "";
            position: absolute;
            left: 30px;
            top: 50%;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.5);
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .welcome-content {
            background: #fff;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .welcome-title {
            font-size: 28px;
            color: #1e1e2d;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            padding: 15px 0;
            color: #666;
            font-size: 12px;
            background: transparent;
            border-top: 1px solid #eee;
        }

        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 24px;
            transition: all 0.3s;
        }

        .user-info:hover {
            background: #f5f7fa;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid #fff;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .user-name {
            color: #1e1e2d;
            font-weight: 500;
            margin-right: 8px;
        }

        .dropdown-menu {
            position: absolute;
            top: calc(100% + 8px);
            right: 0;
            background: #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 8px 0;
            display: none;
            min-width: 160px;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-menu a {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            color: #1e1e2d;
            text-decoration: none;
            transition: all 0.3s;
        }

        .dropdown-menu a i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
            color: #666;
        }

        .dropdown-menu a:hover {
            background: #f5f7fa;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="layout-sidebar">
        <div class="sidebar-logo">
            <img src="./images/26.png" alt="Logo">
            <span>TDBIP</span>
        </div>
        <nav>
            <div class="menu-item">
                <div class="menu-title">
                    <i class="fas fa-file-alt"></i>
                    <span>表单管理</span>
                </div>
                <div class="submenu">
                    <a href="create-eto-project">创建项目</a>
                    <a href="eto-projects">项目列表</a>
                    <a href="create-eto">创建表单</a>
                    <a href="my-eto">我的表单</a>
                    <a href="draft-eto">草稿箱</a>
                </div>
            </div>

            <div class="menu-item">
                <div class="menu-title">
                    <i class="fas fa-tasks"></i>
                    <span>方案管理</span>
                </div>
                <div class="submenu">
                    <a href="scheme-list">方案列表</a>
                    <a href="scheme-compare">方案对比</a>
                    <a href="scheme-analysis">方案分析</a>
                </div>
            </div>

            <#if Level_1>
            <div class="menu-item">
                <div class="menu-title">
                    <i class="fas fa-check-circle"></i>
                    <span>审核管理</span>
                </div>
                <div class="submenu">
                    <a href="pending-review">待审核方案</a>
                    <a href="reviewed">已审核方案</a>
                    <a href="review-statistics">审核统计</a>
                </div>
            </div>
            </#if>

            <#if Level_2>
            <div class="menu-item">
                <div class="menu-title">
                    <i class="fas fa-cog"></i>
                    <span>系统管理</span>
                </div>
                <div class="submenu">
                    <a href="user-management">用户管理</a>
                    <a href="role-management">角色权限</a>
                    <a href="system-settings">系统设置</a>
                    <a href="log">操作日志</a>
                </div>
            </div>
            </#if>
        </nav>
    </div>

    <!-- 顶部导航 -->
    <header class="layout-header">
        <div class="header-left">
            <i class="fas fa-bars" id="sidebar-toggle" style="cursor: pointer; font-size: 20px; color: #666;"></i>
        </div>
        <div class="user-info">
            <img src="./images/people1.png" alt="用户头像" class="user-avatar">
            <span class="user-name">管理员</span>
            <i class="fas fa-chevron-down" style="color: #666; font-size: 12px;"></i>
            <div class="dropdown-menu">
                <a href="profile"><i class="fas fa-user"></i>个人信息</a>
                <a href="settings"><i class="fas fa-cog"></i>账号设置</a>
                <a href="quit"><i class="fas fa-sign-out-alt"></i>退出登录</a>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="welcome-content">
            <h1 class="welcome-title">欢迎使用 TDBIP</h1>
            <p class="welcome-subtitle">TireDesign BuildInno Platform - 在这里，您可以便捷地创建和管理表单，进行方案设计与对比，完成审核流程，实现无纸化办公。</p>
        </div>
        <footer class="footer">
            Copyright © 山东玲珑轮胎股份有限公司 | Ver 2.0.0
        </footer>
    </main>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['layer'], function(){
        var layer = layui.layer;
        var $ = layui.$;

        // 菜单展开/收起
        $('.menu-title').on('click', function(e){
            e.preventDefault();
            var submenu = $(this).next('.submenu');
            $('.submenu').not(submenu).removeClass('active');                   
            submenu.toggleClass('active');
        });

        // 用户下拉菜单
        $('.user-info').on('click', function(e){
            e.stopPropagation();
            $('.dropdown-menu').toggleClass('show');
        });

        // 点击其他地方关闭下拉菜单
        $(document).on('click', function(){
            $('.dropdown-menu').removeClass('show');
        });

        // 侧边栏切换 
        var sidebarCollapsed = false;
        $('#sidebar-toggle').on('click', function(){
            if(sidebarCollapsed) {
                $('.layout-sidebar').css('width', '220px');
                $('.layout-header, .main-content').css('margin-left', '220px');
                $('.sidebar-logo span').fadeIn();
                $('.menu-title span').fadeIn();
                sidebarCollapsed = false;
            } else {
                $('.layout-sidebar').css('width', '60px');
                $('.layout-header, .main-content').css('margin-left', '60px');
                $('.sidebar-logo span').fadeOut();
                $('.menu-title span').fadeOut();
                sidebarCollapsed = true;
            }
        });

        // 初始化激活状态
        var path = window.location.pathname;
        $('.submenu a').each(function(){
            if($(this).attr('href') === path.split('/').pop()){
                $(this).css({
                    'background': 'rgba(255,255,255,0.1)',
                    'color': '#fff'
                });
                $(this).parent('.submenu').addClass('active');
            }
        });
    });
    </script>
</body>
</html>