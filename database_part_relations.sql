-- ETO项目与半部件关联表
USE [ETO]
GO

-- 1. ETO项目半部件配置表
CREATE TABLE [dbo].[EtoProjectPartConfig](
    [ID] [bigint] IDENTITY(1,1) NOT NULL,
    [PROJECT_ID] [bigint] NOT NULL,                    -- 关联ETO项目ID
    [SCHEME_ID] [bigint] NULL,                         -- 关联方案ID（可选）
    [PART_TYPE] [varchar](20) NOT NULL,                -- 部件类型：tread/sidewall/carcass等
    [PART_NAME] [varchar](50) NOT NULL,                -- 部件名称
    [PART_SAPCODE] [varchar](20) NULL,                 -- SAP代码
    [QUANTITY] [int] DEFAULT 1,                        -- 使用数量
    [POSITION] [varchar](20) NULL,                     -- 位置信息（如：左/右/内/外）
    [REMARKS] [nvarchar](200) NULL,                    -- 备注
    [CREATE_TIME] [datetime] DEFAULT GETDATE(),        -- 创建时间
    [UPDATE_TIME] [datetime] DEFAULT GETDATE(),        -- 更新时间
    [CREATE_BY] [varchar](50) NULL,                    -- 创建人
    [UPDATE_BY] [varchar](50) NULL,                    -- 更新人
    [STATUS] [varchar](10) DEFAULT 'ACTIVE',           -- 状态：ACTIVE/INACTIVE
PRIMARY KEY CLUSTERED 
(
    [ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- 添加外键约束
ALTER TABLE [dbo].[EtoProjectPartConfig]
ADD CONSTRAINT [FK_EtoProjectPartConfig_Project] 
FOREIGN KEY([PROJECT_ID]) REFERENCES [dbo].[eto_project]([id])
ON DELETE CASCADE
GO

-- 2. 半部件模板表（预定义的部件组合）
CREATE TABLE [dbo].[PartTemplate](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [TEMPLATE_NAME] [varchar](50) NOT NULL,            -- 模板名称
    [TEMPLATE_DESC] [nvarchar](200) NULL,              -- 模板描述
    [TIRE_TYPE] [varchar](20) NULL,                    -- 适用轮胎类型：PCR/TBR/SUV等
    [SIZE_RANGE] [varchar](50) NULL,                   -- 适用尺寸范围
    [IS_DEFAULT] [bit] DEFAULT 0,                      -- 是否默认模板
    [CREATE_TIME] [datetime] DEFAULT GETDATE(),        -- 创建时间
    [UPDATE_TIME] [datetime] DEFAULT GETDATE(),        -- 更新时间
    [CREATE_BY] [varchar](50) NULL,                    -- 创建人
    [UPDATE_BY] [varchar](50) NULL,                    -- 更新人
    [STATUS] [varchar](10) DEFAULT 'ACTIVE',           -- 状态
PRIMARY KEY CLUSTERED 
(
    [ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- 3. 模板部件配置表
CREATE TABLE [dbo].[PartTemplateConfig](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [TEMPLATE_ID] [int] NOT NULL,                      -- 关联模板ID
    [PART_TYPE] [varchar](20) NOT NULL,                -- 部件类型
    [PART_NAME] [varchar](50) NOT NULL,                -- 部件名称
    [IS_REQUIRED] [bit] DEFAULT 1,                     -- 是否必需
    [DEFAULT_QUANTITY] [int] DEFAULT 1,                -- 默认数量
    [SORT_ORDER] [int] DEFAULT 0,                      -- 排序顺序
    [REMARKS] [nvarchar](200) NULL,                    -- 备注
PRIMARY KEY CLUSTERED 
(
    [ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- 添加外键约束
ALTER TABLE [dbo].[PartTemplateConfig]
ADD CONSTRAINT [FK_PartTemplateConfig_Template] 
FOREIGN KEY([TEMPLATE_ID]) REFERENCES [dbo].[PartTemplate]([ID])
ON DELETE CASCADE
GO

-- 4. 部件兼容性表（定义哪些部件可以组合使用）
CREATE TABLE [dbo].[PartCompatibility](
    [ID] [int] IDENTITY(1,1) NOT NULL,
    [PART_TYPE_1] [varchar](20) NOT NULL,              -- 第一个部件类型
    [PART_NAME_1] [varchar](50) NOT NULL,              -- 第一个部件名称
    [PART_TYPE_2] [varchar](20) NOT NULL,              -- 第二个部件类型
    [PART_NAME_2] [varchar](50) NOT NULL,              -- 第二个部件名称
    [COMPATIBILITY_LEVEL] [varchar](10) NOT NULL,      -- 兼容性级别：HIGH/MEDIUM/LOW
    [REMARKS] [nvarchar](200) NULL,                    -- 备注说明
    [CREATE_TIME] [datetime] DEFAULT GETDATE(),        -- 创建时间
    [CREATE_BY] [varchar](50) NULL,                    -- 创建人
PRIMARY KEY CLUSTERED 
(
    [ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- 5. 创建索引以提高查询性能
CREATE INDEX [IX_EtoProjectPartConfig_ProjectId] ON [dbo].[EtoProjectPartConfig] ([PROJECT_ID])
GO

CREATE INDEX [IX_EtoProjectPartConfig_PartType] ON [dbo].[EtoProjectPartConfig] ([PART_TYPE])
GO

CREATE INDEX [IX_PartTemplateConfig_TemplateId] ON [dbo].[PartTemplateConfig] ([TEMPLATE_ID])
GO

-- 6. 插入一些示例模板数据
INSERT INTO [dbo].[PartTemplate] ([TEMPLATE_NAME], [TEMPLATE_DESC], [TIRE_TYPE], [SIZE_RANGE], [IS_DEFAULT], [CREATE_BY])
VALUES 
('标准PCR模板', '标准乘用车轮胎部件配置', 'PCR', '13-18寸', 1, 'system'),
('SUV标准模板', 'SUV轮胎标准部件配置', 'SUV', '16-22寸', 0, 'system'),
('高性能模板', '高性能轮胎部件配置', 'PCR', '17-20寸', 0, 'system');

-- 插入标准PCR模板的部件配置
DECLARE @templateId INT = (SELECT ID FROM [dbo].[PartTemplate] WHERE [TEMPLATE_NAME] = '标准PCR模板');

INSERT INTO [dbo].[PartTemplateConfig] ([TEMPLATE_ID], [PART_TYPE], [PART_NAME], [IS_REQUIRED], [DEFAULT_QUANTITY], [SORT_ORDER])
VALUES 
(@templateId, 'tread', '标准胎面', 1, 1, 1),
(@templateId, 'sidewall', '标准胎侧', 1, 2, 2),
(@templateId, 'carcass', '标准胎体', 1, 1, 3),
(@templateId, 'innerliner', '标准内衬', 1, 1, 4),
(@templateId, 'capply', '标准冠带', 1, 1, 5),
(@templateId, 'beltlayer', '标准带束', 1, 2, 6),
(@templateId, 'beadwire', '标准钢丝圈', 1, 2, 7),
(@templateId, 'apex', '标准三角胶', 1, 2, 8);

-- 7. 创建视图以便于查询
CREATE VIEW [dbo].[V_ProjectPartSummary] AS
SELECT 
    p.id as ProjectId,
    p.project_name as ProjectName,
    p.size as TireSize,
    pc.PART_TYPE as PartType,
    pc.PART_NAME as PartName,
    pc.PART_SAPCODE as SapCode,
    pc.QUANTITY as Quantity,
    pc.POSITION as Position,
    pc.STATUS as Status
FROM [dbo].[eto_project] p
LEFT JOIN [dbo].[EtoProjectPartConfig] pc ON p.id = pc.PROJECT_ID
WHERE pc.STATUS = 'ACTIVE'
GO
