<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oracle.SpringBoot.mapper.UsersMapper">

    <select id="findByUsername" parameterType="java.lang.String" resultType="com.oracle.SpringBoot.entity.Users">
        SELECT * FROM Users WHERE username = #{username}
    </select>

</mapper>
