package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartCarcass;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 胎体部件Mapper接口
 */
@Mapper
public interface PartCarcassMapper extends BaseMapper<PartCarcass> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartCarcass WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartCarcass selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartCarcass WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartCarcass> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的胎体部件
     */
    @Select("SELECT * FROM PartCarcass WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartCarcass> selectAllActive();
    
    /**
     * 根据胎体材料查询
     */
    @Select("SELECT * FROM PartCarcass WHERE CarcassMaterial = #{material} AND FLAG = 1")
    List<PartCarcass> selectByCarcassMaterial(String material);
    
    /**
     * 根据角度查询
     */
    @Select("SELECT * FROM PartCarcass WHERE Angle = #{angle} AND FLAG = 1")
    List<PartCarcass> selectByAngle(Double angle);
}
