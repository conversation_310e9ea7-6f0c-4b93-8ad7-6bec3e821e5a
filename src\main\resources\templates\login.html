<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - TDBIP</title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url('./images/login1.jpg') center/cover no-repeat;
            font-family: "Microsoft YaHei", sans-serif;
        }

        .login-container {
            width: 400px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header img {
            height: 60px;
            margin-bottom: 15px;
        }

        .login-header h2 {
            color: #333;
            font-size: 24px;
            margin: 0;
            font-weight: normal;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }

        .layui-input {
            height: 45px;
            line-height: 45px;
            padding-left: 45px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
            background: #fff;
            transition: all 0.3s ease;
        }

        .layui-input:focus {
            border-color: #3B82F6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .layui-btn {
            width: 100%;
            height: 45px;
            line-height: 45px;
            background-color: #3B82F6;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            transition: all 0.3s ease;
        }

        .layui-btn:hover {
            background-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            color: #666;
            font-size: 14px;
        }

        .login-options a {
            color: #3B82F6;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .login-options a:hover {
            color: #2563eb;
        }

        .error-message {
            color: #dc2626;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }

        .success-message {
            color: #059669;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .remember-me input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin: 0;
        }

        .links {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .links a {
            color: #3B82F6;
            text-decoration: none;
            margin: 0 10px;
        }

        .links a:hover {
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="./images/26.png" alt="Logo">
            <h2>TDBIP</h2>
            <p style="color: #666; font-size: 14px; margin-top: 5px;">TireDesign BuildInno Platform</p>
        </div>
        
        <form class="layui-form" method="post" action="loginInfo" enctype="application/x-www-form-urlencoded">
            <div class="input-group">
                <i class="fas fa-user"></i>
                <input type="text" name="username" required lay-verify="required|username" 
                       class="layui-input" placeholder="请输入用户名">
            </div>
            
            <div class="input-group">
                <i class="fas fa-lock"></i>
                <input type="password" name="password" required lay-verify="required|password" 
                       class="layui-input" placeholder="请输入密码">
            </div>
            
            <div class="login-options">
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">记住密码</label>
                </div>
                <a href="forgetPassword">忘记密码？</a>
            </div>
            
            <button type="submit" class="layui-btn" lay-submit lay-filter="formDemo">登 录</button>
            
            <div class="links">
                <a href="forgetPassword">密码重置</a>
                <span>|</span>
                <a href="changePassword">密码修改</a>
            </div>

            <div class="messages">
                <#if error2??>
                    <div class="error-message">${error2}</div>
                </#if>
                <#if error??>
                    <div class="error-message">${error}</div>
                </#if>
                <#if success??>
                    <div class="success-message">${success}</div>
                </#if>
            </div>
        </form>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;
            
            form.verify({
                username: function(value){
                    if(value.length < 6 || value.length > 15){
                        return '用户名长度必须为6到15位';
                    }
                },
                password: function(value){
                    if(value.length < 6 || value.length > 15){
                        return '密码长度必须为6到15位';
                    }
                }
            });

            form.on('submit(formDemo)', function(data){
                layer.load(1);
                return true;
            });
        });
    </script>
</body>
</html>