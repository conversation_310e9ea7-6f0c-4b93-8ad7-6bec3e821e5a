package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoProject;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ETO项目服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoProjectService extends IService<EtoProject> {
    
    /**
     * 根据ETO主表ID获取关联的项目
     * @param etoMainId ETO主表ID
     * @return 项目信息
     */
    EtoProject getProjectByEtoMainId(Long etoMainId);
} 