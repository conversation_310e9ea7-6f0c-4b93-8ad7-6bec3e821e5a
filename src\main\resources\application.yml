server:
  port: 8080
  servlet:
    session:
      timeout: 300m
    context-path: /boot

#mybatis:
 # mapper-locations: classpath:mapper/*.xml
  #type-aliases-package: com.oracle.SpringBoot.mapper
  #configuration:
   # map-underscore-to-camel-case: true

mybatis-plus:
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath:mapper/*.xml
  # 以下配置均有默认值,可以不设置
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增" INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断"  NOT_NULL:"非 NULL 判断")  NOT_EMPTY:"非空判断"
      #field-strategy: NOT_EMPTY
      #数据库类型
      #db-type: sqlserver
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    #call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

spring:
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: ************************************************
    username: sa
    password: 123456789.zlq
      # 下面为连接池的补充设置，应用到上面所有数据源中
      # 初始化大小，最小，最大
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evict-able-idle-time-millis: 300000
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall用于防火墙，此处是filter修改的地方
      filter:
        commons-log:
          connection-logger-name: stat,wall,log4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connect-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  #jpa:
    #open-in-view: false

#  mvc:
#    view:
#     prefix: templates/
#     suffix: .html
  #关闭thymeleaf的缓存，不然在开发过程中修改页面不会立刻生效需要重启，生产可配置为true

  #thymeleaf:
   # cache: false
    #prefix: classpath:/templates/
    #suffix: .html
    #mode: LEGACY-HTML5
    #encoding: UTF-8
  devtools:
     restart.enabled: true
     restart.additional-paths: src/main/java

#  resources: chain.strategy.content.enabled=true
#            chain.strategy.content.paths=/**
  freemarker:
    request-context-attribute: req  #req访问request
    suffix: .html  #后缀名
    content-type: text/html
    enabled: true
    cache: false #缓存配置
    template-loader-path: classpath:/templates/ #模板加载路径 按需配置
    charset: UTF-8 #编码格式
    settings:
      number_format: '0.##'   #数字格式化，无小数点

logging:
  level:
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

