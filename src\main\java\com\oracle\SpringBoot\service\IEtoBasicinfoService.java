package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoBasicinfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ETO基础信息服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoBasicinfoService extends IService<EtoBasicinfo> {
    
    /**
     * 根据ETO主表ID获取基础信息
     * @param etoId ETO主表ID
     * @return 基础信息
     */
    EtoBasicinfo getByEtoId(Integer etoId);
}
