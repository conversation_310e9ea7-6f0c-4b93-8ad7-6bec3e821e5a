package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartInnerLiner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 内衬层部件Mapper接口
 */
@Mapper
public interface PartInnerLinerMapper extends BaseMapper<PartInnerLiner> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartInnerLiner WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartInnerLiner selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartInnerLiner WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartInnerLiner> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的内衬层部件
     */
    @Select("SELECT * FROM PartInnerLiner WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartInnerLiner> selectAllActive();
    
    /**
     * 根据气密层胶料查询
     */
    @Select("SELECT * FROM PartInnerLiner WHERE AirTightLayerCompound = #{compound} AND FLAG = 1")
    List<PartInnerLiner> selectByAirTightLayerCompound(String compound);
    
    /**
     * 根据过渡层胶料查询
     */
    @Select("SELECT * FROM PartInnerLiner WHERE TransitionLayerCompound = #{compound} AND FLAG = 1")
    List<PartInnerLiner> selectByTransitionLayerCompound(String compound);
}
