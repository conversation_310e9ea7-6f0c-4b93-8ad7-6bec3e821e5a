package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Log")
public class Log implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "uid", type = IdType.AUTO)
    private Integer uid;

    private String people;

    private String behaviour;

    private LocalDateTime date;

    public static Log createLog(String username, String behavior) {
        Log log = new Log();
        log.setPeople(username);
        log.setBehaviour(behavior);
        log.setDate(LocalDateTime.now());
        return log;
    }

}
