<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>半部件选择功能测试</title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            padding: 20px;
            font-family: "Microsoft YaHei", sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .select-btn {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>半部件选择功能测试页面</h1>
    
    <div class="test-section">
        <h3>胎面部件测试</h3>
        <div class="input-group">
            <label>胎面胶料:</label>
            <input type="text" class="layui-input" id="treadCompound" placeholder="选择胎面胶料" style="width: 200px; display: inline-block;">
            <button type="button" class="layui-btn layui-btn-sm select-btn" 
                    onclick="partSelectorUtils.openPartSelector('tread', document.getElementById('treadCompound'), {
                        'treadCompound': '#treadCompound',
                        'baseCompound': '#baseCompound',
                        'centerThickness': '#centerThickness'
                    })">
                选择胎面部件
            </button>
        </div>
        <div class="input-group">
            <label>基部胶料:</label>
            <input type="text" class="layui-input" id="baseCompound" placeholder="基部胶料" style="width: 200px; display: inline-block;">
        </div>
        <div class="input-group">
            <label>中心厚度:</label>
            <input type="text" class="layui-input" id="centerThickness" placeholder="中心厚度" style="width: 200px; display: inline-block;">
        </div>
    </div>
    
    <div class="test-section">
        <h3>胎侧部件测试</h3>
        <div class="input-group">
            <label>胎侧胶料:</label>
            <input type="text" class="layui-input" id="sidewallCompound" placeholder="选择胎侧胶料" style="width: 200px; display: inline-block;">
            <button type="button" class="layui-btn layui-btn-sm select-btn" 
                    onclick="partSelectorUtils.openPartSelector('sidewall', document.getElementById('sidewallCompound'), {
                        'sidewallCompound': '#sidewallCompound',
                        'wearResistantCompound': '#wearResistantCompound',
                        'width': '#sidewallWidth'
                    })">
                选择胎侧部件
            </button>
        </div>
        <div class="input-group">
            <label>耐磨胶料:</label>
            <input type="text" class="layui-input" id="wearResistantCompound" placeholder="耐磨胶料" style="width: 200px; display: inline-block;">
        </div>
        <div class="input-group">
            <label>胎侧宽度:</label>
            <input type="text" class="layui-input" id="sidewallWidth" placeholder="胎侧宽度" style="width: 200px; display: inline-block;">
        </div>
    </div>
    
    <div class="test-section">
        <h3>胎体部件测试</h3>
        <div class="input-group">
            <label>胎体材料:</label>
            <input type="text" class="layui-input" id="carcassMaterial" placeholder="选择胎体材料" style="width: 200px; display: inline-block;">
            <button type="button" class="layui-btn layui-btn-sm select-btn" 
                    onclick="partSelectorUtils.openPartSelector('carcass', document.getElementById('carcassMaterial'), {
                        'carcassMaterial': '#carcassMaterial',
                        'density': '#carcassDensity',
                        'carcassCompound': '#carcassCompound',
                        'angle': '#carcassAngle'
                    })">
                选择胎体部件
            </button>
        </div>
        <div class="input-group">
            <label>密度:</label>
            <input type="text" class="layui-input" id="carcassDensity" placeholder="密度" style="width: 200px; display: inline-block;">
        </div>
        <div class="input-group">
            <label>胎体胶料:</label>
            <input type="text" class="layui-input" id="carcassCompound" placeholder="胎体胶料" style="width: 200px; display: inline-block;">
        </div>
        <div class="input-group">
            <label>角度:</label>
            <input type="text" class="layui-input" id="carcassAngle" placeholder="角度" style="width: 200px; display: inline-block;">
        </div>
    </div>
    
    <div class="test-section">
        <h3>API测试</h3>
        <button type="button" class="layui-btn" onclick="testAPI('tread')">测试胎面API</button>
        <button type="button" class="layui-btn" onclick="testAPI('sidewall')">测试胎侧API</button>
        <button type="button" class="layui-btn" onclick="testAPI('carcass')">测试胎体API</button>
        <button type="button" class="layui-btn" onclick="testAPI('beadwire')">测试钢丝圈API</button>
        <div id="apiResult" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; display: none;">
            <h4>API测试结果:</h4>
            <pre id="apiResultContent"></pre>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['layer'], function(){
        var layer = layui.layer;
        var $ = layui.$;
        
        // 半部件选择功能 (复制自create-eto.html)
        window.partSelectorUtils = {
            openPartSelector: function(partType, inputElement, fieldMapping) {
                var that = this;
                
                $.ajax({
                    url: '/boot/api/parts/' + partType,
                    type: 'GET',
                    success: function(res) {
                        if (res.code === 0 && res.data) {
                            that.showPartSelectorDialog(partType, res.data, inputElement, fieldMapping);
                        } else {
                            layer.msg('获取' + that.getPartTypeName(partType) + '部件数据失败');
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，请重试');
                    }
                });
            },
            
            showPartSelectorDialog: function(partType, parts, inputElement, fieldMapping) {
                var that = this;
                var content = that.buildPartSelectorContent(partType, parts);
                
                layer.open({
                    type: 1,
                    title: '选择' + that.getPartTypeName(partType) + '部件',
                    area: ['800px', '600px'],
                    content: content,
                    btn: ['确定', '取消'],
                    yes: function(index, layero) {
                        var selectedPart = layero.find('input[name="selectedPart"]:checked');
                        if (selectedPart.length > 0) {
                            var partData = JSON.parse(selectedPart.val());
                            that.fillInputFields(partData, inputElement, fieldMapping);
                            layer.close(index);
                        } else {
                            layer.msg('请选择一个部件');
                        }
                    }
                });
            },
            
            buildPartSelectorContent: function(partType, parts) {
                var html = '<div class="part-selector-dialog">';
                html += '<div class="part-list" style="max-height: 400px; overflow-y: auto; padding: 10px;">';
                
                if (parts.length === 0) {
                    html += '<p style="text-align: center; color: #999; padding: 50px;">暂无' + this.getPartTypeName(partType) + '部件数据</p>';
                } else {
                    for (var i = 0; i < parts.length; i++) {
                        var part = parts[i];
                        html += '<div class="part-item" style="border: 1px solid #e6e6e6; margin: 10px 0; padding: 15px; border-radius: 5px; cursor: pointer;" onclick="$(this).find(\'input\').prop(\'checked\', true);">';
                        html += '<label style="cursor: pointer; display: block; width: 100%;">';
                        html += '<input type="radio" name="selectedPart" value=\'' + JSON.stringify(part).replace(/'/g, '&#39;') + '\' style="margin-right: 10px;">';
                        html += '<strong style="font-size: 16px;">' + (part.partName || '未命名') + '</strong>';
                        html += '<span style="color: #666; margin-left: 10px; font-size: 14px;">(' + (part.partSapcode || '无SAP代码') + ')</span>';
                        html += '<br><small style="color: #999; margin-top: 5px; display: block;">' + (part.partDesc || '无描述') + '</small>';
                        html += '</label>';
                        html += '</div>';
                    }
                }
                
                html += '</div></div>';
                return html;
            },
            
            getPartTypeName: function(partType) {
                var names = {
                    'tread': '胎面',
                    'sidewall': '胎侧', 
                    'carcass': '胎体',
                    'innerliner': '内衬层',
                    'capply': '冠带层',
                    'beltlayer': '带束层',
                    'beadwire': '钢丝圈',
                    'apex': '三角胶'
                };
                return names[partType] || partType;
            },
            
            fillInputFields: function(partData, inputElement, fieldMapping) {
                if (!fieldMapping) {
                    $(inputElement).val(partData.partName || '');
                    return;
                }
                
                for (var field in fieldMapping) {
                    var targetSelector = fieldMapping[field];
                    var value = partData[field] || '';
                    
                    if (typeof targetSelector === 'function') {
                        targetSelector(value, partData);
                    } else {
                        $(targetSelector).val(value);
                    }
                }
                
                layer.msg('已填充' + this.getPartTypeName(partData.partType || 'part') + '数据', {icon: 1});
            }
        };
        
        // API测试函数
        window.testAPI = function(partType) {
            $.ajax({
                url: '/boot/api/parts/' + partType,
                type: 'GET',
                success: function(res) {
                    $('#apiResult').show();
                    $('#apiResultContent').text(JSON.stringify(res, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#apiResult').show();
                    $('#apiResultContent').text('错误: ' + error + '\n状态: ' + status);
                }
            });
        };
    });
    </script>
</body>
</html>
