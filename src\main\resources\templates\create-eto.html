<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建ETO表单 - ETO线上系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        /* 继承主题样式 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .eto-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .param-table th {
            background-color: #f8f9fa;
            font-weight: normal;
        }

        .param-table input {
            width: 100%;
            border: none;
            text-align: center;
            background: transparent;
        }

        .id-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .id-format {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }

        .title-section {
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
            font-size: 18px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
        }

        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            word-wrap: break-word;
        }

        .data-table th {
            background-color: #f8f9fa;
            text-align: left;
            width: 200px;
        }

        .data-table th:first-child {
            width: 200px;
        }

        .data-table th:not(:first-child),
        .data-table td:not(:first-child) {
            width: calc((100% - 200px) / 3);
            text-align: center;
        }

        .data-table input {
            width: 100%;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            padding: 6px 10px;
            background: transparent;
            box-sizing: border-box;
        }

        .data-table input:focus {
            border-color: #409EFF;
            outline: none;
        }

        .section-title {
            font-weight: bold;
            margin: 20px 0 10px 0;
            padding: 5px;
            background: #f0f0f0;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
        }

        .layui-btn {
            height: 40px;
            line-height: 40px;
            padding: 0 24px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        /* 新增样式 */
        .format-header {
            font-weight: bold;
            margin-bottom: 12px;
        }
        
        .format-content {
            padding-left: 20px;
        }
        
        .format-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            gap: 12px;
        }
        
        .format-item label {
            min-width: 60px;
        }
        
        .format-item .layui-input {
            width: 100px;
        }
        
        .format-desc {
            color: #666;
            margin-left: 12px;
        }
        
        .format-rule {
            margin-top: 16px;
            padding: 8px;
            background: #f5f7fa;
            border-radius: 4px;
        }
        
        .param-table input.layui-input {
            height: 32px;
            line-height: 32px;
            padding: 0 8px;
            text-align: center;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
        }

        /* 新增和修改的样式 */
        .id-format-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .id-format-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }
        
        .id-format-table label {
            display: inline-block;
            width: 80px;
            font-weight: 500;
            color: #333;
        }
        
        .id-format-table .layui-input,
        .id-format-table .layui-select {
            display: inline-block;
            width: 150px;
            margin-right: 12px;
        }
        
        .id-format-table .format-desc {
            color: #666;
            font-size: 13px;
        }
        
        .id-format-table input[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin: 30px 0 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #3B82F6;
            border-radius: 0 4px 4px 0;
        }
        
        .data-table select.layui-select {
            width: 100px;
        }
        
        .data-table input.layui-input {
            width: 100%;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
        }
        
        .data-table select.layui-select option:first-child {
            color: #808080; /* 与输入框placeholder颜色保持一致 */
        }

        /* 模块标题样式优化 */
        .module-title {
            background-color: #1E9FFF;
            color: white;
            padding: 10px 15px;
            margin: 20px 0 15px 0;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            position: sticky;
            left: 0;
            z-index: 2;
            text-align: left;
        }

        /* 表格容器样式 */
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }

        .scheme-preview {
            position: fixed;
            top: 70px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            max-width: 250px;
            z-index: 1000;
            display: none;
            max-height: 300px;
            overflow-y: auto;
            opacity: 0.95;
            transition: all 0.3s ease;
        }
        
        .scheme-preview .close-btn {
            position: absolute;
            top: 8px;
            right: 10px;
            cursor: pointer;
            font-size: 16px;
            color: #999;
        }
        
        .scheme-preview .close-btn:hover {
            color: #666;
        }

        .scheme-preview.active {
            display: block;
        }

        .scheme-preview h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #1e1e2d;
        }

        .scheme-preview ul {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .scheme-preview li {
            margin-bottom: 5px;
            font-size: 14px;
            color: #4b5563;
        }

        .scheme-preview .changed-value {
            color: #d97706;
            font-weight: 500;
        }

        .invalid-input {
            border-color: #dc2626 !important;
            background-color: #fee2e2 !important;
        }

        .value-changed {
            background-color: #fef3c7 !important;
            position: relative;
        }
        
        .value-changed::after {
            content: "↑";
            position: absolute;
            top: -15px;
            right: 5px;
            color: #d97706;
            font-weight: bold;
        }

        .value-decreased::after {
            content: "↓";
        }

        .highlight-row th {
            background-color: #fff7e6 !important;
            color: #ff6b00;
        }

        .show-preview-btn {
            position: fixed;
            top: 70px;
            right: 20px;
            background: #1E9FFF;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            z-index: 999;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            display: none;
        }
        
        .show-preview-btn:hover {
            background: #0D8AE3;
        }

        /* 半部件选择按钮样式 */
        .part-select-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
            color: white !important;
            border-radius: 20px !important;
            padding: 4px 12px !important;
            font-size: 12px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3) !important;
            margin-top: 5px !important;
        }

        .part-select-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4) !important;
        }

        .part-select-btn i {
            margin-right: 4px !important;
        }

        /* 主选择按钮样式 */
        .main-part-select-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
            border: none !important;
            color: white !important;
            border-radius: 25px !important;
            padding: 6px 16px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 3px 6px rgba(79, 172, 254, 0.3) !important;
            margin-left: 10px !important;
        }

        .main-part-select-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 12px rgba(79, 172, 254, 0.4) !important;
        }

        .main-part-select-btn i {
            margin-right: 6px !important;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="eto-container">
            <!-- 实验轮胎订单标题 -->
            <div class="title-section" style="margin-bottom: 32px;">
                LINGLONG TIRE<br>
                Experimental Tire Order
            </div>

            <!-- 简化后的ID显示部分 -->
            <div class="id-section" style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-weight: bold;">ID:</span>
                    <span id="fullId"></span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="font-weight: bold;">SAP号:</span>
                    <span id="sapNumber"></span>
                </div>
            </div>

            <!-- 参数表格 -->
            <table class="param-table">
                <tr>
                    <th>Size&LI&SI</th>
                    <th>Pattern</th>
                    <th>Product Code</th>
                    <th>Status</th>
                    <th>NO.</th>
                    <th>SAP</th>
                    <th>Market</th>
                </tr>
                <tr>
                    <td><input type="text" class="layui-input" name="sizeLiSi" value=""></td>
                    <td><input type="text" class="layui-input" name="pattern" value=""></td>
                    <td><input type="text" class="layui-input" name="productCode" value=""></td>
                    <td>
                        <select class="layui-select" name="status">
                            <option value="">请选择</option>
                            <option value="T">T</option>
                            <option value="P">P</option>
                            <option value="M">M</option>
                        </select>
                    </td>
                    <td><input type="text" class="layui-input" name="id" value=""></td>
                    <td><input type="text" class="layui-input" name="sap" value=""></td>
                    <td>
                        <select class="layui-select" name="market">
                            <option value="">请选择</option>
                            <option value="OE">OE</option>
                            <option value="RE">RE</option>
                        </select>
                    </td>
                </tr>
            </table>

            <!-- 关联项目 -->
            <table class="data-table">
                <tr>
                    <th>关联项目:</th>
                    <td>
                        <select class="layui-input" id="projectSelect" name="projectId" style="width: 100%;">
                            <option value="">请选择要关联的项目</option>
                        </select>
                    </td>
                </tr>
            </table>

            <!-- Construction Group -->
            <table class="data-table">
                <tr>
                    <th>Construction Group:</th>
                    <td><input type="text" class="layui-input" name="constructionGroup" placeholder="请输入Construction Group"></td>
                </tr>
            </table>

            <!-- 轮胎参数表格 -->
            <table class="data-table">
                <tr>
                    <th>本次试制:</th>
                    <td><input type="text" class="layui-input" name="currentTest" placeholder="请输入本次试制内容"></td>
                </tr>
                <tr>
                    <th>Subject:</th>
                    <td><input type="text" class="layui-input" name="subject" placeholder="请输入项目代号" value=""></td>
                </tr>
                <tr>
                    <th>Main Test Items &Requirement:</th>
                    <td><input type="text" class="layui-input" name="mainTestItems" placeholder="请输入测试项目和要求"></td>
                </tr>
                <tr>
                    <th>Required number</th>
                    <td><input type="text" class="layui-input" name="requiredNumber" placeholder="请输入样胎数量*方案数量"></td>
                </tr>
                <tr>
                    <th>Mold drawing update or not</th>
                    <td>
                        <select class="layui-select" name="moldDrawingUpdate">
                            <option value="">请选择</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Electron prevulcanization or not</th>
                    <td>
                        <select class="layui-select" name="electronPrevulcanization">
                            <option value="">请选择</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>If with full spring vent</th>
                    <td>
                        <select class="layui-select" name="fullSpringVent">
                            <option value="">请选择</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Impact test or not</th>
                    <td>
                        <select class="layui-select" name="impactTest">
                            <option value="">请选择</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Standard rim width</th>
                    <td><input type="text" class="layui-input" name="standardRimWidth" placeholder="请输入标准轮辋宽度"></td>
                </tr>
                <tr>
                    <th>UF/DB update or not</th>
                    <td>
                        <select class="layui-select" name="ufdbUpdate">
                            <option value="">请选择</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>R2G</th>
                    <td><input type="text" class="layui-input" name="r2g" placeholder="请输入R2G"></td>
                </tr>
                <tr>
                    <th>Test result pass or not</th>
                    <td>
                        <select class="layui-select" name="testResult">
                            <option value="">请选择</option>
                            <option value="N.A">N.A</option>
                            <option value="Y">Y</option>
                            <option value="N">N</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Vehicle rim width</th>
                    <td><input type="text" class="layui-input" name="vehicleRimWidth" placeholder="请输入车辆轮辋宽度"></td>
                </tr>
                <tr>
                    <th>Signature</th>
                    <td><input type="text" class="layui-input" name="signature" placeholder="请输入签名"></td>
                </tr>
                <tr>
                    <th>Plant / Built / Cured</th>
                    <td>
                        <select class="layui-select" name="plantInfo">
                            <option value="" style="color: #808080;">请选择工厂</option>
                            <option value="PCR2">PCR2</option>
                            <option value="PCR5">PCR5</option>
                            <option value="PCR6">PCR6</option>
                            <option value="柳州">柳州</option>
                            <option value="德州">德州</option>
                            <option value="荆门">荆门</option>
                            <option value="吉林">吉林</option>
                            <option value="塞尔维亚">塞尔维亚</option>
                            <option value="安徽">安徽</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Building Time</th>
                    <td><input type="text" class="layui-input" name="buildingTime" placeholder="请输入成型时间" value=""></td>
                </tr>
                <tr>
                    <th>Engineer</th>
                    <td><input type="text" class="layui-input" name="engineer" placeholder="请输入工程师"></td>
                </tr>
                <tr>
                    <th>Sub Approver</th>
                    <td><input type="text" class="layui-input" name="subApprover" placeholder="请输入校对人" value=""></td>
                </tr>
                <tr>
                    <th>Approver</th>
                    <td><input type="text" class="layui-input" name="approver" placeholder="请输入审批人"></td>
                </tr>
                <tr>
                    <th>Tire Size</th>
                    <td><input type="text" class="layui-input" name="tireSize" readonly></td>
                </tr>
            </table>

            <div class="action-buttons">
                <button type="button" class="layui-btn layui-btn-primary" id="saveDraft">
                    <i class="fas fa-save"></i> 保存草稿
                </button>
                <button type="button" class="layui-btn" id="showSchemeDesign">
                    <i class="fas fa-arrow-down"></i> 展开方案设计
                </button>
            </div>
        </div>

        <!-- 方案设计部分 -->
        <div class="eto-container" id="schemeDesignSection" style="margin-top: 20px; display: none;">
            <div class="scheme-header">
                <div class="scheme-title">方案设计</div>
                <div class="scheme-actions">
                    <button type="button" class="layui-btn layui-btn-primary" id="importScheme">
                        <i class="fas fa-file-import"></i> 导入参考方案
                    </button>
                    <button type="button" class="layui-btn" id="addScheme">
                        <i class="fas fa-plus"></i> 新增方案
                    </button>
                </div>
            </div>

            <div class="module-title">模具信息</div>
            <form class="layui-form" lay-filter="schemeForm">
                <table class="data-table" id="moldInfoTable">
                    <tr class="hidden-row" style="display: none;">
                        <th>Hidden Row</th>
                        <td><input type="text" class="layui-input" name="hidden_1" value=""></td>
                        <td><input type="text" class="layui-input" name="hidden_2" value=""></td>
                        <td><input type="text" class="layui-input" name="hidden_3" value=""></td>
                    </tr>
                    <tr class="header-row">
                        <th>方案名称</th>
                        <th>方案1 <i class="fas fa-trash delete-scheme" data-index="1" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>
                        <th>方案2 <i class="fas fa-trash delete-scheme" data-index="2" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>
                        <th>方案3 <i class="fas fa-trash delete-scheme" data-index="3" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>
                    </tr>
                    
                    <tr>
                        <th>TDW</th>
                        <td><input type="text" class="layui-input" name="tdw_1" value=""></td>
                        <td><input type="text" class="layui-input" name="tdw_2" value=""></td>
                        <td><input type="text" class="layui-input" name="tdw_3" value=""></td>
                    </tr>
                    <tr>
                        <th>OD</th>
                        <td><input type="text" class="layui-input" name="od_1" value=""></td>
                        <td><input type="text" class="layui-input" name="od_2" value=""></td>
                        <td><input type="text" class="layui-input" name="od_3" value=""></td>
                    </tr>
                    <tr>
                        <th>SW</th>
                        <td><input type="text" class="layui-input" name="sw_1" value=""></td>
                        <td><input type="text" class="layui-input" name="sw_2" value=""></td>
                        <td><input type="text" class="layui-input" name="sw_3" value=""></td>
                    </tr>
                    <tr>
                        <th>G.D</th>
                        <td><input type="text" class="layui-input" name="gd_1" value=""></td>
                        <td><input type="text" class="layui-input" name="gd_2" value=""></td>
                        <td><input type="text" class="layui-input" name="gd_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Sea/Land Ratio</th>
                        <td><input type="text" class="layui-input" name="seaLandRatio_1" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatio_2" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatio_3" value=""></td>
                    </tr>
                    <tr class="highlight-row">
                        <th>Sea/Land ratio difference between upper and lower</th>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_1" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_2" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Bead Width</th>
                        <td><input type="text" class="layui-input" name="beadWidth_1" value=""></td>
                        <td><input type="text" class="layui-input" name="beadWidth_2" value=""></td>
                        <td><input type="text" class="layui-input" name="beadWidth_3" value=""></td>
                    </tr>
                </table>

                <div class="module-title">基本信息</div>
                <table class="data-table">
                    <tr id="specRow">
                        <th>Spec</th>
                        <td><input type="text" class="layui-input" name="spec_1" placeholder="方案号" value=""></td>
                        <td><input type="text" class="layui-input" name="spec_2" placeholder="方案号" value=""></td>
                        <td><input type="text" class="layui-input" name="spec_3" placeholder="方案号" value=""></td>
                    </tr>
                    <tr>
                        <th>OEM Spec No.</th>
                        <td><input type="text" class="layui-input" name="oemSpecNo_1" placeholder="OEM代号" value=""></td>
                        <td><input type="text" class="layui-input" name="oemSpecNo_2" placeholder="OEM代号" value=""></td>
                        <td><input type="text" class="layui-input" name="oemSpecNo_3" placeholder="OEM代号" value=""></td>
                    </tr>
                    <tr>
                        <th>Tire type</th>
                        <td>
                            <select class="layui-select" name="tireType_1">
                                <option value="">请选择</option>
                                <option value="Ground Tire">Ground Tire</option>
                                <option value="Spare Tire">Spare Tire</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="tireType_2">
                                <option value="">请选择</option>
                                <option value="Ground Tire">Ground Tire</option>
                                <option value="Spare Tire">Spare Tire</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="tireType_3">
                                <option value="">请选择</option>
                                <option value="Ground Tire">Ground Tire</option>
                                <option value="Spare Tire">Spare Tire</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>New Design (Y/N)</th>
                        <td>
                            <select class="layui-select" name="newDesign_1">
                                <option value="">请选择</option>
                                <option value="Y">Y</option>
                                <option value="N">N</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="newDesign_2">
                                <option value="">请选择</option>
                                <option value="Y">Y</option>
                                <option value="N">N</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="newDesign_3">
                                <option value="">请选择</option>
                                <option value="Y">Y</option>
                                <option value="N">N</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>IP(theory/actual)</th>
                        <td><input type="text" class="layui-input" name="ip_1" placeholder="IP轮胎内轮廓长度" value=""></td>
                        <td><input type="text" class="layui-input" name="ip_2" placeholder="IP轮胎内轮廓长度" value=""></td>
                        <td><input type="text" class="layui-input" name="ip_3" placeholder="IP轮胎内轮廓长度" value=""></td>
                    </tr>
                </table>

                <div class="module-title">胶料信息</div>
                <table class="data-table">
                    <tr>
                        <th>Inner Liner
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('innerliner', this, {
                                        'airTightLayerCompound': ['input[name=innerLinerType1_1]', 'input[name=innerLinerType1_2]', 'input[name=innerLinerType1_3]'],
                                        'airTightLayerThickness': ['input[name=innerLinerThickness1_1]', 'input[name=innerLinerThickness1_2]', 'input[name=innerLinerThickness1_3]'],
                                        'airTightLayerWidth': ['input[name=innerLinerWidth1_1]', 'input[name=innerLinerWidth1_2]', 'input[name=innerLinerWidth1_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择内衬层
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType1_1" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness1_1" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth1_1" placeholder="Width" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType1_2" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness1_2" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth1_2" placeholder="Width" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType1_3" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness1_3" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth1_3" placeholder="Width" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Transition Layer
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('innerliner', this, {
                                        'transitionLayerCompound': ['input[name=innerLinerType2_1]', 'input[name=innerLinerType2_2]', 'input[name=innerLinerType2_3]'],
                                        'transitionLayerThickness': ['input[name=innerLinerThickness2_1]', 'input[name=innerLinerThickness2_2]', 'input[name=innerLinerThickness2_3]'],
                                        'transitionLayerWidth': ['input[name=innerLinerWidth2_1]', 'input[name=innerLinerWidth2_2]', 'input[name=innerLinerWidth2_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择过渡层
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType2_1" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness2_1" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth2_1" placeholder="Width" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType2_2" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness2_2" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth2_2" placeholder="Width" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="innerLinerType2_3" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="innerLinerThickness2_3" placeholder="Gauge" value="">
                          <input type="text" class="layui-input" name="innerLinerWidth2_3" placeholder="Width" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Cushion Compound
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('sidewall', this, {
                                        'wearResistantCompound': ['input[name=cushionCompound_1]', 'input[name=cushionCompound_2]', 'input[name=cushionCompound_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择耐磨胶
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="cushionCompound_1" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="cushionCompound_2" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="cushionCompound_3" placeholder="Compound" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Sidewall Compound
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('sidewall', this, {
                                        'sidewallCompound': ['input[name=sidewallCompound_1]', 'input[name=sidewallCompound_2]', 'input[name=sidewallCompound_3]'],
                                        'wearResistantCompound': ['input[name=whiteCompound_1]', 'input[name=whiteCompound_2]', 'input[name=whiteCompound_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择胎侧
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="sidewallCompound_1" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="sidewallCompound_2" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="sidewallCompound_3" placeholder="Compound" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>White Compound</th>
                        <td>
                          <input type="text" class="layui-input" name="whiteCompound_1" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="whiteCompound_2" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="whiteCompound_3" placeholder="Compound" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Wedge Rubber</th>
                        <td>
                          <input type="text" class="layui-input" name="wedgeRubber_1" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="wedgeRubber_2" placeholder="Compound" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="wedgeRubber_3" placeholder="Compound" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Bead Filler : Compound / HT.
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('apex', this, {
                                        'hardCoreCompound': ['input[name=beadFillerCompound_1]', 'input[name=beadFillerCompound_2]', 'input[name=beadFillerCompound_3]'],
                                        'height': ['input[name=beadFillerHT_1]', 'input[name=beadFillerHT_2]', 'input[name=beadFillerHT_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择三角胶
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="beadFillerCompound_1" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="beadFillerHT_1" placeholder="Height" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beadFillerCompound_2" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="beadFillerHT_2" placeholder="Height" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beadFillerCompound_3" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="beadFillerHT_3" placeholder="Height" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Tread Compound1
                            <button type="button" class="main-part-select-btn"
                                    onclick="partSelectorUtils.openPartSelector('tread', this, {
                                        'treadCompound': ['input[name=treadCompound1_1]', 'input[name=treadCompound1_2]', 'input[name=treadCompound1_3]'],
                                        'baseCompound': ['input[name=treadCompound2_1]', 'input[name=treadCompound2_2]', 'input[name=treadCompound2_3]'],
                                        'centerThickness': ['input[name=treadCenterGaugeActual_1]', 'input[name=treadCenterGaugeActual_2]', 'input[name=treadCenterGaugeActual_3]'],
                                        'shoulderThickness': ['input[name=treadShoulderGauge_1]', 'input[name=treadShoulderGauge_2]', 'input[name=treadShoulderGauge_3]']
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择胎面
                            </button>
                        </th>
                        <td><input type="text" class="layui-input" name="treadCompound1_1" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCompound1_2" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCompound1_3" placeholder="Compound" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread Compound2</th>
                        <td><input type="text" class="layui-input" name="treadCompound2_1" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCompound2_2" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCompound2_3" placeholder="Compound" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread Wing</th>
                        <td><input type="text" class="layui-input" name="treadWing_1" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadWing_2" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadWing_3" placeholder="Compound" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread Base</th>
                        <td>
                          <input type="text" class="layui-input" name="treadBaseType_1" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="treadBaseThickness_1" placeholder="Gauge" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="treadBaseType_2" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="treadBaseThickness_2" placeholder="Gauge" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="treadBaseType_3" placeholder="Compound" value="">
                          <input type="text" class="layui-input" name="treadBaseThickness_3" placeholder="Gauge" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Tread Chimney</th>
                        <td><input type="text" class="layui-input" name="treadChimney_1" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadChimney_2" placeholder="Compound" value=""></td>
                        <td><input type="text" class="layui-input" name="treadChimney_3" placeholder="Compound" value=""></td>
                    </tr>
                    <tr>
                        <th>Sidewall die shape code</th>
                        <td>
                          <input type="text" class="layui-input" name="sidewallDieCode_1" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="sidewallDieSize_1" placeholder="Width" value="">
                          <input type="text" class="layui-input" name="sidewallDieRemark_1" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="sidewallDieCode_2" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="sidewallDieSize_2" placeholder="Width" value="">
                          <input type="text" class="layui-input" name="sidewallDieRemark_2" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="sidewallDieCode_3" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="sidewallDieSize_3" placeholder="Width" value="">
                          <input type="text" class="layui-input" name="sidewallDieRemark_3" placeholder="Process Code" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Apex Die shape code</th>
                        <td>
                          <input type="text" class="layui-input" name="apexDieCode_1" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="apexDieRemark_1" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="apexDieCode_2" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="apexDieRemark_2" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="apexDieCode_3" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="apexDieRemark_3" placeholder="Process Code" value="">
                        </td>
                    </tr>
                    <tr class="highlight-row">
                        <th>Tread die symmetry</th>
                        <td>
                            <input type="checkbox" name="symmetry_1" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_1" value="Dissymmetric"> Dissymmetric
                        </td>
                        <td>
                            <input type="checkbox" name="symmetry_2" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_2" value="Dissymmetric"> Dissymmetric
                        </td>
                        <td>
                            <input type="checkbox" name="symmetry_3" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_3" value="Dissymmetric"> Dissymmetric
                        </td>
                    </tr>
                    <tr>
                        <th>Tread die shape code</th>
                        <td>
                          <input type="text" class="layui-input" name="treadDieCode_1" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="treadDieRemark_1" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="treadDieCode_2" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="treadDieRemark_2" placeholder="Process Code" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="treadDieCode_3" placeholder="Structure Code" value="">
                          <input type="text" class="layui-input" name="treadDieRemark_3" placeholder="Process Code" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Main Groove Base Rubber Gauge</th>
                        <td><input type="text" class="layui-input" name="mainGrooveGauge_1" placeholder="主沟底基部胶层厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="mainGrooveGauge_2" placeholder="主沟底基部胶层厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="mainGrooveGauge_3" placeholder="主沟底基部胶层厚度" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread center gauge (theory)</th>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeTheory_1" placeholder="理论胎面中心厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeTheory_2" placeholder="理论胎面中心厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeTheory_3" placeholder="理论胎面中心厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                    </tr> 
                    <tr>
                        <th>Tread center gauge (actual)</th>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeActual_1" placeholder="实际胎面中心厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeActual_2" placeholder="实际胎面中心厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="treadCenterGaugeActual_3" placeholder="实际胎面中心厚度" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread shoulder gauge(theory)</th>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeTheory_1" placeholder="理论肩部厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeTheory_2" placeholder="理论肩部厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeTheory_3" placeholder="理论肩部厚度（公式计算值）基部胎冠层胶厚度+花纹沟深" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread shoulder gauge(actual)</th>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeActual_1" placeholder="实际肩部厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeActual_2" placeholder="实际肩部厚度" value=""></td>
                        <td><input type="text" class="layui-input" name="treadShoulderGaugeActual_3" placeholder="实际肩部厚度" value=""></td>
                    </tr>
                </table>

                <div class="module-title">骨架材料信息</div>
                <table class="data-table">
                    <tr>
                        <th>Ply Style(*New Materials)/Compound
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" style="margin-left: 10px;"
                                    onclick="partSelectorUtils.openPartSelector('carcass', this, {
                                        'carcassMaterial': 'input[name=plyMaterial_1]',
                                        'density': 'input[name=plyModel_1]',
                                        'carcassCompound': 'input[name=plyCompound_1]',
                                        'width': 'input[name=carcassPlyWidth1_1]',
                                        'angle': 'input[name=carcassAngle_1]'
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择胎体
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="plyMaterial_1" placeholder="胎体类型" value="">
                          <input type="text" class="layui-input" name="plyModel_1" placeholder="密度" value="">
                          <select class="layui-input" name="plyCount_1">
                            <option value="1-0" >1-0</option>
                            <option value="1-1">1-1</option>
                            <option value="2-0">2-0</option>
                            <option value="2-1">2-1</option>
                          </select>
                          <input type="text" class="layui-input" name="plyCompound_1" placeholder="胶胎" value="">
                          <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" style="margin-top: 5px;"
                                  onclick="partSelectorUtils.openPartSelector('carcass', this, {
                                      'carcassMaterial': 'input[name=plyMaterial_1]',
                                      'density': 'input[name=plyModel_1]',
                                      'carcassCompound': 'input[name=plyCompound_1]',
                                      'width': 'input[name=carcassPlyWidth1_1]',
                                      'angle': 'input[name=carcassAngle_1]'
                                  })">
                              选择
                          </button>
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="plyMaterial_2" placeholder="胎体类型" value="">
                          <input type="text" class="layui-input" name="plyModel_2" placeholder="密度" value="">
                          <select class="layui-input" name="plyCount_2">
                            <option value="1-0">1-0</option>
                            <option value="1-1">1-1</option>
                            <option value="2-0">2-0</option>
                            <option value="2-1">2-1</option>
                          </select>
                          <input type="text" class="layui-input" name="plyCompound_2" placeholder="胶胎" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="plyMaterial_3" placeholder="胎体类型" value="">
                          <input type="text" class="layui-input" name="plyModel_3" placeholder="密度" value="">
                          <select class="layui-input" name="plyCount_3">
                            <option value="1-0">1-0</option>
                            <option value="1-1">1-1</option>
                            <option value="2-0">2-0</option>
                            <option value="2-1">2-1</option>
                          </select>
                          <input type="text" class="layui-input" name="plyCompound_3" placeholder="胶胎" value="">
                        </td>
                    </tr>
                    <tr>
                        <th>Carcass Gauge</th>
                        <td>
                          <input type="text" class="layui-input" name="carcassGauge_1" placeholder="胎体压延厚度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassGauge_2" placeholder="胎体压延厚度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassGauge_3" placeholder="胎体压延厚度" value=""></td>
                    </tr>
                    <tr>
                        <th>Carcass Ply Width1</th>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth1_1" placeholder="1号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth1_2" placeholder="1号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth1_3" placeholder="1号胎体宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Carcass Ply Width2</th>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth2_1" placeholder="2号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth2_2" placeholder="2号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth2_3" placeholder="2号胎体宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Carcass Ply Width3</th>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth3_1" placeholder="3号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth3_2" placeholder="3号胎体宽度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="carcassPlyWidth3_3" placeholder="3号胎体宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Turn-up Height / Setting Position1</th>
                        <td>
                          <input type="text" class="layui-input" name="turnupHeight_1" placeholder="1号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="turnupHeight_2" placeholder="1号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="turnupHeight_3" placeholder="1号胎体反包高度" value=""></td>
                    </tr>
                    <tr>
                        <th>Turn-up Height / Setting Position2</th>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition_1" placeholder="2号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition_2" placeholder="2号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition_3" placeholder="2号胎体反包高度" value=""></td>
                    </tr>
                    <tr>
                        <th>Turn-up Height / Setting Position3</th>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition3_1" placeholder="3号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition3_2" placeholder="3号胎体反包高度" value=""></td>
                        <td>
                          <input type="text" class="layui-input" name="settingPosition3_3" placeholder="3号胎体反包高度" value=""></td>
                    </tr>
                    <tr>
                        <th>Carcass angle / direction</th>
                        <td><input type="text" class="layui-input" name="carcassAngle_1" placeholder="胎体角度一般90度" value=""></td>
                        <td><input type="text" class="layui-input" name="carcassAngle_2" placeholder="胎体角度一般90度" value=""></td>
                        <td><input type="text" class="layui-input" name="carcassAngle_3" placeholder="胎体角度一般90度" value=""></td>
                    </tr>
                    <tr>
                        <th>Bead Layup (HEX)/Bead Dia./Compound
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" style="margin-left: 10px;"
                                    onclick="partSelectorUtils.openPartSelector('beadwire', this, {
                                        'arrangementMethod': 'input[name=beadLayupHEX_1]',
                                        'wireDiameter': 'input[name=beadDia_1]',
                                        'beadWireInnerDiameter': 'input[name=beadInnerDia_1]',
                                        'compoundFormula': 'input[name=beadCompound_1]'
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择钢丝圈
                            </button>
                        </th>
                        <td>
                            <input type="text" class="layui-input" name="beadLayupHEX_1" placeholder="钢丝圈排列方式" value="">
                            <input type="text" class="layui-input" name="beadDia_1" placeholder="钢丝内径" value="">
                            <input type="text" class="layui-input" name="beadInnerDia_1" placeholder="钢丝圈内径" value="">
                            <input type="text" class="layui-input" name="beadCompound_1" placeholder="钢丝覆胶" value="">
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" style="margin-top: 5px;"
                                    onclick="partSelectorUtils.openPartSelector('beadwire', this, {
                                        'arrangementMethod': 'input[name=beadLayupHEX_1]',
                                        'wireDiameter': 'input[name=beadDia_1]',
                                        'beadWireInnerDiameter': 'input[name=beadInnerDia_1]',
                                        'compoundFormula': 'input[name=beadCompound_1]'
                                    })">
                                选择
                            </button>
                        </td>
                        <td>
                            <input type="text" class="layui-input" name="beadLayupHEX_2" placeholder="钢丝圈排列方式" value="">
                            <input type="text" class="layui-input" name="beadDia_2" placeholder="钢丝内径" value="">
                            <input type="text" class="layui-input" name="beadInnerDia_2" placeholder="钢丝圈内径" value="">
                            <input type="text" class="layui-input" name="beadCompound_2" placeholder="钢丝覆胶" value="">
                        </td>
                        <td>
                            <input type="text" class="layui-input" name="beadLayupHEX_3" placeholder="钢丝圈排列方式" value="">
                            <input type="text" class="layui-input" name="beadDia_3" placeholder="钢丝内径" value="">
                            <input type="text" class="layui-input" name="beadInnerDia_3" placeholder="钢丝圈内径" value="">
                            <input type="text" class="layui-input" name="beadCompound_3" placeholder="钢丝覆胶" value="">
                        </td>
                    </tr>

                    <tr>
                        <th>Belt Style (*New Materials)/Compound
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" style="margin-left: 10px;"
                                    onclick="partSelectorUtils.openPartSelector('beltlayer', this, {
                                        'steelMaterial': 'input[name=beltMaterial_1]',
                                        'density': 'input[name=beltModel_1]',
                                        'rubberCompound': 'input[name=beltCompound_1]',
                                        'steelThickness': 'input[name=beltGauge_1]',
                                        'beltWidth': 'input[name=beltWidth1_1]',
                                        'cutAngle': 'input[name=beltAngle_1]'
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择带束层
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="beltMaterial_1" placeholder="钢丝带束材料代号">
                          <input type="text" class="layui-input" name="beltModel_1" placeholder="密度">
                          <input type="text" class="layui-input" name="beltCompound_1" placeholder="钢丝覆胶">
                          <input type="text" class="layui-input" name="beltGauge_1" placeholder="钢丝覆胶厚度" value="">
                          <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" style="margin-top: 5px;"
                                  onclick="partSelectorUtils.openPartSelector('beltlayer', this, {
                                      'steelMaterial': 'input[name=beltMaterial_1]',
                                      'density': 'input[name=beltModel_1]',
                                      'rubberCompound': 'input[name=beltCompound_1]',
                                      'steelThickness': 'input[name=beltGauge_1]',
                                      'beltWidth': 'input[name=beltWidth1_1]',
                                      'cutAngle': 'input[name=beltAngle_1]'
                                  })">
                              选择
                          </button>
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltMaterial_2" placeholder="钢丝带束材料代号">
                          <input type="text" class="layui-input" name="beltModel_2" placeholder="密度">
                          <input type="text" class="layui-input" name="beltCompound_2" placeholder="钢丝覆胶">
                          <input type="text" class="layui-input" name="beltGauge_2" placeholder="钢丝覆胶厚度" value="">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltMaterial_3" placeholder="钢丝带束材料代号">
                          <input type="text" class="layui-input" name="beltModel_3" placeholder="密度">
                          <input type="text" class="layui-input" name="beltCompound_3" placeholder="钢丝覆胶">
                          <input type="text" class="layui-input" name="beltGauge_3" placeholder="钢丝覆胶厚度" value="">
                        </td>
                    </tr>

                   <tr>
                        <th>Belt Width #1/#2 Green</th>
                        <td>
                          <input type="text" class="layui-input" name="beltWidth1_1" placeholder="1号钢丝带束宽度">
                          <input type="text" class="layui-input" name="beltWidth2_1" placeholder="2号钢丝带束宽度">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltWidth1_2" placeholder="1号钢丝带束宽度">
                          <input type="text" class="layui-input" name="beltWidth2_2" placeholder="2号钢丝带束宽度">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltWidth1_3" placeholder="1号钢丝带束宽度">
                          <input type="text" class="layui-input" name="beltWidth2_3" placeholder="2号钢丝带束宽度">
                        </td>
                    </tr>
                    <tr>
                        <th>Belt Angle, Degrees</th>
                        <td><input type="text" class="layui-input" name="beltAngle_1" placeholder="钢丝角度"></td>
                        <td><input type="text" class="layui-input" name="beltAngle_2" placeholder="钢丝角度"></td>
                        <td><input type="text" class="layui-input" name="beltAngle_3" placeholder="钢丝角度"></td>
                    </tr>
                    <tr>
                        <th>1# Belt Edge Gum Strip/Compound</th>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness1_1" placeholder="钢丝1号带束贴边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth1_1" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound1_1" placeholder="胶料">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness1_2" placeholder="钢丝1号带束贴边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth1_2" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound1_2" placeholder="胶料">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness1_3" placeholder="钢丝1号带束贴边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth1_3" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound1_3" placeholder="胶料">
                        </td>
                    </tr>
                    <tr>
                        <th>2# Belt Edge Gum Strip/Compound</th>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness2_1" placeholder="钢丝2号带束贴边胶/包边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth2_1" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound2_1" placeholder="胶料">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness2_2" placeholder="钢丝2号带束贴边胶/包边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth2_2" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound2_2" placeholder="胶料">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="beltEdgeGumStripThickness2_3" placeholder="钢丝2号带束贴边胶/包边胶">
                          <input type="text" class="layui-input" name="beltEdgeGumStripWidth2_3" placeholder="宽度">
                          <input type="text" class="layui-input" name="beltEdgeGumStripCompound2_3" placeholder="胶料">
                        </td>
                    </tr>
                    <tr>
                        <th>Belt Drum Circumference</th>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_1" placeholder="贴合鼓周长" value=""></td>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_2" placeholder="贴合鼓周长" value=""></td>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_3" placeholder="贴合鼓周长" value=""></td>
                    </tr>
                    <tr>
                        <th>Belt Lift Ratio</th>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_1" placeholder="钢丝带束膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_2" placeholder="钢丝带束膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_3" placeholder="钢丝带束膨胀率" value=""></td>
                    </tr>
                    <tr>
                        <th>Inner Lift Ratio</th>
                        <td><input type="text" class="layui-input" name="innerLiftRatio_1" placeholder="内衬膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="innerLiftRatio_2" placeholder="内衬膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="innerLiftRatio_3" placeholder="内衬膨胀率" value=""></td>
                    </tr>
                    <tr>
                        <th>Carcass Lift Ratio</th>
                        <td><input type="text" class="layui-input" name="carcassLiftRatio_1" placeholder="胎体膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="carcassLiftRatio_2" placeholder="胎体膨胀率" value=""></td>
                        <td><input type="text" class="layui-input" name="carcassLiftRatio_3" placeholder="胎体膨胀率" value=""></td>
                    </tr>
                    <tr>
                        <th>Nylon Reinforcement</th>
                        <td>
                            <select class="layui-select" name="nylonReinforcement_1">
                                <option value="">请选择</option>
                                <option value="1JF1JE">1JF1JE</option>
                                <option value="2JF">2JF</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="nylonReinforcement_2">
                                <option value="">请选择</option>
                                <option value="1JF1JE">1JF1JE</option>
                                <option value="2JF">2JF</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="nylonReinforcement_3">
                                <option value="">请选择</option>
                                <option value="1JF1JE">1JF1JE</option>
                                <option value="2JF">2JF</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>Nylon Reinforcement Material/Density/Compound
                            <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" style="margin-left: 10px;"
                                    onclick="partSelectorUtils.openPartSelector('capply', this, {
                                        'capPlyMaterial': 'input[name=nylonReinforcementMaterial_1]',
                                        'density': 'input[name=nylonReinforcementDensity_1]',
                                        'capPlyCompound': 'input[name=nylonReinforcementCompound_1]'
                                    })">
                                <i class="layui-icon layui-icon-search"></i> 选择冠带层
                            </button>
                        </th>
                        <td>
                          <input type="text" class="layui-input" name="nylonReinforcementMaterial_1" placeholder="冠带材料">
                          <input type="text" class="layui-input" name="nylonReinforcementDensity_1" placeholder="密度">
                          <input type="text" class="layui-input" name="nylonReinforcementCompound_1" placeholder="材料覆胶">
                          <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" style="margin-top: 5px;"
                                  onclick="partSelectorUtils.openPartSelector('capply', this, {
                                      'capPlyMaterial': 'input[name=nylonReinforcementMaterial_1]',
                                      'density': 'input[name=nylonReinforcementDensity_1]',
                                      'capPlyCompound': 'input[name=nylonReinforcementCompound_1]'
                                  })">
                              选择
                          </button>
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="nylonReinforcementMaterial_2" placeholder="冠带材料">
                          <input type="text" class="layui-input" name="nylonReinforcementDensity_2" placeholder="密度">
                          <input type="text" class="layui-input" name="nylonReinforcementCompound_2" placeholder="材料覆胶">
                        </td>
                        <td>
                          <input type="text" class="layui-input" name="nylonReinforcementMaterial_3" placeholder="冠带材料">
                          <input type="text" class="layui-input" name="nylonReinforcementDensity_3" placeholder="密度">
                          <input type="text" class="layui-input" name="nylonReinforcementCompound_3" placeholder="材料覆胶">
                        </td>
                    </tr>

                </table> 

                <div class="module-title">设计信息</div>
                <table class="data-table">
                    <tr>
                        <th>Drum Width/B.S.D</th>
                        <td><input type="text" class="layui-input" name="drumWidth_1" placeholder="机头宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="drumWidth_2" placeholder="机头宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="drumWidth_3" placeholder="机头宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Reach In</th>
                        <td><input type="text" class="layui-input" name="reachIn_1" placeholder="胎侧伸出" value=""></td>
                        <td><input type="text" class="layui-input" name="reachIn_2" placeholder="胎侧伸出" value=""></td>
                        <td><input type="text" class="layui-input" name="reachIn_3" placeholder="胎侧伸出" value=""></td>
                    </tr>
                    <tr>
                        <th>Reach Out</th>
                        <td><input type="text" class="layui-input" name="reachOut_1" placeholder="胎侧垫入" value=""></td>
                        <td><input type="text" class="layui-input" name="reachOut_2" placeholder="胎侧垫入" value=""></td>
                        <td><input type="text" class="layui-input" name="reachOut_3" placeholder="胎侧垫入" value=""></td>
                    </tr>
                    <tr>
                        <th>Sidewall setting or Pre-Assemble</th>
                        <td><input type="text" class="layui-input" name="sidewallSetting_1" placeholder="" value=""></td>
                        <td><input type="text" class="layui-input" name="sidewallSetting_2" placeholder="" value=""></td>
                        <td><input type="text" class="layui-input" name="sidewallSetting_3" placeholder="" value=""></td>
                    </tr>
                    <tr>
                        <th>Circumference</th>
                        <td><input type="text" class="layui-input" name="circumference_1" placeholder="生胎外周长（公式计算）" value=""></td>
                        <td><input type="text" class="layui-input" name="circumference_2" placeholder="生胎外周长（公式计算）" value=""></td>
                        <td><input type="text" class="layui-input" name="circumference_3" placeholder="生胎外周长（公式计算）" value=""></td>
                    </tr>
                    <tr>
                        <th>Tread Color Line</th>
                        <td><input type="text" class="layui-input" name="treadColorLine_1" placeholder="胎面色线" value=""></td>
                        <td><input type="text" class="layui-input" name="treadColorLine_2" placeholder="胎面色线" value=""></td>
                        <td><input type="text" class="layui-input" name="treadColorLine_3" placeholder="胎面色线" value=""></td>
                    </tr>
                    <tr>
                        <th>Building Type</th>
                        <td><input type="text" class="layui-input" name="buildingType_1" placeholder="一二次法" value=""></td>
                        <td><input type="text" class="layui-input" name="buildingType_2" placeholder="一二次法" value=""></td>
                        <td><input type="text" class="layui-input" name="buildingType_3" placeholder="一二次法" value=""></td>
                    </tr>
                    <tr>
                        <th>Curing Temperature</th>
                        <td>
                            <select class="layui-select" name="curingTemperature_1">
                                <option value="">请选择</option>
                                <option value="165">165</option>
                                <option value="155">155</option>
                                <option value="normal">normal</option>
                            </select
                        </td>
                        <td>
                            <select class="layui-select" name="curingTemperature_2">
                                <option value="">请选择</option>
                                <option value="165">165</option>
                                <option value="155">155</option>
                                <option value="normal">normal</option>
                            </select>
                        </td>
                        <td>
                            <select class="layui-select" name="curingTemperature_3">
                                <option value="">请选择</option>
                                <option value="165">165</option>
                                <option value="155">155</option>
                                <option value="normal">normal</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>PCI Chuck Width</th>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_1" placeholder="后充气宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_2" placeholder="后充气宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_3" placeholder="后充气宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Inner bead distance of tire</th>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_1" placeholder="内子口宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_2" placeholder="内子口宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_3" placeholder="内子口宽度" value=""></td>
                    </tr>
                </table>

                <div class="module-title">参考数据</div>
                <table class="data-table">
                    <tr>
                        <th>Main groove base perimeter</th>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_1" placeholder="生胎外周长公式报错" value=""></td>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_2" placeholder="生胎外周长公式报错" value=""></td>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_3" placeholder="生胎外周长公式报错" value=""></td>
                    </tr>
                    <tr>
                        <th>Caculated Cured Bead Width</th>
                        <td><input type="text" class="layui-input" name="calculatedBeadWidth_1" placeholder="钢丝圈计算宽度报错" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedBeadWidth_2" placeholder="钢丝圈计算宽度报错" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedBeadWidth_3" placeholder="钢丝圈计算宽度报错" value=""></td>
                    </tr>
                    <tr>
                        <th>Cured Bead Width</th>
                        <td><input type="text" class="layui-input" name="curedBeadWidth_1" placeholder="实际钢丝圈宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="curedBeadWidth_2" placeholder="实际钢丝圈宽度" value=""></td>
                        <td><input type="text" class="layui-input" name="curedBeadWidth_3" placeholder="实际钢丝圈宽度" value=""></td>
                    </tr>
                    <tr>
                        <th>Caculated Weight-predicted (kg)</th>
                        <td><input type="text" class="layui-input" name="calculatedWeight_1" placeholder="计算重量" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedWeight_2" placeholder="计算重量" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedWeight_3" placeholder="计算重量" value=""></td>
                    </tr>
                    
                    <tr>
                        <th>风险识别项:</th>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_1" value=""></td>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_2" value=""></td>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_3" value=""></td>
                    </tr>
                    <tr>
                        <th style="background-color: #ffff00;">Confirmation</th>
                        <td style="background-color: #ffff00;">
                          <label class="layui-form-label" style="width: auto; padding: 5px;">E-BOM</label>
                          <input type="checkbox" name="confirmation_ebom_1" lay-skin="switch" lay-text="确认|未确认">
                        </td>
                        <td style="background-color: #ffff00;">
                          <label class="layui-form-label" style="width: auto; padding: 5px;">Performance parameter</label>
                          <input type="checkbox" name="confirmation_performance_2" lay-skin="switch" lay-text="确认|未确认">
                        </td>
                        <td style="background-color: #ffff00;">
                          <label class="layui-form-label" style="width: auto; padding: 5px;">Cut section analysis</label>
                          <input type="checkbox" name="confirmation_analysis_3" lay-skin="switch" lay-text="确认|未确认">
                        </td>
                    </tr>
                </table>
                <div class="note" style="margin-top: 20px; color: #666;">
                    注：超低温硫化时必须使用全弹氮气管
                </div>
            </form>

            <div class="action-buttons">
                <button type="button" class="layui-btn layui-btn-primary" id="saveSchemeDraft">
                    <i class="fas fa-save"></i> 保存草稿
                </button>
                <button type="button" class="layui-btn" id="submitAll">
                    <i class="fas fa-check"></i> 提交审核
                </button>
            </div>
        </div>

        <div class="show-preview-btn" id="showPreviewBtn">显示方案对比</div>
        <div class="scheme-preview" id="schemePreview">
            <h3>方案对比预览</h3>
            <span class="close-btn">&times;</span>
            <ul id="previewList"></ul>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        
        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURI(r[2]); return null;
        }

        // 半部件选择功能
        window.partSelectorUtils = {
            // 打开半部件选择弹窗
            openPartSelector: function(partType, inputElement, fieldMapping) {
                var that = this;

                // 获取部件数据
                $.ajax({
                    url: '/boot/api/parts/' + partType,
                    type: 'GET',
                    success: function(res) {
                        if (res.code === 0 && res.data) {
                            that.showPartSelectorDialog(partType, res.data, inputElement, fieldMapping);
                        } else {
                            layer.msg('获取' + that.getPartTypeName(partType) + '部件数据失败');
                        }
                    },
                    error: function() {
                        layer.msg('网络错误，请重试');
                    }
                });
            },

            // 显示部件选择对话框
            showPartSelectorDialog: function(partType, parts, inputElement, fieldMapping) {
                var that = this;
                var content = that.buildPartSelectorContent(partType, parts);

                layer.open({
                    type: 1,
                    title: '选择' + that.getPartTypeName(partType) + '部件',
                    area: ['800px', '600px'],
                    content: content,
                    btn: ['确定', '取消'],
                    yes: function(index, layero) {
                        var selectedPart = layero.find('input[name="selectedPart"]:checked');
                        if (selectedPart.length > 0) {
                            var partData = JSON.parse(selectedPart.val());
                            that.fillInputFields(partData, inputElement, fieldMapping);
                            layer.close(index);
                        } else {
                            layer.msg('请选择一个部件');
                        }
                    }
                });
            },

            // 构建部件选择器内容
            buildPartSelectorContent: function(partType, parts) {
                var html = '<div class="part-selector-dialog">';
                html += '<div class="part-list" style="max-height: 400px; overflow-y: auto; padding: 10px;">';

                if (parts.length === 0) {
                    html += '<p style="text-align: center; color: #999; padding: 50px;">暂无' + this.getPartTypeName(partType) + '部件数据</p>';
                } else {
                    for (var i = 0; i < parts.length; i++) {
                        var part = parts[i];
                        html += '<div class="part-item" style="border: 1px solid #e6e6e6; margin: 10px 0; padding: 15px; border-radius: 5px; cursor: pointer;" onclick="$(this).find(\'input\').prop(\'checked\', true);">';
                        html += '<label style="cursor: pointer; display: block; width: 100%;">';
                        html += '<input type="radio" name="selectedPart" value=\'' + JSON.stringify(part).replace(/'/g, '&#39;') + '\' style="margin-right: 10px;">';
                        html += '<strong style="font-size: 16px;">' + (part.partName || '未命名') + '</strong>';
                        html += '<span style="color: #666; margin-left: 10px; font-size: 14px;">(' + (part.partSapcode || '无SAP代码') + ')</span>';
                        html += '<br><small style="color: #999; margin-top: 5px; display: block;">' + (part.partDesc || '无描述') + '</small>';

                        // 显示部分关键参数
                        if (partType === 'tread' && part.centerThickness) {
                            html += '<small style="color: #409EFF; margin-top: 3px; display: block;">中心厚度: ' + part.centerThickness + 'mm</small>';
                        }
                        if (partType === 'sidewall' && part.width) {
                            html += '<small style="color: #409EFF; margin-top: 3px; display: block;">宽度: ' + part.width + 'mm</small>';
                        }
                        if (partType === 'carcass' && part.angle) {
                            html += '<small style="color: #409EFF; margin-top: 3px; display: block;">角度: ' + part.angle + '°</small>';
                        }

                        html += '</label>';
                        html += '</div>';
                    }
                }

                html += '</div>';
                html += '</div>';

                return html;
            },

            // 获取部件类型中文名称
            getPartTypeName: function(partType) {
                var names = {
                    'tread': '胎面',
                    'sidewall': '胎侧',
                    'carcass': '胎体',
                    'innerliner': '内衬层',
                    'capply': '冠带层',
                    'beltlayer': '带束层',
                    'beadwire': '钢丝圈',
                    'apex': '三角胶'
                };
                return names[partType] || partType;
            },

            // 填充输入框字段
            fillInputFields: function(partData, inputElement, fieldMapping) {
                if (!fieldMapping) {
                    // 如果没有字段映射，直接填充部件名称
                    $(inputElement).val(partData.partName || '');
                    return;
                }

                var filledCount = 0;

                // 根据字段映射填充相关输入框
                for (var field in fieldMapping) {
                    var targetSelector = fieldMapping[field];
                    var value = partData[field] || '';

                    if (typeof targetSelector === 'function') {
                        targetSelector(value, partData);
                        filledCount++;
                    } else if (Array.isArray(targetSelector)) {
                        // 如果是数组，填充所有对应的输入框
                        for (var i = 0; i < targetSelector.length; i++) {
                            var selector = targetSelector[i];
                            if ($(selector).length > 0) {
                                $(selector).val(value);
                                filledCount++;
                            }
                        }
                    } else {
                        // 单个选择器
                        if ($(targetSelector).length > 0) {
                            $(targetSelector).val(value);
                            filledCount++;
                        }
                    }
                }

                // 显示成功消息
                if (filledCount > 0) {
                    layer.msg('已填充' + this.getPartTypeName(partData.partType || 'part') + '数据到 ' + filledCount + ' 个字段', {icon: 1});
                } else {
                    layer.msg('未找到匹配的输入框', {icon: 2});
                }
            }
        };
        
        // 获取预设的项目ID
        var presetProjectId = getUrlParam('projectId');
        
        // 加载项目列表
        function loadProjects() {
            $.ajax({
                url: '/boot/api/eto-projects',
                type: 'GET',
                success: function(res) {
                    if(res.code === 0 && res.data) {
                        var select = $('#projectSelect');
                        select.empty().append('<option value="">请选择要关联的项目</option>');
                        
                        // 安全访问项目列表，确保是数组
                        var projects = res.data.list || [];
                        if(!Array.isArray(projects)) {
                            console.warn('API返回的项目列表不是数组:', projects);
                            projects = [];
                        }
                        
                        projects.forEach(function(project) {
                            var selected = presetProjectId && (project.id == presetProjectId) ? 'selected' : '';
                            select.append('<option value="' + project.id + '" ' + selected + '>' + project.projectNo + ' - ' + project.projectName + ' (' + project.size + ')</option>');
                        });
                        
                        form.render('select');
                    } else {
                        console.warn('获取项目列表失败:', res);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载项目列表失败:', error);
                    layer.msg('加载项目列表失败，请刷新页面重试');
                }
            });
        }
        
        // 页面加载完成后加载项目列表
        loadProjects();

        // 展开/收起方案设计部分
        $('#showSchemeDesign').on('click', function(){
            var $section = $('#schemeDesignSection');
            var $icon = $(this).find('i');
            var $text = $(this).contents().last();
            
            if($section.is(':visible')) {
                $section.slideUp();
                $icon.removeClass('fa-arrow-up').addClass('fa-arrow-down');
                $text.replaceWith(' 展开方案设计');
            } else {
                $section.slideDown();
                $icon.removeClass('fa-arrow-down').addClass('fa-arrow-up');
                $text.replaceWith(' 收起方案设计');
            }
        });

        // 保存所有数据
        $('#submitAll').on('click', function(){
            // 收集基础信息表单数据
            var basicFormData = {};
            $('.data-table input, .data-table select, .param-table input, .param-table select').each(function() {
                var name = $(this).attr('name');
                if(name) {
                    basicFormData[name] = $(this).val();
                }
            });
            
            // 添加项目ID
            var projectId = $('#projectSelect').val();
            if(projectId) {
                basicFormData.projectId = projectId;
            }

            // 收集方案设计数据
            var schemeFormData = {};
            $('#moldInfoTable input').each(function() {
                var name = $(this).attr('name');
                if(name) {
                    schemeFormData[name] = $(this).val();
                }
            });

            // 合并数据
            var allData = {
                basicInfo: basicFormData,
                schemeDesign: schemeFormData
            };

            // 发送请求
            $.ajax({
                url: '/boot/saveAllEtoData',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(allData),
                success: function(res){
                    if(res.code === 0) {
                        layer.msg('保存成功，正在提交审核...');
                        setTimeout(function(){
                            window.location.href = '/boot/my-eto';
                        }, 1000);
                    } else {
                        layer.msg(res.msg || '保存失败，请重试');
                    }
                },
                error: function(xhr, status, error){
                    console.log('错误信息:', error);
                    layer.msg('系统错误，请重试');
                }
            });
        });

        // 基础信息相关功能
        function updateFullId() {
            var prefix1 = 'BG'; // BG固定
            var market = $('select[name="market"]').val() || 'OE';
            var productCode = $('input[name="productCode"]').val();
            var status = $('select[name="status"]').val();
            var serialNumber = $('input[name="id"]').val();
            
            var fullId = [prefix1, market, productCode, status, serialNumber]
                .filter(Boolean)
                .join('-');
                
            $('#fullId').text(fullId);
        }
        
        // 监听基础信息输入变化
        $('input[name="productCode"], input[name="id"]').on('input', updateFullId);
        $('select[name="market"], select[name="status"]').on('change', updateFullId);
        
        // 初始化表单和ID
        form.render('select');
        updateFullId();

        // 监听SAP号变化
        $('input[name="sap"]').on('input', function(){
            $('#sapNumber').text($(this).val());
        });

        // 监听 Size&LI&SI 和 Pattern 的变化
        $('input[name="sizeLiSi"], input[name="pattern"]').on('input', function(){
            var sizeLiSi = $('input[name="sizeLiSi"]').val();
            var pattern = $('input[name="pattern"]').val();
            $('input[name="tireSize"]').val(sizeLiSi + ' ' + pattern);
        });
        
        // 初始化 Tire Size
        var initialSizeLiSi = $('input[name="sizeLiSi"]').val();
        var initialPattern = $('input[name="pattern"]').val();
        $('input[name="tireSize"]').val(initialSizeLiSi + ' ' + initialPattern);

        // 方案设计相关功能
        // 验证规则
        var validationRules = {
            tdw: { min: 50, max: 500 },
            od: { min: 300, max: 1500 },
            sw: { min: 50, max: 500 },
            gd: { min: 0.5, max: 10 },
            seaLandRatio: { pattern: /^\d{1,2}(\.\d{1,2})?%$/ },
            beadWidth: { min: 10, max: 50 }
        };

        // 验证函数
        function validateInput(input) {
            var name = input.attr('name').split('_')[0];
            var value = input.val();
            var rule = validationRules[name];
            
            if (!rule) return true;

            if (rule.min !== undefined && rule.max !== undefined) {
                var numValue = parseFloat(value);
                if (isNaN(numValue) || numValue < rule.min || numValue > rule.max) {
                    input.addClass('invalid-input');
                    layer.tips('值应在 ' + rule.min + ' 到 ' + rule.max + ' 之间', input);
                    return false;
                }
            }

            if (rule.pattern && !rule.pattern.test(value)) {
                input.addClass('invalid-input');
                layer.tips('格式不正确', input);
                return false;
            }

            input.removeClass('invalid-input');
            return true;
        }

        // 导入参考方案
        $('#importScheme').on('click', function(){
            layer.open({
                type: 2,
                title: '导入参考方案',
                area: ['80%', '80%'],
                content: '/boot/reference-schemes',
                btn: ['确定', '取消'],
                yes: function(index, layero){
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    var selectedScheme = iframeWin.getSelectedScheme();
                    if(selectedScheme){
                        insertSchemeAtFirst(selectedScheme);
                        layer.close(index);
                    } else {
                        layer.msg('请选择一个参考方案');
                    }
                }
            });
        });

        // 在方案一位置插入参考方案
        function insertSchemeAtFirst(schemeData) {
            var tables = $('.data-table'); // 获取所有data-table类的表格
            var columnCount = $('#moldInfoTable tr:first th').length - 1;
            
            // 保存现有方案数据
            var existingSchemes = [];
            for(var i = 1; i <= columnCount; i++) {
                var schemeData = {};
                tables.each(function() {
                    var table = $(this);
                    table.find('tr:gt(0)').each(function(){
                        var input = $(this).find('td:eq(' + (i-1) + ') input');
                        if(input.length) {
                            schemeData[input.attr('name').split('_')[0]] = input.val();
                        }
                    });
                });
                existingSchemes.push(schemeData);
            }
            
            // 清除现有方案列
            tables.each(function() {
                var table = $(this);
                table.find('tr').each(function(){
                    $(this).find('th:gt(0), td').remove();
                });
            });
            
            // 添加参考方案作为第一列
            tables.each(function() {
                var table = $(this);
                if (table.attr('id') === 'moldInfoTable') {
                    table.find('tr:first').append('<th>参考方案 <i class="fas fa-trash delete-scheme" data-index="1"></i></th>');
                }
                existingSchemes.forEach(function(scheme, index) {
                    var schemeIndex = index + 2;
                    table.find('tr:first').append('<th>方案' + (index + 1) + ' <i class="fas fa-trash delete-scheme" data-index="' + schemeIndex + '"></i></th>');
                    table.find('tr:gt(0)').each(function(){
                        var paramName = $(this).find('th').text().toLowerCase().replace(/[^a-z0-9]/g, '');
                        var value = scheme[paramName] || '';
                        $(this).append('<td><input type="text" class="layui-input" name="' + paramName + '_' + schemeIndex + '" value="' + value + '"></td>');
                    });
                });
            });
            
            form.render();
            watchValueChanges();
            bindDeleteEvents();
        }

        // 新增方案
        $('#addScheme').on('click', function(){
            // 只处理方案设计部分的表格
            var $moldInfoTable = $('#moldInfoTable');
            var columnCount = $moldInfoTable.find('tr.header-row th').length;
            var newSchemeNumber = columnCount;

            // 遍历方案设计部分的表格
            $('#schemeDesignSection .data-table').each(function() {
                var table = $(this);
                // 只对表头行添加新列
                if (table.attr('id') === 'moldInfoTable') {
                    table.find('tr.header-row').append('<th>方案' + newSchemeNumber + ' <i class="fas fa-trash delete-scheme" data-index="' + newSchemeNumber + '" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>');
                }
                // 为所有其他行添加输入框
                table.find('tr:not(.header-row)').each(function(){
                    var $row = $(this);
                    var thText = $row.find('th').text().trim();
                    var paramName = thText.toLowerCase().replace(/[^a-z0-9]/g, '');
                    
                    if ($row.hasClass('hidden-row')) {
                        $row.append('<td><input type="text" class="layui-input" name="hidden_' + newSchemeNumber + '" value=""></td>');
                    } else if (thText === 'Confirmation') {
                        $row.append('<td style="background-color: #ffff00;"><input type="text" class="layui-input" name="confirmation_' + newSchemeNumber + '" value=""></td>');
                    } else {
                        $row.append('<td><input type="text" class="layui-input" name="' + paramName + '_' + newSchemeNumber + '" value=""></td>');
                    }
                });
            });

            form.render();
            watchValueChanges();
            bindDeleteEvents();
        });

        // 删除方案后重新编号
        function reNumberSchemes() {
            // 只处理方案设计部分的表格
            $('#schemeDesignSection .data-table').each(function() {
                var table = $(this);
                if (table.attr('id') === 'moldInfoTable') {
                    table.find('tr.header-row th:gt(0)').each(function(i){
                        var newIndex = i + 1;
                        $(this).html('方案' + newIndex + ' <i class="fas fa-trash delete-scheme" data-index="' + newIndex + '" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i>');
                    });
                }
                
                // 更新所有表格中输入框的name属性
                table.find('tr:not(.header-row)').each(function(){
                    $(this).find('td').each(function(i){
                        var input = $(this).find('input');
                        if(input.length) {
                            var oldName = input.attr('name');
                            var newName = oldName.replace(/_\d+$/, '_' + (i + 1));
                            input.attr('name', newName);
                        }
                    });
                });
            });
        }

        // 更新删除方案的处理函数
        function bindDeleteEvents() {
            $('.delete-scheme').off('click').on('click', function(){
                var index = $(this).data('index');
                var tables = $('.data-table');
                
                layer.confirm('确认删除该方案？', {
                    btn: ['确定','取消']
                }, function(layerIndex){
                    tables.each(function() {
                        var table = $(this);
                        table.find('tr').each(function(){
                            if(table.attr('id') === 'moldInfoTable') {
                                $(this).find('th:eq(' + index + '), td:eq(' + (index-1) + ')').remove();
                            } else {
                                $(this).find('td:eq(' + (index-1) + ')').remove();
                            }
                        });
                    });

                    reNumberSchemes();
                    form.render();
                    watchValueChanges();
                    bindDeleteEvents();

                    // 关闭确认弹窗
                    layer.close(layerIndex);

                    // 显示删除成功消息
                    layer.msg('方案删除成功', {icon: 1});
                });
            });
        }

        // 监听值变化
        function watchValueChanges(){
            $('.layui-input').off('input').on('input', function(){
                var $input = $(this);
                validateInput($input);
                updateSchemePreview();
            });
            
            // 添加关闭按钮事件处理
            $('.scheme-preview .close-btn').off('click').on('click', function(e){
                e.stopPropagation();
                $('#schemePreview').removeClass('active');
                $('#showPreviewBtn').show();
            });
            
            // 添加显示预览按钮事件处理
            $('#showPreviewBtn').off('click').on('click', function(){
                $(this).hide();
                $('#schemePreview').addClass('active');
            });
        }

        // 更新方案预览
        function updateSchemePreview() {
            var $preview = $('#schemePreview');
            var $previewList = $('#previewList');
            var $showPreviewBtn = $('#showPreviewBtn');
            var changes = [];

            $('.data-table tr:gt(0)').each(function() {
                var $row = $(this);
                var paramName = $row.find('th').text();
                var baseValue = $row.find('td:eq(0) input').val();
                
                $row.find('td:gt(0)').each(function(index) {
                    var currentValue = $(this).find('input').val();
                    if(baseValue && currentValue && baseValue !== currentValue) {
                        changes.push({
                            param: paramName,
                            scheme: index + 2,
                            from: baseValue,
                            to: currentValue
                        });
                    }
                });
            });

            if (changes.length > 0) {
                $previewList.empty();
                changes.forEach(function(change) {
                    $previewList.append(
                        '<li>' + change.param + ' (方案' + change.scheme + '): ' +
                        '<span class="changed-value">' + change.from + ' → ' + change.to + '</span></li>'
                    );
                });
                $preview.addClass('active');
                $showPreviewBtn.hide();
            } else {
                $preview.removeClass('active');
                $showPreviewBtn.hide(); // 如果没有变化，两个按钮都不显示
            }
        }

        // 初始化
        watchValueChanges();
        bindDeleteEvents();
    });
    </script>
</body>
</html>