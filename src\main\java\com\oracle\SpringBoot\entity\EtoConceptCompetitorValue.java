package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ETO概念竞品参数值实体类
 */
@Data
@TableName("eto_concept_competitor_value")
public class EtoConceptCompetitorValue {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 竞品ID
     */
    private Long competitorId;
    
    /**
     * 参数ID
     */
    private Long paramId;
    
    /**
     * 参数值
     */
    private String paramValue;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
} 