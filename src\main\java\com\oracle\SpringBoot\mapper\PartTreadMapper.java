package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartTread;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 胎面部件Mapper接口
 */
@Mapper
public interface PartTreadMapper extends BaseMapper<PartTread> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartTread WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartTread selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartTread WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartTread> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的胎面部件
     */
    @Select("SELECT * FROM PartTread WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartTread> selectAllActive();
    
    /**
     * 根据胶料类型查询
     */
    @Select("SELECT * FROM PartTread WHERE TreadCompound = #{compound} AND FLAG = 1")
    List<PartTread> selectByTreadCompound(String compound);
    
    /**
     * 根据厚度范围查询
     */
    @Select("SELECT * FROM PartTread WHERE CenterThickness BETWEEN #{minThickness} AND #{maxThickness} AND FLAG = 1")
    List<PartTread> selectByThicknessRange(Double minThickness, Double maxThickness);
}
