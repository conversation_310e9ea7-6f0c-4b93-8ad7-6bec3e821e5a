package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_Main")
public class EtoMain implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("Code")
    private String code;

    @TableField("Plant")
    private String plant;

    @TableField("FullId")
    private String fullId;

    @TableField("Status")
    private String status;

    @TableField("CreateBy")
    private String createBy;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("UpdateTime")
    private Date updateTime;

    @TableField("ProjectId")
    private Long projectId;

}
