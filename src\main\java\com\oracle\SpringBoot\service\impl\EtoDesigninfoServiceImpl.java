package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoDesigninfo;
import com.oracle.SpringBoot.mapper.EtoDesigninfoMapper;
import com.oracle.SpringBoot.service.IEtoDesigninfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO设计信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoDesigninfoServiceImpl extends ServiceImpl<EtoDesigninfoMapper, EtoDesigninfo> implements IEtoDesigninfoService {

    @Override
    public EtoDesigninfo getBySchemeId(Integer schemeId) {
        return this.lambdaQuery()
                .eq(EtoDesigninfo::getSchemeId, schemeId)
                .one();
    }
}
