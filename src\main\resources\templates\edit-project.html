<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑项目 - ETO线上系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <style>
        /* 继承主题样式 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .eto-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .eto-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
        }

        .eto-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e1e2d;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4b5563;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .layui-btn {
            height: 40px;
            line-height: 40px;
            padding: 0 24px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        /* 表单布局 */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -12px;
        }

        .form-col {
            flex: 1;
            padding: 0 12px;
            min-width: 300px;
            margin-bottom: 15px;
        }
        
        .form-col-full {
            flex: 100%;
            width: 100%;
        }

        /* 只读字段样式 */
        .layui-input[readonly] {
            background-color: #f5f7fa;
            cursor: not-allowed;
            color: #606266;
        }

        /* 必填项标记 */
        .required::after {
            content: "*";
            color: #FF5722;
            margin-left: 4px;
        }
        
        /* 表单控件样式 */
        .layui-input, .layui-textarea, .layui-select {
            border-color: #DCDFE6;
            border-radius: 4px;
        }
        
        .layui-input:focus, .layui-textarea:focus {
            border-color: #409EFF;
        }
        
        .layui-textarea {
            min-height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="eto-container">
            <div class="eto-header">
                <div class="eto-title">编辑项目</div>
            </div>

            <form class="layui-form" id="projectForm" lay-filter="projectForm">
                <input type="hidden" name="id" id="projectId">
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>项目编号</label>
                            <input type="text" name="projectNo" class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="required">项目名称</label>
                            <input type="text" name="projectName" placeholder="请输入项目名称" class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="required">轮胎组别</label>
                            <select name="tireGroup" lay-verify="required">
                                <option value="">请选择轮胎组别</option>
                                <option value="SUV">SUV</option>
                                <option value="PCR">PCR</option>
                                <option value="TBR">TBR</option>
                                <option value="OTR">OTR</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="required">轮胎规格</label>
                            <input type="text" name="size" placeholder="请输入轮胎规格" class="layui-input" lay-verify="required">
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="required">市场</label>
                            <select name="market" lay-verify="required">
                                <option value="">请选择市场</option>
                                <option value="China">中国</option>
                                <option value="America">美国</option>
                                <option value="Europe">欧洲</option>
                                <option value="Asia">亚洲</option>
                                <option value="Global">全球</option>
                                <option value="Other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="required">品牌</label>
                            <select name="brand" lay-verify="required">
                                <option value="">请选择品牌</option>
                                <option value="LL">LL</option>
                                <option value="ATLAS">ATLAS</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>目的</label>
                            <select name="purpose">
                                <option value="">请选择目的</option>
                                <option value="RE">RE</option>
                                <option value="OE">OE</option>
                                <option value="OE/RE">OE/RE</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label>轮胎细分</label>
                            <select name="tireSegment">
                                <option value="">请选择轮胎细分</option>
                                <option value="SUV">SUV</option>
                                <option value="Passenger">Passenger</option>
                                <option value="Van">Van</option>
                                <option value="Pick Up">Pick Up</option>
                                <option value="ETC">ETC</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>基础</label>
                            <input type="text" name="basis" placeholder="请输入基础" class="layui-input">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label>OE厂商</label>
                            <input type="text" name="oeMaker" placeholder="请输入OE厂商" class="layui-input">
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>申请人</label>
                            <input type="text" name="applicant" placeholder="请输入申请人" class="layui-input">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label>部门</label>
                            <select name="department">
                                <option value="">请选择部门</option>
                                <option value="RD">研发部</option>
                                <option value="PM">项目管理部</option>
                                <option value="QA">质量保证部</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>工厂</label>
                            <select name="plant">
                                <option value="">请选择工厂</option>
                                <option value="Zhao yuan">Zhao yuan</option>
                                <option value="PCR">PCR</option>
                                <option value="PCR5">PCR5</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label>状态</label>
                            <select name="status">
                                <option value="pending">待审批</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已拒绝</option>
                                <option value="draft">草稿</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col form-col-full">
                        <div class="form-group">
                            <label>研发目的</label>
                            <textarea name="purposeOfDevelopment" placeholder="请输入研发目的" class="layui-textarea"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>创建时间</label>
                            <input type="text" name="createTime" class="layui-input" readonly>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label>更新时间</label>
                            <input type="text" name="updateTime" class="layui-input" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="layui-btn" id="saveBtn" lay-submit lay-filter="saveForm">
                        <i class="fas fa-save"></i> 保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;
        
        // 获取项目ID
        var projectId = getProjectIdFromUrl();
        
        // 初始化表单
        form.render();
        
        // 加载项目数据
        function loadProject() {
            $.ajax({
                url: '/boot/api/eto-project/' + projectId,
                type: 'GET',
                success: function(res) {
                    if(res.code === 0) {
                        var projectData = res.data;
                        
                        // 填充表单
                        $('#projectId').val(projectData.id);
                        $('input[name="projectNo"]').val(projectData.projectNo);
                        $('input[name="projectName"]').val(projectData.projectName);
                        $('select[name="tireGroup"]').val(projectData.tireGroup);
                        $('input[name="size"]').val(projectData.size);
                        $('select[name="brand"]').val(projectData.brand);
                        $('select[name="market"]').val(projectData.market);
                        $('select[name="status"]').val(projectData.status);
                        $('select[name="purpose"]').val(projectData.purpose);
                        $('select[name="tireSegment"]').val(projectData.tireSegment);
                        $('input[name="basis"]').val(projectData.basis);
                        $('input[name="oeMaker"]').val(projectData.oeMaker);
                        $('input[name="applicant"]').val(projectData.applicant);
                        $('select[name="department"]').val(projectData.department);
                        $('select[name="plant"]').val(projectData.plant);
                        $('textarea[name="purposeOfDevelopment"]').val(projectData.purposeOfDevelopment);
                        
                        if(projectData.createTime) {
                            $('input[name="createTime"]').val(formatDate(projectData.createTime));
                        }
                        
                        if(projectData.updateTime) {
                            $('input[name="updateTime"]').val(formatDate(projectData.updateTime));
                        }
                        
                        // 更新表单渲染
                        form.render();
                    } else {
                        layer.msg(res.msg || '获取项目信息失败');
                    }
                },
                error: function() {
                    layer.msg('服务器错误，请稍后重试');
                }
            });
        }
        
        // 从URL中获取项目ID
        function getProjectIdFromUrl() {
            var url = window.location.href;
            var parts = url.split('/');
            return parts[parts.length - 1];
        }
        
        // 格式化日期
        function formatDate(timestamp) {
            if(!timestamp) return '';
            var date = new Date(timestamp);
            return date.getFullYear() + '-' + 
                   padZero(date.getMonth() + 1) + '-' + 
                   padZero(date.getDate()) + ' ' + 
                   padZero(date.getHours()) + ':' + 
                   padZero(date.getMinutes());
        }
        
        // 补零
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }
        
        // 保存按钮点击事件
        form.on('submit(saveForm)', function(data) {
            // 收集表单数据
            var formData = data.field;
            
            // 发送更新请求
            $.ajax({
                url: '/boot/api/eto-project/' + projectId,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(res) {
                    if(res.code === 0) {
                        layer.msg('保存成功', {icon: 1});
                        setTimeout(function() {
                            window.location.href = '/boot/eto-project/' + projectId;
                        }, 1000);
                    } else {
                        layer.msg(res.msg || '保存失败，请重试', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('服务器错误，请稍后重试', {icon: 2});
                }
            });
            
            return false;
        });
        
        // 取消按钮点击事件
        $('#cancelBtn').on('click', function() {
            window.location.href = '/boot/eto-project/' + projectId;
        });
        
        // 初始化加载项目数据
        loadProject();
    });
    </script>
</body>
</html> 