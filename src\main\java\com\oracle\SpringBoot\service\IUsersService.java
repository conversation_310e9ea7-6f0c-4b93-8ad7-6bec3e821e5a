package com.oracle.SpringBoot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.oracle.SpringBoot.entity.Users;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface IUsersService extends IService<Users> {
    boolean validateUsers(String username, String password);

    boolean validateStatus(String username, String password);

    boolean Verificationlevel(String username);

    boolean Verificationlevel2(String username);

    boolean updatePassword(String username, String password, String password1);
}
