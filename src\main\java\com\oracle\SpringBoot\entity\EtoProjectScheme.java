package com.oracle.SpringBoot.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import lombok.Data;

/**
 * ETO项目方案实体类 - 对应新表eto_project_scheme
 */
@Data
@Table(name = "eto_project_scheme")
public class EtoProjectScheme {
    @Id
    private Long id;
    private Long projectId;         // 关联项目ID
    private String schemeName;      // 方案名称
    private String schemeDescription; // 方案描述
    private Boolean isReference;    // 是否为参考方案
    private String status;          // 状态
    private Date createTime;        // 创建时间
    private Date updateTime;        // 更新时间
    private String createBy;        // 创建人
    
    // 非数据库字段
    @Transient
    private List<EtoSchemeParam> params; // 方案参数列表
} 