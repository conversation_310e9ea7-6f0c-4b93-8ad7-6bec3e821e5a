package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 胎侧部件实体类
 */
@Data
@TableName("PartSidewall")
public class PartSidewall {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 工艺尺寸图
     */
    @TableField("ProcessSizeChart")
    private String processSizeChart;
    
    /**
     * 宽度
     */
    @TableField("Width")
    private BigDecimal width;
    
    /**
     * 耐磨胶料
     */
    @TableField("WearResistantCompound")
    private String wearResistantCompound;
    
    /**
     * 胎侧胶料
     */
    @TableField("SidewallCompound")
    private String sidewallCompound;
    
    /**
     * 结构尺寸图
     */
    @TableField("StructureSizeChart")
    private String structureSizeChart;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
