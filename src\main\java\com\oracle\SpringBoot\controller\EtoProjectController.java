package com.oracle.SpringBoot.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.oracle.SpringBoot.entity.EtoDraft;
import com.oracle.SpringBoot.entity.EtoProject;
import com.oracle.SpringBoot.entity.EtoConceptParam;
import com.oracle.SpringBoot.entity.EtoConceptCompetitor;
import com.oracle.SpringBoot.service.IEtoDraftService;
import com.oracle.SpringBoot.service.IEtoProjectService;
import com.oracle.SpringBoot.service.EtoConceptService;
import com.oracle.SpringBoot.vo.CompetitorFormVO;
import com.oracle.SpringBoot.vo.ConceptDataVO;
import com.oracle.SpringBoot.vo.TargetFormVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

@RestController
@RequestMapping({"/api", "/boot/api"})
public class EtoProjectController {
    
    private static final Logger logger = LoggerFactory.getLogger(EtoProjectController.class);
    
    @Autowired
    private IEtoProjectService etoProjectService;
    
    @Autowired
    private IEtoDraftService etoDraftService;
    
    @Autowired
    private EtoConceptService etoConceptService;
    
    @PostMapping("/eto-project")
    public Map<String, Object> createProject(@RequestBody EtoProject project, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 设置创建时间和用户
            project.setCreateTime(new Date());
            project.setUpdateTime(new Date());
            project.setCreateBy(username);
            
            // 设置默认状态
            if (project.getStatus() == null) {
                project.setStatus("pending");
            }
            
            boolean saved = etoProjectService.save(project);
            
            if(saved) {
                // 处理产品概念数据 - 使用新的表结构
                processConceptData(project, username);
                
                // 如果有草稿ID，删除对应草稿
                if (project.getDraftId() != null) {
                    etoDraftService.removeById(project.getDraftId());
                }
                
                response.put("code", 0);
                response.put("msg", "项目创建成功");
                response.put("data", project);
            } else {
                response.put("code", 1);
                response.put("msg", "项目创建失败");
            }
        } catch (Exception e) {
            logger.error("创建项目失败", e);
            response.put("code", 1);
            response.put("msg", "创建失败：" + e.getMessage());
        }
        return response;
    }
    
    @GetMapping("/eto-projects")
    public Map<String, Object> getProjects(
            @RequestParam(required = false) String projectNo,
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String size,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit
    ) {
        Map<String, Object> response = new HashMap<>();
        try {
            logger.debug("获取项目列表, 参数: page={}, limit={}, projectNo={}, projectName={}, size={}, status={}", 
                       page, limit, projectNo, projectName, size, status);
            
            // 构建查询条件
            LambdaQueryWrapper<EtoProject> queryWrapper = Wrappers.lambdaQuery(EtoProject.class);
            
            if (projectNo != null && !projectNo.isEmpty()) {
                queryWrapper.like(EtoProject::getProjectNo, projectNo);
            }
            
            if (projectName != null && !projectName.isEmpty()) {
                queryWrapper.like(EtoProject::getProjectName, projectName);
            }
            
            if (size != null && !size.isEmpty()) {
                queryWrapper.like(EtoProject::getSize, size);
            }
            
            if (status != null && !status.isEmpty()) {
                queryWrapper.eq(EtoProject::getStatus, status);
            }
            
            // 按创建时间倒序排序
            queryWrapper.orderByDesc(EtoProject::getCreateTime);
            
            // 手动计算总数 - 避免使用可能不兼容的count()方法
            int total = 0;
            try {
                // 获取所有记录列表（仅用于计数）
                List<EtoProject> allProjects = etoProjectService.list(queryWrapper);
                total = allProjects != null ? allProjects.size() : 0;
                logger.debug("获取项目总数: {}", total);
            } catch (Exception e) {
                logger.error("获取总数失败", e);
                // 如果获取总数失败，设置为0，但继续尝试获取分页数据
                total = 0;
            }
            
            // 手动添加分页条件
            int offset = (page - 1) * limit;
            // SQL Server分页写法
            queryWrapper.last("OFFSET " + offset + " ROWS FETCH NEXT " + limit + " ROWS ONLY");
            List<EtoProject> projects = etoProjectService.list(queryWrapper);
            logger.debug("获取项目列表结果数: {}", projects.size());
            
            // 组装返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("total", total);
            data.put("list", projects);
            
            response.put("code", 0);
            response.put("msg", "获取成功");
            response.put("data", data);
            logger.debug("查询完成，返回数据");
        } catch (Exception e) {
            logger.error("获取项目列表失败", e);
            response.put("code", 1);
            response.put("msg", "获取失败：" + e.getMessage());
        }
        return response;
    }
    
    @GetMapping("/eto-project/{id}")
    public Map<String, Object> getProjectDetail(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取项目详情
            EtoProject project = etoProjectService.getById(id);
            if (project == null) {
                throw new RuntimeException("项目不存在");
            }
            
            // 加载新版产品概念数据
            try {
                ConceptDataVO conceptData = etoConceptService.getProjectConceptData(id);
                project.setConceptData(conceptData);
                logger.info("项目[{}]加载产品概念数据：参数数量={}, 目标值数量={}, 竞品数量={}",
                          id, 
                          conceptData.getParams().size(),
                          conceptData.getTarget().size(),
                          conceptData.getCompetitors().size());
            } catch (Exception e) {
                logger.error("获取产品概念数据失败", e);
                // 发生异常时，设置空的概念数据
                project.setConceptData(new ConceptDataVO());
            }
            
            response.put("code", 0);
            response.put("msg", "获取成功");
            response.put("data", project);
        } catch (Exception e) {
            logger.error("获取项目详情失败", e);
            response.put("code", 1);
            response.put("msg", "获取失败：" + e.getMessage());
        }
        return response;
    }
    
    @PutMapping("/eto-project/{id}")
    public Map<String, Object> updateProject(@PathVariable Long id, @RequestBody EtoProject project, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 确保ID一致
            project.setId(id);
            project.setUpdateTime(new Date());
            project.setUpdateBy(username);
            
            boolean updated = etoProjectService.updateById(project);
            
            if (updated) {
                // 处理产品概念数据 - 使用新的表结构
                processConceptData(project, username);
                
                response.put("code", 0);
                response.put("msg", "项目更新成功");
            } else {
                response.put("code", 1);
                response.put("msg", "项目更新失败");
            }
        } catch (Exception e) {
            logger.error("更新项目失败", e);
            response.put("code", 1);
            response.put("msg", "更新失败：" + e.getMessage());
        }
        return response;
    }
    
    /**
     * 处理产品概念数据，保存到新表结构
     */
    private void processConceptData(EtoProject project, String username) {
        if (project.getConceptData() == null) {
            return;
        }
        
        try {
            Long projectId = project.getId();
            Object conceptDataObj = project.getConceptData();
            Map<String, Object> conceptData;
            
            if (conceptDataObj instanceof Map) {
                conceptData = (Map<String, Object>) conceptDataObj;
            } else {
                logger.error("产品概念数据类型错误: {}", conceptDataObj.getClass().getName());
                return;
            }
            
            // 1. 处理参数
            List<Map<String, Object>> params = (List<Map<String, Object>>) conceptData.get("params");
            if (params != null && !params.isEmpty()) {
                Map<String, EtoConceptParam> paramMap = new HashMap<>();
                
                for (Map<String, Object> paramData : params) {
                    String paramName = (String) paramData.get("paramName");
                    if (paramName == null || paramName.isEmpty()) {
                        continue;
                    }
                    
                    EtoConceptParam param = new EtoConceptParam();
                    param.setProjectId(projectId);
                    param.setParamName(paramName);
                    param.setCreateBy(username);
                    param.setUpdateBy(username);
                    
                    // 保存参数并获取ID
                    Long paramId = etoConceptService.addParam(param);
                    param.setId(paramId);
                    paramMap.put(paramName, param);
                }
                
                // 2. 处理目标值
                Map<String, String> targetValues = new HashMap<>();
                Object targetObj = conceptData.get("target");
                if (targetObj instanceof Map) {
                    targetValues = (Map<String, String>) targetObj;
                }
                
                if (targetValues != null && !targetValues.isEmpty()) {
                    TargetFormVO targetForm = new TargetFormVO();
                    targetForm.setProjectId(projectId);
                    
                    Map<Long, String> formattedValues = new HashMap<>();
                    for (Map.Entry<String, String> entry : targetValues.entrySet()) {
                        try {
                            Long paramId = Long.parseLong(entry.getKey());
                            String value = entry.getValue();
                            formattedValues.put(paramId, value);
                        } catch (NumberFormatException e) {
                            logger.error("目标值参数ID格式错误: {}", entry.getKey());
                        }
                    }
                    
                    targetForm.setTargetValues(formattedValues);
                    etoConceptService.setTargetValues(targetForm);
                }
                
                // 3. 处理竞品
                List<Map<String, Object>> competitors = (List<Map<String, Object>>) conceptData.get("competitors");
                if (competitors != null && !competitors.isEmpty()) {
                    for (Map<String, Object> competitorData : competitors) {
                        String competitorName = (String) competitorData.get("name");
                        Map<String, String> values = (Map<String, String>) competitorData.get("values");
                        
                        if (competitorName == null || competitorName.isEmpty() || values == null || values.isEmpty()) {
                            continue;
                        }
                        
                        CompetitorFormVO competitorForm = new CompetitorFormVO();
                        competitorForm.setProjectId(projectId);
                        competitorForm.setCompetitorName(competitorName);
                        
                        List<CompetitorFormVO.ParamValueVO> paramValues = new ArrayList<>();
                        for (Map.Entry<String, String> entry : values.entrySet()) {
                            try {
                                Long paramId = Long.parseLong(entry.getKey());
                                String value = entry.getValue();
                                
                                CompetitorFormVO.ParamValueVO paramValue = new CompetitorFormVO.ParamValueVO();
                                paramValue.setParamId(paramId);
                                paramValue.setValue(value);
                                paramValues.add(paramValue);
                            } catch (NumberFormatException e) {
                                logger.error("竞品值参数ID格式错误: {}", entry.getKey());
                            }
                        }
                        
                        competitorForm.setParamValues(paramValues);
                        etoConceptService.addCompetitor(competitorForm);
                    }
                }
                
                logger.info("项目[{}]产品概念数据保存完成: {} 个参数", projectId, paramMap.size());
            }
            
        } catch (Exception e) {
            logger.error("处理产品概念数据失败", e);
            throw e;
        }
    }
    
    @DeleteMapping("/eto-project/{id}")
    public Map<String, Object> deleteProject(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 删除产品概念数据
            deleteConceptData(id);
            
            // 再删除项目
            boolean removed = etoProjectService.removeById(id);
            
            if(removed) {
                response.put("code", 0);
                response.put("msg", "项目删除成功");
            } else {
                response.put("code", 1);
                response.put("msg", "项目删除失败");
            }
        } catch (Exception e) {
            logger.error("删除项目失败", e);
            response.put("code", 1);
            response.put("msg", "删除失败：" + e.getMessage());
        }
        return response;
    }
    
    @GetMapping("/eto-projects/by-eto-main/{etoMainId}")
    public Map<String, Object> getProjectByEtoMainId(@PathVariable Long etoMainId) {
        Map<String, Object> response = new HashMap<>();
        try {
            EtoProject project = etoProjectService.getProjectByEtoMainId(etoMainId);
            response.put("code", 0);
            response.put("msg", "获取成功");
            response.put("data", project);
        } catch (Exception e) {
            logger.error("获取项目失败", e);
            response.put("code", 1);
            response.put("msg", "获取失败：" + e.getMessage());
        }
        return response;
    }
    
    @DeleteMapping("/eto-projects/batch")
    public Map<String, Object> batchDeleteProjects(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            if (ids == null || ids.isEmpty()) {
                response.put("code", 1);
                response.put("msg", "请选择要删除的项目");
                return response;
            }
            
            // 删除产品概念数据
            for (Long id : ids) {
                deleteConceptData(id);
            }
            
            // 再删除项目
            boolean removed = etoProjectService.removeByIds(ids);
            
            if(removed) {
                response.put("code", 0);
                response.put("msg", "批量删除成功");
            } else {
                response.put("code", 1);
                response.put("msg", "批量删除失败");
            }
        } catch (Exception e) {
            logger.error("批量删除项目失败", e);
            response.put("code", 1);
            response.put("msg", "删除失败：" + e.getMessage());
        }
        return response;
    }
    
    /**
     * 删除项目相关的概念数据
     */
    private void deleteConceptData(Long projectId) {
        try {
            // 1. 获取项目相关的竞品
            LambdaQueryWrapper<EtoConceptCompetitor> competitorWrapper = new LambdaQueryWrapper<>();
            competitorWrapper.eq(EtoConceptCompetitor::getProjectId, projectId);
            List<EtoConceptCompetitor> competitors = etoConceptService.getCompetitorsByProjectId(projectId);
            
            // 2. 删除竞品参数值
            if (!competitors.isEmpty()) {
                List<Long> competitorIds = competitors.stream()
                    .map(EtoConceptCompetitor::getId)
                    .collect(Collectors.toList());
                
                etoConceptService.deleteCompetitorValues(competitorIds);
            }
            
            // 3. 删除竞品
            etoConceptService.deleteCompetitors(projectId);
            
            // 4. 删除目标值
            etoConceptService.deleteTargets(projectId);
            
            // 5. 删除参数
            etoConceptService.deleteParams(projectId);
            
            logger.info("项目[{}]的产品概念数据已删除", projectId);
        } catch (Exception e) {
            logger.error("删除项目[{}]的产品概念数据失败", projectId, e);
            // 继续抛出异常，让上层处理
            throw e;
        }
    }
} 