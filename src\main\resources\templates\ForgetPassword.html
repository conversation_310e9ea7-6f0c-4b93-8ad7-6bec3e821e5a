<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
            color: #1e1e2d;
        }

        
        .forget-password-container {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 420px;
            padding: 40px;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .form-header h2 {
            font-size: 24px;
            color: #1e1e2d;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .form-header p {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 32px;
            position: relative;
        }

        .step-indicator::before {
            content: '';
            position: absolute;
            top: 14px;
            left: 30px;
            right: 30px;
            height: 2px;
            background: #e2e8f0;
            z-index: 1;
        }

        .step {
            position: relative;
            z-index: 2;
            background: #fff;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #666;
            border: 2px solid #e2e8f0;
            transition: all 0.3s;
        }

        .step.active {
            color: #fff;
            background: #3B82F6;
            border-color: #3B82F6;
        }

        .step.completed {
            background: #10b981;
            border-color: #10b981;
            color: #fff;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group .layui-input {
            height: 44px;
            line-height: 44px;
            padding: 0 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.3s;
        }

        .form-group .layui-input:focus {
            border-color: #3B82F6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .form-group .layui-input:hover {
            border-color: #3B82F6;
        }

        .verification-code {
            display: flex;
            gap: 12px;
        }

        .verification-code .layui-input {
            flex: 1;
        }

        .verification-code .layui-btn {
            height: 44px;
            padding: 0 20px;
            font-size: 14px;
            border-radius: 6px;
            background: #3B82F6;
            transition: all 0.3s;
        }

        .verification-code .layui-btn:hover {
            background: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .verification-code .layui-btn[disabled] {
            background: #e2e8f0;
            cursor: not-allowed;
        }

        .submit-btn {
            width: 100%;
            height: 44px;
            line-height: 44px;
            background: #3B82F6;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .submit-btn:hover {
            background: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .form-footer {
            text-align: center;
            margin-top: 24px;
            font-size: 14px;
            color: #666;
        }

        .form-footer a {
            color: #3B82F6;
            text-decoration: none;
            transition: color 0.3s;
        }

        .form-footer a:hover {
            color: #2563eb;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .success-icon {
            font-size: 48px;
            color: #10b981;
            margin-bottom: 16px;
        }

        .error-message {
            color: #dc2626;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="forget-password-container">
        <div class="form-header">
            <h2>找回密码</h2>
            <p>请按照步骤操作，我们将帮助您重置密码</p>
        </div>

        <div class="step-indicator">
            <div class="step active">1</div>
            <div class="step">2</div>
            <div class="step">3</div>
        </div>

        <form class="layui-form">
            <!-- 步骤1：验证身份 -->
            <div class="step-content active" id="step1">
                <div class="form-group">
                    <input type="text" name="username" required lay-verify="required" 
                           placeholder="请输入用户名" class="layui-input">
                </div>
                <div class="form-group">
                    <input type="email" name="email" required lay-verify="required|email" 
                           placeholder="请输入注册邮箱" class="layui-input">
                </div>
                <div class="form-group verification-code">
                    <input type="text" name="verificationCode" required lay-verify="required" 
                           placeholder="请输入验证码" class="layui-input">
                    <button type="button" class="layui-btn" id="sendCode">获取验证码</button>
                </div>
                <button type="button" class="layui-btn submit-btn" onclick="nextStep(2)">下一步</button>
            </div>

            <!-- 步骤2：重置密码 -->
            <div class="step-content" id="step2">
                <div class="form-group">
                    <input type="password" name="newPassword" required lay-verify="required|password" 
                           placeholder="请输入新密码" class="layui-input">
                    <div class="error-message">密码必须包含大小写字母、数字和特殊字符</div>
                </div>
                <div class="form-group">
                    <input type="password" name="confirmPassword" required lay-verify="required|confirmPassword" 
                           placeholder="请确认新密码" class="layui-input">
                    <div class="error-message">两次输入的密码不一致</div>
                </div>
                <button type="button" class="layui-btn submit-btn" onclick="nextStep(3)">确认修改</button>
            </div>

            <!-- 步骤3：完成 -->
            <div class="step-content" id="step3">
                <div style="text-align: center;">
                    <i class="fas fa-check-circle success-icon"></i>
                    <h3 style="margin-bottom: 16px;">密码重置成功</h3>
                    <p style="color: #666; margin-bottom: 24px;">您的密码已经成功重置，请使用新密码登录</p>
                    <button type="button" class="layui-btn submit-btn" onclick="window.location.href='login'">
                        返回登录
                    </button>
                </div>
            </div>
        </form>

        <div class="form-footer">
            <a href="login">返回登录</a>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 验证码倒计时
        var countdown = 60;
        var timer = null;
        $('#sendCode').on('click', function(){
            var email = $('input[name="email"]').val();
            if(!email) {
                layer.msg('请先输入邮箱地址');
                return;
            }
            if(!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(email)) {
                layer.msg('请输入有效的邮箱地址');
                return;
            }

            var btn = $(this);
            btn.attr('disabled', true);
            timer = setInterval(function(){
                if(countdown === 0) {
                    btn.removeAttr('disabled').text('重新获取');
                    countdown = 60;
                    clearInterval(timer);
                } else {
                    btn.text(countdown + '秒后重试');
                    countdown--;
                }
            }, 1000);

            // 这里添加发送验证码的Ajax请求
            $.ajax({
                url: 'sendVerificationCode',
                type: 'POST',
                data: {email: email},
                success: function(res){
                    layer.msg('验证码已发送到您的邮箱');
                },
                error: function(){
                    layer.msg('验证码发送失败，请重试');
                    btn.removeAttr('disabled').text('获取验证码');
                    countdown = 60;
                    clearInterval(timer);
                }
            });
        });

        // 自定义验证规则
        form.verify({
            email: function(value){
                if(!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(value)){
                    return '请输入有效的邮箱地址';
                }
            },
            password: function(value){
                if(value.length < 8){
                    return '密码长度不能小于8个字符';
                }
                if(!/(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])/.test(value)){
                    return '密码必须包含大小写字母、数字和特殊字符';
                }
            },
            confirmPassword: function(value){
                var password = $('input[name="newPassword"]').val();
                if(value !== password){
                    return '两次输入的密码不一致';
                }
            }
        });
    });

    // 步骤切换
    function nextStep(step) {
        var form = layui.form;
        var $ = layui.$;

        // 验证当前步骤的表单
        var currentInputs = $('#step' + (step-1) + ' input[lay-verify]');
        var isValid = true;
        currentInputs.each(function(){
            var verify = $(this).attr('lay-verify').split('|');
            for(var i = 0; i < verify.length; i++) {
                var errorMsg = form.config.verify[verify[i]]($(this).val());
                if(errorMsg) {
                    layer.msg(errorMsg);
                    isValid = false;
                    return false;
                }
            }
        });

        if(!isValid) return;

        // 如果是最后一步，提交表单
        if(step === 3) {
            // 这里添加提交表单的Ajax请求
            $.ajax({
                url: 'resetPassword',
                type: 'POST',
                data: {
                    username: $('input[name="username"]').val(),
                    email: $('input[name="email"]').val(),
                    verificationCode: $('input[name="verificationCode"]').val(),
                    newPassword: $('input[name="newPassword"]').val()
                },
                success: function(res){
                    showStep(step);
                },
                error: function(){
                    layer.msg('密码重置失败，请重试');
                }
            });
        } else {
            showStep(step);
        }
    }

    function showStep(step) {
        $('.step-content').removeClass('active');
        $('#step' + step).addClass('active');
        
        $('.step').removeClass('active completed');
        for(var i = 1; i < step; i++) {
            $('.step:nth-child(' + i + ')').addClass('completed');
        }
        $('.step:nth-child(' + step + ')').addClass('active');
    }
    </script>
</body>
</html>