package com.oracle.SpringBoot.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.oracle.SpringBoot.entity.EtoDraft;
import com.oracle.SpringBoot.entity.EtoProject;
import com.oracle.SpringBoot.service.IEtoDraftService;
import com.oracle.SpringBoot.service.IEtoProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * ETO草稿控制器
 */
@RestController
@RequestMapping({"/api", "/boot/api"})
public class EtoDraftController {

    @Autowired
    private IEtoDraftService etoDraftService;
    
    @Autowired
    private IEtoProjectService etoProjectService;
    
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 保存草稿
     */
    @PostMapping("/eto-draft")
    public Map<String, Object> saveDraft(@RequestBody Map<String, Object> draftData, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 创建草稿实体
            EtoDraft draft = new EtoDraft();
            
            // 将整个表单数据转换为JSON字符串
            String jsonData = objectMapper.writeValueAsString(draftData);
            draft.setDraftData(jsonData);
            
            // 如果是已有项目的草稿，设置项目ID
            if (draftData.containsKey("id") && draftData.get("id") != null) {
                draft.setProjectId(Long.valueOf(String.valueOf(draftData.get("id"))));
            }
            
            // 设置创建信息
            draft.setCreateBy(username);
            draft.setCreateTime(new Date());
            draft.setUpdateTime(new Date());
            
            // 保存草稿
            etoDraftService.save(draft);
            
            // 返回草稿ID
            Map<String, Object> result = new HashMap<>();
            result.put("id", draft.getId());
            
            response.put("code", 0);
            response.put("msg", "保存草稿成功");
            response.put("data", result);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            response.put("code", 1);
            response.put("msg", "保存草稿失败：" + e.getMessage());
            return response;
        }
    }

    /**
     * 获取草稿
     */
    @GetMapping("/eto-draft/{id}")
    public Map<String, Object> getDraft(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            EtoDraft draft = etoDraftService.getById(id);
            if (draft == null) {
                response.put("code", 1);
                response.put("msg", "草稿不存在");
                return response;
            }
            
            // 将JSON字符串解析为Map
            Map<String, Object> draftData = objectMapper.readValue(draft.getDraftData(), Map.class);
            
            response.put("code", 0);
            response.put("msg", "获取草稿成功");
            response.put("data", draftData);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            response.put("code", 1);
            response.put("msg", "获取草稿失败：" + e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取最新草稿
     */
    @GetMapping("/eto-draft/latest")
    public Map<String, Object> getLatestDraft(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 查询当前用户的最新草稿
            LambdaQueryWrapper<EtoDraft> queryWrapper = Wrappers.lambdaQuery(EtoDraft.class)
                .eq(EtoDraft::getCreateBy, username)
                .orderByDesc(EtoDraft::getCreateTime);
            
            // 取第一条记录而不是使用 LIMIT 1
            List<EtoDraft> drafts = etoDraftService.list(queryWrapper);
            EtoDraft draft = drafts.isEmpty() ? null : drafts.get(0);
            
            if (draft == null) {
                response.put("code", 0);
                response.put("msg", "没有找到草稿");
                response.put("data", null);
                return response;
            }
            
            // 将JSON字符串解析为Map
            Map<String, Object> draftData = objectMapper.readValue(draft.getDraftData(), Map.class);
            draftData.put("id", draft.getId()); // 添加草稿ID
            
            response.put("code", 0);
            response.put("msg", "获取最新草稿成功");
            response.put("data", draftData);
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            response.put("code", 1);
            response.put("msg", "获取最新草稿失败：" + e.getMessage());
            return response;
        }
    }
} 