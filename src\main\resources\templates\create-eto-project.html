<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建ETO项目 - ETO线上系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .project-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #EBEEF5;
        }

        .header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .header h2 i {
            margin-right: 8px;
            color: #409EFF;
        }

        .project-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .project-table th, .project-table td {
            border: 1px solid #EBEEF5;
            padding: 12px;
            text-align: left;
        }

        .project-table th {
            background-color: #F5F7FA;
            font-weight: bold;
            width: 150px;
            color: #606266;
        }

        .project-table td {
            background-color: #fff;
        }

        .project-table input[type="text"], 
        .project-table select,
        .project-table textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .project-table input[type="text"]:focus,
        .project-table select:focus,
        .project-table textarea:focus {
            border-color: #409EFF;
            outline: none;
        }

        .project-table textarea {
            min-height: 80px;
            resize: vertical;
        }

        .required::before {
            content: "*";
            color: #F56C6C;
            margin-right: 4px;
        }

        .target-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .target-table th, .target-table td {
            border: 1px solid #EBEEF5;
            padding: 8px;
            text-align: center;
        }

        .target-table th {
            background-color: #F5F7FA;
            font-weight: bold;
            color: #606266;
        }

        .target-table input[type="text"] {
            width: 100%;
            padding: 6px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            text-align: center;
        }

        .action-buttons {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #EBEEF5;
        }

        .layui-btn {
            margin: 0 10px;
            padding: 0 25px;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .layui-btn i {
            margin-right: 8px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
            padding: 15px;
            background: #F5F7FA;
            border-radius: 4px;
        }

        .info-item {
            flex: 1;
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .info-item:last-child {
            margin-right: 0;
        }

        .info-label {
            color: #606266;
            margin-right: 10px;
            font-weight: 500;
            width: 100px;
        }

        .info-content {
            flex: 1;
        }

        .info-content .layui-input,
        .info-content select {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            color: #606266;
            background-color: #fff;
        }

        .info-content select {
            appearance: none;
            -webkit-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23606266'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 16px;
            padding-right: 30px;
        }

        .info-content select:focus {
            border-color: #409EFF;
            outline: none;
        }

        .info-value {
            color: #333;
        }

        .concept-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .concept-table th, .concept-table td {
            border: 1px solid #EBEEF5;
            padding: 8px;
            text-align: center;
        }

        .concept-table th {
            background-color: #F5F7FA;
            position: relative;
            font-weight: bold;
            color: #606266;
        }

        .concept-table .target {
            color: red;
        }

        .table-actions {
            margin-bottom: 10px;
        }

        .delete-column {
            position: absolute;
            top: 0;
            right: 0;
            padding: 2px 5px;
            color: #F56C6C;
            cursor: pointer;
            font-size: 12px;
        }

        .tire-segment {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tire-segment span {
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .tire-segment .selected {
            background-color: #409EFF;
            color: white;
        }

        .fixed-column {
            background-color: #F5F7FA !important;
        }

        .uploaded-file {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            background: #F5F7FA;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .uploaded-file i {
            margin-right: 5px;
        }

        .uploaded-file .fa-times {
            margin-left: auto;
            cursor: pointer;
            color: #F56C6C;
        }
        
        .layui-input,
        .layui-select,
        .layui-textarea {
            border: 1px solid #DCDFE6;
            border-radius: 4px;
            padding: 8px 12px;
            width: 100%;
            transition: all 0.3s;
        }

        .layui-input:focus,
        .layui-select:focus,
        .layui-textarea:focus {
            border-color: #409EFF;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="project-container">
            <div class="header">
                <h2><i class="fas fa-project-diagram"></i>创建ETO项目</h2>
            </div>

            <div class="info-row">
                <div class="info-item">
                    <span class="info-label">Applicant:</span>
                    <div class="info-content">
                        <input type="text" name="applicant" class="layui-input">
                    </div>
                </div>
                <div class="info-item">
                    <span class="info-label">Apply Time:</span>
                    <div class="info-content">
                        <input type="text" name="applyTime" class="layui-input" id="applyTime">
                    </div>
                </div>
                <div class="info-item">
                    <span class="info-label">Department:</span>
                    <div class="info-content">
                        <select name="department" lay-verify="required" class="layui-input">
                            <option value="">Select Department</option>
                            <option value="RD">R&D Department</option>
                            <option value="PM">Project Management</option>
                            <option value="QA">Quality Assurance</option>
                        </select>
                    </div>
                </div>
            </div>

            <form class="layui-form" lay-filter="projectForm">
                <table class="project-table">
                    <tr>
                        <th>Project No.</th>
                        <td><input type="text" name="projectNo" class="layui-input"></td>
                        <th>Basis</th>
                        <td><input type="text" name="basis" class="layui-input"></td>
                    </tr>
                    <tr>
                        <th>Project Name</th>
                        <td><input type="text" name="projectName" class="layui-input" value="China market"></td>
                        <th>Purpose</th>
                        <td>
                            <select name="purpose" lay-verify="required">
                                <option value="RE">RE</option>
                                <option value="OE">OE</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>Tire Group</th>
                        <td>
                            <select name="tireGroup" lay-verify="required">
                                <option value="SUV">SUV</option>
                                <option value="PCR">PCR</option>
                                <option value="TBR">TBR</option>
                            </select>
                        </td>
                        <th>OE Maker</th>
                        <td><input type="text" name="oeMaker" class="layui-input"></td>
                    </tr>
                    <tr>
                        <th>Market</th>
                        <td>
                            <select name="market" lay-verify="required">
                                <option value="China">China</option>
                                <option value="Europe">Europe</option>
                                <option value="America">America</option>
                            </select>
                        </td>
                        <th>Tire Segment</th>
                        <td>
                            <div class="tire-segment">
                                <span class="selected">SUV</span>
                                <span>Passenger</span>
                                <span>Van</span>
                                <span>Pick Up</span>
                                <span>ETC</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th>Size</th>
                        <td><input type="text" name="size" class="layui-input" value="255/50R20"></td>
                        <th>Product Name</th>
                        <td><input type="text" name="productName" class="layui-input"></td>
                    </tr>
                    <tr>
                        <th>Brand</th>
                        <td>
                            <select name="brand" lay-verify="required">
                                <option value="LL">LL</option>
                                <option value="ATLAS">ATLAS</option>
                            </select>
                        </td>
                        <th>Plant</th>
                        <td>
                            <select name="plant" lay-verify="required">
                                <option value="Zhao yuan">Zhao yuan</option>
                                <option value="PCR">PCR</option>
                                <option value="PCR5">PCR5</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th>Purpose of Development</th>
                        <td colspan="3">
                            <textarea class="layui-textarea" name="purposeOfDevelopment" rows="3" placeholder="Enter development purposes"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <th>Product Concept & Strategy</th>
                        <td colspan="3">
                            <div class="table-actions">
                                <button type="button" class="layui-btn layui-btn-sm" id="addColumn">
                                    <i class="fas fa-plus"></i> 添加参数
                                </button>
                                <button type="button" class="layui-btn layui-btn-sm" id="addCompetitor">
                                    <i class="fas fa-plus"></i> 添加竞品
                                </button>
                            </div>
                            <table class="concept-table" id="dynamicTable">
                                <tr>
                                    <th>Items</th>
                                    <th class="fixed-column">RRc</th>
                                    <th class="fixed-column">Wet Grip</th>
                                    <th class="fixed-column">PBN</th>
                                    <th>Wet Braking<span class="delete-column">&times;</span></th>
                                    <th>Wet Handling<span class="delete-column">&times;</span></th>
                                    <th>Dry Braking<span class="delete-column">&times;</span></th>
                                    <th>Dry Handling<span class="delete-column">&times;</span></th>
                                    <th>NVH<span class="delete-column">&times;</span></th>
                                    <th>Comfort<span class="delete-column">&times;</span></th>
                                </tr>
                                <tr>
                                    <td>Target</td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                </tr>
                                <tr class="competitor-row">
                                    <td><input type="text" class="layui-input" placeholder="Competitor Name"><span class="delete-competitor">&times;</span></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                    <td><input type="text" class="layui-input"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>

                <div class="action-buttons">
                    <button type="button" class="layui-btn layui-btn-primary" id="saveDraft">
                        <i class="fas fa-save"></i> 保存草稿
                    </button>
                    <button type="submit" class="layui-btn" lay-submit lay-filter="submitProject">
                        <i class="fas fa-check"></i> 创建项目
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer', 'laydate', 'upload'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var $ = layui.$;

        // 初始化所有日期选择器
        laydate.render({
            elem: '#applyTime',
            value: new Date().toLocaleDateString()
        });
        
        // 设置基本信息
        $('#applicant').text(layui.sessionData('user').username || '未登录');
        $('#applyTime').text(new Date().toLocaleDateString());
        $('#department').text(layui.sessionData('user').department || '未知部门');

        // 添加参数列
        $('#addColumn').on('click', function(){
            layer.prompt({
                title: '请输入参数名称',
                formType: 0
            }, function(value, index){
                // 添加新列到表头
                var headerCell = $('<th>').html(value + '<span class="delete-column">&times;</span>');
                $('#dynamicTable tr:first').append(headerCell);
                
                // 为Target行添加输入框
                $('#dynamicTable tr:eq(1)').append('<td><input type="text" class="layui-input"></td>');
                
                // 为所有竞品行添加输入框
                $('#dynamicTable tr.competitor-row').each(function(){
                    $(this).append('<td><input type="text" class="layui-input"></td>');
                });
                
                layer.close(index);
            });
        });

        // 添加竞品行
        $('#addCompetitor').on('click', function(){
            var columnCount = $('#dynamicTable tr:first th').length - 1; // 减去Items列
            var newRow = $('<tr class="competitor-row">');
            newRow.append('<td><input type="text" class="layui-input" placeholder="Competitor Name"><span class="delete-competitor">&times;</span></td>');
            for(var i = 0; i < columnCount; i++){
                newRow.append('<td><input type="text" class="layui-input"></td>');
            }
            $('#dynamicTable').append(newRow);
        });

        // 删除列功能
        $(document).on('click', '.delete-column', function(e){
            e.stopPropagation();
            var columnIndex = $(this).parent().index();
            var isFixedColumn = $(this).parent().hasClass('fixed-column');
            
            // 不允许删除Items列和固定列（RRc、Wet Grip、PBN）
            if(columnIndex > 0 && !isFixedColumn) {
                $('#dynamicTable tr').each(function(){
                    $(this).find('th,td').eq(columnIndex).remove();
                });
            }
        });

        // 删除竞品行
        $(document).on('click', '.delete-competitor', function(){
            $(this).closest('tr').remove();
        });

        // Tire Segment 选择
        $('.tire-segment span').on('click', function(){
            $('.tire-segment span').removeClass('selected');
            $(this).addClass('selected');
        });

        // 保存草稿
        $('#saveDraft').on('click', function(){
            var formData = form.val('projectForm');
            formData.tireSegment = $('.tire-segment .selected').text();
            formData.productConcept = getConceptTableData();
            
            $.ajax({
                url: '/api/eto-project/draft',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(res){
                    if(res.code === 0) {
                        layer.msg('草稿保存成功');
                    } else {
                        layer.msg(res.msg || '保存失败，请重试');
                    }
                },
                error: function(){
                    layer.msg('系统错误，请重试');
                }
            });
        });

        // 提交表单
        form.on('submit(submitProject)', function(data){
            data.field.tireSegment = $('.tire-segment .selected').text();
            data.field.productConcept = getConceptTableData();
            
            $.ajax({
                url: '/api/eto-project',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function(res){
                    if(res.code === 0) {
                        layer.msg('项目创建成功', {
                            time: 2000
                        }, function(){
                            window.location.href = '/eto-projects';
                        });
                    } else {
                        layer.msg(res.msg || '创建失败，请重试');
                    }
                },
                error: function(){
                    layer.msg('系统错误，请重试');
                }
            });
            
            return false;
        });

        // 获取概念表格数据
        function getConceptTableData() {
            var tableData = {
                headers: [],
                target: [],
                competitors: []
            };
            
            // 收集表头
            $('#dynamicTable th:not(:first)').each(function(){
                tableData.headers.push($(this).text().replace('×', '').trim());
            });
            
            // 收集Target行
            $('#dynamicTable tr:eq(1) td:not(:first) input').each(function(){
                tableData.target.push($(this).val());
            });
            
            // 收集所有竞品行
            $('#dynamicTable tr.competitor-row').each(function(){
                var competitor = {
                    name: $(this).find('td:first input').val(),
                    values: []
                };
                $(this).find('td:not(:first) input').each(function(){
                    competitor.values.push($(this).val());
                });
                if(competitor.name) { // 只添加有名称的
                    tableData.competitors.push(competitor);
                }
            });
            
            return tableData;
        }

        // 表单验证
        form.verify({
            required: function(value, item){
                if(!value){
                    return '必填项不能为空';
                }
            }
        });                                                                                            
    });
    </script>
</body>
</html> 