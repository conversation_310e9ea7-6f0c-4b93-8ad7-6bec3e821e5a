package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 内衬层部件实体类
 */
@Data
@TableName("PartInnerLiner")
public class PartInnerLiner {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 气密层宽度
     */
    @TableField("AirTightLayerWidth")
    private BigDecimal airTightLayerWidth;
    
    /**
     * 过渡层宽度
     */
    @TableField("TransitionLayerWidth")
    private BigDecimal transitionLayerWidth;
    
    /**
     * 气密层厚度
     */
    @TableField("AirTightLayerThickness")
    private BigDecimal airTightLayerThickness;
    
    /**
     * 过渡层厚度
     */
    @TableField("TransitionLayerThickness")
    private BigDecimal transitionLayerThickness;
    
    /**
     * 过渡层胶料
     */
    @TableField("TransitionLayerCompound")
    private String transitionLayerCompound;
    
    /**
     * 气密层胶料
     */
    @TableField("AirTightLayerCompound")
    private String airTightLayerCompound;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
