package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoConceptCompetitor;
import com.oracle.SpringBoot.entity.EtoConceptParam;
import com.oracle.SpringBoot.vo.CompetitorFormVO;
import com.oracle.SpringBoot.vo.ConceptDataVO;
import com.oracle.SpringBoot.vo.TargetFormVO;

import java.util.List;

/**
 * ETO概念服务接口
 */
public interface EtoConceptService {
    
    /**
     * 添加参数
     *
     * @param param 参数信息
     * @return 参数ID
     */
    Long addParam(EtoConceptParam param);
    
    /**
     * 获取项目参数列表
     *
     * @param projectId 项目ID
     * @return 参数列表
     */
    List<EtoConceptParam> getProjectParams(Long projectId);
    
    /**
     * 设置目标值
     *
     * @param targetForm 目标值表单
     */
    void setTargetValues(TargetFormVO targetForm);
    
    /**
     * 添加竞品及其参数值
     *
     * @param competitorForm 竞品表单
     * @return 竞品ID
     */
    Long addCompetitor(CompetitorFormVO competitorForm);
    
    /**
     * 获取项目概念数据
     *
     * @param projectId 项目ID
     * @return 概念数据
     */
    ConceptDataVO getProjectConceptData(Long projectId);
    
    /**
     * 根据项目ID获取竞品列表
     *
     * @param projectId 项目ID
     * @return 竞品列表
     */
    List<EtoConceptCompetitor> getCompetitorsByProjectId(Long projectId);
    
    /**
     * 删除竞品参数值
     *
     * @param competitorIds 竞品ID列表
     */
    void deleteCompetitorValues(List<Long> competitorIds);
    
    /**
     * 删除项目的所有竞品
     *
     * @param projectId 项目ID
     */
    void deleteCompetitors(Long projectId);
    
    /**
     * 删除项目的所有目标值
     *
     * @param projectId 项目ID
     */
    void deleteTargets(Long projectId);
    
    /**
     * 删除项目的所有参数
     *
     * @param projectId 项目ID
     */
    void deleteParams(Long projectId);
    
    /**
     * 根据ID获取参数
     *
     * @param paramId 参数ID
     * @return 参数信息
     */
    EtoConceptParam getParamById(Long paramId);
    
    /**
     * 根据ID删除参数
     *
     * @param paramId 参数ID
     */
    void deleteParamById(Long paramId);
} 