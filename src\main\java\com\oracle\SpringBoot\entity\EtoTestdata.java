package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ETO测试数据实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_Testdata")
public class EtoTestdata implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("SchemeId")
    private Integer schemeId;
    
    @TableField("MainGrooveBasePerimeter")
    private String mainGrooveBasePerimeter;
    
    @TableField("CalculatedBeadWidth")
    private String calculatedBeadWidth;
    
    @TableField("CuredBeadWidth")
    private String curedBeadWidth;
    
    @TableField("TreadColorLine")
    private String treadColorLine;
    
    @TableField("CalculatedWeight")
    private String calculatedWeight;
    
    @TableField("RiskIdentification")
    private String riskIdentification;
    
    @TableField("Confirmation")
    private String confirmation;
    
    @TableField("Comment")
    private String comment;
    
    @TableField("CreateTime")
    private Date createTime;
    
    @TableField("UpdateTime")
    private Date updateTime;
}
