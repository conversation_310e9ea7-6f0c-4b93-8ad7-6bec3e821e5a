package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ETO胶料信息实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_RubberInfo")
public class EtoRubberinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("Code")
    private String code;

    @TableField("Plant")
    private String plant;

    @TableField("SchemeId")
    private Integer schemeId;

    @TableField("InnerLinerType1")
    private String innerLinerType1;

    @TableField("InnerLinerThickness1")
    private String innerLinerThickness1;

    @TableField("InnerLinerWidth1")
    private String innerLinerWidth1;

    @TableField("InnerLinerType2")
    private String innerLinerType2;

    @TableField("InnerLinerThickness2")
    private String innerLinerThickness2;

    @TableField("InnerLinerWidth2")
    private String innerLinerWidth2;

    @TableField("SidewallCompound1")
    private String sidewallCompound1;

    @TableField("SidewallCompound2")
    private String sidewallCompound2;

    @TableField("BeadFillerCompound")
    private String beadFillerCompound;

    @TableField("BeadFillerHT")
    private String beadFillerHT;

    @TableField("TreadCompound")
    private String treadCompound;

    @TableField("TreadWing")
    private String treadWing;

    @TableField("TreadBaseType")
    private String treadBaseType;

    @TableField("TreadBaseThickness")
    private String treadBaseThickness;

    @TableField("TreadChimney")
    private String treadChimney;

    @TableField("SidewallDieCode")
    private String sidewalldiecode;

    @TableField("SidewallDieSize")
    private String sidewalldiesize;

    @TableField("SidewallDieRemark")
    private String sidewalldieremark;

    @TableField("SidewallSetting")
    private Double sidewallsetting;

    @TableField("ApexDieCode")
    private String apexdiecode;

    @TableField("ApexDieRemark")
    private String apexdieremark;

    @TableField("TreadDieSymmetry")
    private Boolean treaddiesymmetry;

    @TableField("TreadDieDissymmetric")
    private Boolean treaddiedissymmetric;

    @TableField("TreadDieCode")
    private String treaddiecode;

    @TableField("TreadDieRemark")
    private String treaddieremark;

    @TableField("MainGrooveGauge")
    private Double maingroovegauge;

    @TableField("TreadCenterGaugeTheoryMin")
    private Double treadcentergaugetheorymin;

    @TableField("TreadCenterGaugeTheoryMax")
    private Double treadcentergaugetheorymax;

    @TableField("TreadCenterGaugeActual")
    private Double treadcentergaugeactual;

    @TableField("TreadShoulderGaugeTheoryMin")
    private Double treadshouldergaugetheorymin;

    @TableField("TreadShoulderGaugeTheoryMax")
    private Double treadshouldergaugetheorymax;

    @TableField("TreadShoulderGaugeActual")
    private Double treadshouldergaugeactual;

    @TableField("CreateTime")
    private Date createTime;

    @TableField("UpdateTime")
    private Date updateTime;

}
