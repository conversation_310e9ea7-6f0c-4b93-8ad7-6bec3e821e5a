package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 胎体部件实体类
 */
@Data
@TableName("PartCarcass")
public class PartCarcass {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 宽度
     */
    @TableField("Width")
    private BigDecimal width;
    
    /**
     * 角度
     */
    @TableField("Angle")
    private BigDecimal angle;
    
    /**
     * 密度
     */
    @TableField("Density")
    private String density;
    
    /**
     * 胎体材料
     */
    @TableField("CarcassMaterial")
    private String carcassMaterial;
    
    /**
     * 胎体胶料
     */
    @TableField("CarcassCompound")
    private String carcassCompound;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
