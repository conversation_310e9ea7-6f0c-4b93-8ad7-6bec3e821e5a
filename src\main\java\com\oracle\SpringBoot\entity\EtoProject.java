package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oracle.SpringBoot.vo.ConceptDataVO;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ETO项目实体类
 */
@TableName("eto_project")
public class EtoProject {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 项目编号
     */
    private String projectNo;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 尺寸
     */
    private String size;
    
    /**
     * 基础依据
     */
    private String basis;
    
    /**
     * 目的(RE/OE)
     */
    private String purpose;
    
    /**
     * 轮胎组别
     */
    private String tireGroup;
    
    /**
     * 轮胎细分
     */
    private String tireSegment;
    
    /**
     * 市场
     */
    private String market;
    
    /**
     * OE制造商
     */
    private String oeMaker;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 品牌
     */
    private String brand;
    
    /**
     * 工厂
     */
    private String plant;
    
    /**
     * 开发目的
     */
    private String purposeOfDevelopment;
    
    /**
     * 状态(pending/approved/rejected)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 关联ETO主表ID
     */
    private Long etoMainId;
    
    /**
     * 草稿ID（非数据库字段）
     */
    @TableField(exist = false)
    private Long draftId;
    
    /**
     * 产品概念数据（非数据库字段，用于前端展示）
     */
    @TableField(exist = false)
    private Map<String, Object> conceptData;
    
    // 申请人相关信息
    @TableField(exist = false)
    private String applicant;
    
    @TableField(exist = false)
    private String applyTime;
    
    @TableField(exist = false)
    private String department;
    
    // Getters and Setters
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getProjectNo() {
        return projectNo;
    }
    
    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }
    
    public String getProjectName() {
        return projectName;
    }
    
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    
    public String getSize() {
        return size;
    }
    
    public void setSize(String size) {
        this.size = size;
    }
    
    public String getBasis() {
        return basis;
    }
    
    public void setBasis(String basis) {
        this.basis = basis;
    }
    
    public String getPurpose() {
        return purpose;
    }
    
    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }
    
    public String getTireGroup() {
        return tireGroup;
    }
    
    public void setTireGroup(String tireGroup) {
        this.tireGroup = tireGroup;
    }
    
    public String getTireSegment() {
        return tireSegment;
    }
    
    public void setTireSegment(String tireSegment) {
        this.tireSegment = tireSegment;
    }
    
    public String getMarket() {
        return market;
    }
    
    public void setMarket(String market) {
        this.market = market;
    }
    
    public String getOeMaker() {
        return oeMaker;
    }
    
    public void setOeMaker(String oeMaker) {
        this.oeMaker = oeMaker;
    }
    
    public String getProductName() {
        return productName;
    }
    
    public void setProductName(String productName) {
        this.productName = productName;
    }
    
    public String getBrand() {
        return brand;
    }
    
    public void setBrand(String brand) {
        this.brand = brand;
    }
    
    public String getPlant() {
        return plant;
    }
    
    public void setPlant(String plant) {
        this.plant = plant;
    }
    
    public String getPurposeOfDevelopment() {
        return purposeOfDevelopment;
    }
    
    public void setPurposeOfDevelopment(String purposeOfDevelopment) {
        this.purposeOfDevelopment = purposeOfDevelopment;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public Long getEtoMainId() {
        return etoMainId;
    }
    
    public void setEtoMainId(Long etoMainId) {
        this.etoMainId = etoMainId;
    }
    
    public Long getDraftId() {
        return draftId;
    }
    
    public void setDraftId(Long draftId) {
        this.draftId = draftId;
    }
    
    public Map<String, Object> getConceptData() {
        return conceptData;
    }
    
    public void setConceptData(Object conceptData) {
        if (conceptData instanceof Map) {
            this.conceptData = (Map<String, Object>) conceptData;
        } else if (conceptData instanceof ConceptDataVO) {
            ConceptDataVO vo = (ConceptDataVO) conceptData;
            Map<String, Object> data = new HashMap<>();
            data.put("params", vo.getParams());
            data.put("target", vo.getTarget());
            data.put("competitors", vo.getCompetitors());
            this.conceptData = data;
        }
    }
    
    public String getApplicant() {
        return applicant;
    }
    
    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }
    
    public String getApplyTime() {
        return applyTime;
    }
    
    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
} 