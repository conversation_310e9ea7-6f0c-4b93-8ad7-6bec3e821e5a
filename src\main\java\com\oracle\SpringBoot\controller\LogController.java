package com.oracle.SpringBoot.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class LogController {

    @SuppressWarnings("unused")
    private static final Logger logger = LoggerFactory.getLogger(LogController.class);

    @GetMapping("/logs")
    public List<String> getLogs() throws IOException {
        String filePath = "logs/spring-boot.log";

        // 读取日志文件内容
        Path path = Paths.get(filePath);
        List<String> lines = Files.readAllLines(path, StandardCharsets.UTF_8);

        // 过滤出INFO级别的日志
        List<String> infoLogs = lines.stream()
                .filter(line -> line.contains("用户"))
                .collect(Collectors.toList());
        return infoLogs;
    }
}