<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方案设计 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/boot/layui/css/layui.css">
    <style>
        /* 继承主题样式 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .scheme-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            overflow-x: auto;
        }

        .scheme-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
        }

        .scheme-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e1e2d;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            min-width: 800px;
        }

        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            min-width: 120px;
        }

        .data-table th:first-child {
            background-color: #f8f9fa;
            text-align: left;
            width: 250px;
            font-weight: normal;
            position: sticky;
            left: 0;
            z-index: 1;
        }

        .data-table td {
            background: #fff;
        }

        .data-table input {
            width: 100%;
            border: none;
            background: transparent;
        }

        .highlight-row th {
            background-color: #fff7e6 !important;
            color: #ff6b00;
        }

        .module-title {
            background-color: #1E9FFF;
            color: white;
            padding: 8px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            font-size: 16px;
            position: sticky;
            left: 0;
            z-index: 2;
            text-align: center;
        }

        .scheme-header {
            position: sticky;
            left: 0;
            z-index: 2;
            background: #fff;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
            position: sticky;
            left: 0;
            background: #fff;
        }

        .layui-btn {
            height: 40px;
            line-height: 40px;
            padding: 0 24px;
            font-size: 14px;
            border-radius: 4px;
        }

        .scheme-column {
            min-width: 200px;
        }

        .value-changed {
            background-color: #fef3c7 !important;
            position: relative;
        }
        
        .value-changed::after {
            content: "↑";
            position: absolute;
            top: -15px;
            right: 5px;
            color: #d97706;
            font-weight: bold;
        }

        .value-decreased::after {
            content: "↓";
        }

        .invalid-input {
            border-color: #dc2626 !important;
            background-color: #fee2e2 !important;
        }

        .scheme-preview {
            position: fixed;
            top: 70px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            max-width: 250px;
            z-index: 1000;
            display: none;
            max-height: 300px;
            overflow-y: auto;
            opacity: 0.95;
            transition: all 0.3s ease;
        }
        
        .scheme-preview .close-btn {
            position: absolute;
            top: 8px;
            right: 10px;
            cursor: pointer;
            font-size: 16px;
            color: #999;
        }
        
        .scheme-preview .close-btn:hover {
            color: #666;
        }

        .scheme-preview.active {
            display: block;
        }

        .scheme-preview h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            color: #1e1e2d;
        }

        .scheme-preview ul {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .scheme-preview li {
            margin-bottom: 5px;
            font-size: 14px;
            color: #4b5563;
        }

        .scheme-preview .changed-value {
            color: #d97706;
            font-weight: 500;
        }

        .show-preview-btn {
            position: fixed;
            top: 70px;
            right: 20px;
            background: #1E9FFF;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            z-index: 999;
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            display: none;
        }
        
        .show-preview-btn:hover {
            background: #0D8AE3;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="scheme-container">
            <div class="scheme-header">
                <div class="scheme-title">方案设计</div>
                <div class="scheme-actions">
                    <button type="button" class="layui-btn layui-btn-primary" id="importScheme">
                        <i class="fas fa-file-import"></i> 导入参考方案
                    </button>
                    <button type="button" class="layui-btn" id="addScheme">
                        <i class="fas fa-plus"></i> 新增方案
                    </button>
                </div>
            </div>

            <div class="module-title">Mold Information</div>
            <form class="layui-form" lay-filter="schemeForm">
                <table class="data-table" id="moldInfoTable">
                    <tr>
                        <th>参数名称</th>
                        <th>
                            方案一
                            <i class="fas fa-trash delete-scheme" data-index="1" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i>
                        </th>
                        <th>
                            方案二
                            <i class="fas fa-trash delete-scheme" data-index="2" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i>
                        </th>
                        <th>
                            方案三
                            <i class="fas fa-trash delete-scheme" data-index="3" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i>
                        </th>
                    </tr>
                    <tr>
                        <th>TDW</th>
                        <td><input type="text" class="layui-input" name="tdw_1" value="118"></td>
                        <td><input type="text" class="layui-input" name="tdw_2" value="118"></td>
                        <td><input type="text" class="layui-input" name="tdw_3" value="118"></td>
                    </tr>
                    <tr>
                        <th>OD</th>
                        <td><input type="text" class="layui-input" name="od_1" value="670"></td>
                        <td><input type="text" class="layui-input" name="od_2" value="670"></td>
                        <td><input type="text" class="layui-input" name="od_3" value="670"></td>
                    </tr>
                    <tr>
                        <th>SW</th>
                        <td><input type="text" class="layui-input" name="sw_1" value="140"></td>
                        <td><input type="text" class="layui-input" name="sw_2" value="140"></td>
                        <td><input type="text" class="layui-input" name="sw_3" value="140"></td>
                    </tr>
                    <tr>
                        <th>G.D</th>
                        <td><input type="text" class="layui-input" name="gd_1" value="3"></td>
                        <td><input type="text" class="layui-input" name="gd_2" value="3"></td>
                        <td><input type="text" class="layui-input" name="gd_3" value="3"></td>
                    </tr>
                    <tr>
                        <th>Sea/Land Ratio</th>
                        <td><input type="text" class="layui-input" name="seaLandRatio_1" value="80.00%"></td>
                        <td><input type="text" class="layui-input" name="seaLandRatio_2" value="80.00%"></td>
                        <td><input type="text" class="layui-input" name="seaLandRatio_3" value="80.00%"></td>
                    </tr>
                    <tr class="highlight-row">
                        <th>Sea/Land ratio difference between upper and lower</th>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_1" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_2" value=""></td>
                        <td><input type="text" class="layui-input" name="seaLandRatioDiff_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Bead Width</th>
                        <td><input type="text" class="layui-input" name="beadWidth_1" value="16.5"></td>
                        <td><input type="text" class="layui-input" name="beadWidth_2" value="16.5"></td>
                        <td><input type="text" class="layui-input" name="beadWidth_3" value="16.5"></td>
                    </tr>
                    <tr>
                        <th>Spec</th>
                        <td><input type="text" class="layui-input" name="spec_1" value="Sub0 1A"></td>
                        <td><input type="text" class="layui-input" name="spec_2" value="Sub0 1B"></td>
                        <td><input type="text" class="layui-input" name="spec_3" value="Sub0 1C"></td>
                    </tr>
                    <tr>
                        <th>OEM Spec No.</th>
                        <td><input type="text" class="layui-input" name="oemSpecNo_1" value="G091552010101UAA0"></td>
                        <td><input type="text" class="layui-input" name="oemSpecNo_2" value="G091552010101UAB0"></td>
                        <td><input type="text" class="layui-input" name="oemSpecNo_3" value="G091552010101UAC0"></td>
                    </tr>
                    <tr>
                        <th>Tire type</th>
                        <td><input type="text" class="layui-input" name="tireType_1" value="Spare Tire"></td>
                        <td><input type="text" class="layui-input" name="tireType_2" value="Spare Tire"></td>
                        <td><input type="text" class="layui-input" name="tireType_3" value="Spare Tire"></td>
                    </tr>
                    <tr>
                        <th>New Design (Y/N)</th>
                        <td><input type="text" class="layui-input" name="newDesign_1" value="Y"></td>
                        <td><input type="text" class="layui-input" name="newDesign_2" value="Y"></td>
                        <td><input type="text" class="layui-input" name="newDesign_3" value="Y"></td>
                    </tr>
                    <tr>
                        <th>IP(theory/actual)</th>
                        <td><input type="text" class="layui-input" name="ip_1" value="280 mm"></td>
                        <td><input type="text" class="layui-input" name="ip_2" value="280 mm"></td>
                        <td><input type="text" class="layui-input" name="ip_3" value="280 mm"></td>
                    </tr>
                    <tr>
                        <th>Drum Width/B.S.D</th>
                        <td><input type="text" class="layui-input" name="drumWidth_1" value="247 mm"></td>
                        <td><input type="text" class="layui-input" name="drumWidth_2" value="247 mm"></td>
                        <td><input type="text" class="layui-input" name="drumWidth_3" value="247 mm"></td>
                    </tr>
                    <tr>
                        <th>Inner Liner</th>
                        <td><input type="text" class="layui-input" name="innerLiner_1" value="HF551-0.5mm 320mm"></td>
                        <td><input type="text" class="layui-input" name="innerLiner_2" value="HF551-0.5mm 320mm"></td>
                        <td><input type="text" class="layui-input" name="innerLiner_3" value="HF551-0.5mm 320mm"></td>
                    </tr>
                    <tr>
                        <th>Transition Layer</th>
                        <td><input type="text" class="layui-input" name="transitionLayer_1" value="HL790-1.1mm 300mm&#10;1000D/2 E100(2-ply)&#10;/HF527"></td>
                        <td><input type="text" class="layui-input" name="transitionLayer_2" value="HL790-1.1mm 301mm&#10;1000D/2 E100(2-ply)&#10;/HF528"></td>
                        <td><input type="text" class="layui-input" name="transitionLayer_3" value="HL790-1.1mm 302mm&#10;1000D/2 E100(2-ply)&#10;/HF529"></td>
                    </tr>
                    <tr>
                        <th>Ply Style(*New Materials)</th>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>/Compound</th>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Carcass Ply Width</th>
                        <td><input type="text" class="layui-input" name="carcassWidth_1" value="380mm/320mm"></td>
                        <td><input type="text" class="layui-input" name="carcassWidth_2" value="380mm/321mm"></td>
                        <td><input type="text" class="layui-input" name="carcassWidth_3" value="380mm/322mm"></td>
                    </tr>
                    <tr>
                        <th>Turn-up Height / Setting Position</th>
                        <td><input type="text" class="layui-input" name="turnupHeight_1" value="43mm/15mm"></td>
                        <td><input type="text" class="layui-input" name="turnupHeight_2" value="43mm/16mm"></td>
                        <td><input type="text" class="layui-input" name="turnupHeight_3" value="43mm/17mm"></td>
                    </tr>
                    <tr>
                        <th>Carcass angle / direction</th>
                        <td><input type="text" class="layui-input" name="carcassAngle_1" value="90°"></td>
                        <td><input type="text" class="layui-input" name="carcassAngle_2" value="91°"></td>
                        <td><input type="text" class="layui-input" name="carcassAngle_3" value="92°"></td>
                    </tr>
                    <tr>
                        <th>Sidewall Compound</th>
                        <td><input type="text" class="layui-input" name="sidewallCompound_1" value="HS417/HA693"></td>
                        <td><input type="text" class="layui-input" name="sidewallCompound_2" value="HS418/HA694"></td>
                        <td><input type="text" class="layui-input" name="sidewallCompound_3" value="HS419/HA695"></td>
                    </tr>
                    <tr>
                        <th>Sidewall die shape code</th>
                        <td><input type="text" class="layui-input" name="sidewallDieCode_1" value="RS31941/110mm (SS2023)"></td>
                        <td><input type="text" class="layui-input" name="sidewallDieCode_2" value="RS31941/110mm (SS2024)"></td>
                        <td><input type="text" class="layui-input" name="sidewallDieCode_3" value="RS31941/110mm (SS2025)"></td>
                    </tr>
                    <tr>
                        <th>Sidewall setting or Pre-Assemble</th>
                        <td><input type="text" class="layui-input" name="sidewallSetting_1" value="44mm"></td>
                        <td><input type="text" class="layui-input" name="sidewallSetting_2" value="44mm"></td>
                        <td><input type="text" class="layui-input" name="sidewallSetting_3" value="44mm"></td>
                    </tr>
                    <tr>
                        <th>Bead Filler : Compound : HT.</th>
                        <td><input type="text" class="layui-input" name="beadFiller_1" value="HA669/40mm"></td>
                        <td><input type="text" class="layui-input" name="beadFiller_2" value="HA669/40mm"></td>
                        <td><input type="text" class="layui-input" name="beadFiller_3" value="HA669/40mm"></td>
                    </tr>
                    <tr>
                        <th>Apex Die shape code</th>
                        <td><input type="text" class="layui-input" name="apexDieCode_1" value="RSF4009C (SA176)"></td>
                        <td><input type="text" class="layui-input" name="apexDieCode_2" value="RSF4009C (SA176)"></td>
                        <td><input type="text" class="layui-input" name="apexDieCode_3" value="RSF4009C (SA176)"></td>
                    </tr>
                    <tr>
                        <th>Bead Layup (HEX)/Bead Dia./Compound</th>
                        <td><input type="text" class="layui-input" name="beadLayup_1" value="4-5-6-5-4/0.96&#10;491.5/HA601"></td>
                        <td><input type="text" class="layui-input" name="beadLayup_2" value="4-5-6-5-4/0.96&#10;491.5/HA601"></td>
                        <td><input type="text" class="layui-input" name="beadLayup_3" value="4-5-6-5-4/0.96&#10;491.5/HA601"></td>
                    </tr>
                    <tr>
                        <th>Calculated Cured Bead Width</th>
                        <td>#REF!</td>
                        <td>#REF!</td>
                        <td>#REF!</td>
                    </tr>
                    <tr>
                        <th>Cured Bead Width</th>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Tread Compound</th>
                        <td>HT290</td>
                        <td>HT290</td>
                        <td>HT290</td>
                    </tr>
                    <tr>
                        <th>Tread Wing</th>
                        <td>HS417</td>
                        <td>HS417</td>
                        <td>HS417</td>
                    </tr>
                    <tr>
                        <th>Tread Base</th>
                        <td>HB351, 0.5mm</td>
                        <td>HB351, 0.6mm</td>
                        <td>HB351, 0.7mm</td>
                    </tr>
                    <tr>
                        <th>Tread Chimney</th>
                        <td>/</td>
                        <td>/</td>
                        <td>/</td>
                    </tr>
                    <tr class="highlight-row">
                        <th>Tread die symmetry</th>
                        <td>
                            <input type="checkbox" name="symmetry_1" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_1" value="Dissymmetric"> Dissymmetric
                        </td>
                        <td>
                            <input type="checkbox" name="symmetry_2" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_2" value="Dissymmetric"> Dissymmetric
                        </td>
                        <td>
                            <input type="checkbox" name="symmetry_3" value="Symmetry"> Symmetry
                            <input type="checkbox" name="dissymmetric_3" value="Dissymmetric"> Dissymmetric
                        </td>
                    </tr>
                    <tr>
                        <th>Tread die shape code</th>
                        <td>RSMA5893 (SM6416)</td>
                        <td>RSMA5893 (SM6417)</td>
                        <td>RSMA5893 (SM6418)</td>
                    </tr>
                    <tr>
                        <th>Main Groove Base Rubber Gauge</th>
                        <td>1.8</td>
                        <td>1.8</td>
                        <td>1.8</td>
                    </tr>
                    <tr>
                        <th>Tread center gauge (theory)</th>
                        <td>3-3.4</td>
                        <td>3-3.4</td>
                        <td>3-3.4</td>
                    </tr>
                    <tr>
                        <th>Tread center gauge (actual)</th>
                        <td>4</td>
                        <td>4</td>
                        <td>4</td>
                    </tr>
                    <tr>
                        <th>Tread shoulder gauge(theory)</th>
                        <td>3.5-3.8</td>
                        <td>3.5-3.8</td>
                        <td>3.5-3.8</td>
                    </tr>
                    <tr>
                        <th>Tread shoulder gauge(actual)</th>
                        <td>5.1</td>
                        <td>5.1</td>
                        <td>5.1</td>
                    </tr>
                    <tr>
                        <th>Belt Style (*New Materials)</th>
                        <td>1*2*0.305T F96</td>
                        <td>1*2*0.305T F96</td>
                        <td>1*2*0.305T F96</td>
                    </tr>
                    <tr>
                        <th>/Compound</th>
                        <td>HF535</td>
                        <td>HF536</td>
                        <td>HF537</td>
                    </tr>
                    <tr>
                        <th>Belt Width #1/#2 Green</th>
                        <td>120mm/110mm</td>
                        <td>120mm/111mm</td>
                        <td>120mm/112mm</td>
                    </tr>
                    <tr>
                        <th>Belt Angle, Degrees</th>
                        <td>21°</td>
                        <td>22°</td>
                        <td>23°</td>
                    </tr>
                    <tr>
                        <th>1# Belt Edge Gum Strip /Compound</th>
                        <td>0.6*20mm-HF535</td>
                        <td>0.6*20mm-HF536</td>
                        <td>0.6*20mm-HF537</td>
                    </tr>
                    <tr>
                        <th>2# Belt Edge Gum Strip /Compound</th>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Belt Lift Ratio</th>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_1" value="1.043"></td>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_2" value="2.043"></td>
                        <td><input type="text" class="layui-input" name="beltLiftRatio_3" value="3.043"></td>
                    </tr>
                    <tr>
                        <th>Nylon Reinforcement</th>
                        <td><input type="text" class="layui-input" name="nylonReinforcement_1" value="/"></td>
                        <td><input type="text" class="layui-input" name="nylonReinforcement_2" value="/"></td>
                        <td><input type="text" class="layui-input" name="nylonReinforcement_3" value="/"></td>
                    </tr>
                    <tr>
                        <th>Nylon reinforcement</th>
                        <td><input type="text" class="layui-input" name="nylonReinforcement2_1" value="/"></td>
                        <td><input type="text" class="layui-input" name="nylonReinforcement2_2" value="/"></td>
                        <td><input type="text" class="layui-input" name="nylonReinforcement2_3" value="/"></td>
                    </tr>
                    <tr>
                        <th>Material/Density/Compound</th>
                        <td><input type="text" class="layui-input" name="materialDensity_1" value="/"></td>
                        <td><input type="text" class="layui-input" name="materialDensity_2" value="/"></td>
                        <td><input type="text" class="layui-input" name="materialDensity_3" value="/"></td>
                    </tr>
                    <tr>
                        <th>Belt Drum Circumference</th>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_1" value="1975"></td>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_2" value="1975"></td>
                        <td><input type="text" class="layui-input" name="beltDrumCircumference_3" value="1975"></td>
                    </tr>
                    <tr>
                        <th>Circumference</th>
                        <td><input type="text" class="layui-input" name="circumference_1" value="2050"></td>
                        <td><input type="text" class="layui-input" name="circumference_2" value="2050"></td>
                        <td><input type="text" class="layui-input" name="circumference_3" value="2050"></td>
                    </tr>
                    <tr>
                        <th>Main groove base perimeter</th>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_1" value="2086.0"></td>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_2" value=""></td>
                        <td><input type="text" class="layui-input" name="mainGrooveBasePerimeter_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Pcs/ Tread Color Line</th>
                        <td><input type="text" class="layui-input" name="treadColorLine_1" value="蓝6||"></td>
                        <td><input type="text" class="layui-input" name="treadColorLine_2" value="蓝6||"></td>
                        <td><input type="text" class="layui-input" name="treadColorLine_3" value="蓝7||"></td>
                    </tr>
                    <tr>
                        <th>Building Type</th>
                        <td><input type="text" class="layui-input" name="buildingType_1" value="Two Stage"></td>
                        <td><input type="text" class="layui-input" name="buildingType_2" value="Two Stage"></td>
                        <td><input type="text" class="layui-input" name="buildingType_3" value="Two Stage"></td>
                    </tr>
                    <tr>
                        <th>Caculated Weight-predicted (kg)</th>
                        <td><input type="text" class="layui-input" name="calculatedWeight_1" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedWeight_2" value=""></td>
                        <td><input type="text" class="layui-input" name="calculatedWeight_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Curing Temperature</th>
                        <td><input type="text" class="layui-input" name="curingTemperature_1" value="Lower"></td>
                        <td><input type="text" class="layui-input" name="curingTemperature_2" value="Lower"></td>
                        <td><input type="text" class="layui-input" name="curingTemperature_3" value="Lower"></td>
                    </tr>
                    <tr>
                        <th>PCI Chuck Width</th>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_1" value=""></td>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_2" value=""></td>
                        <td><input type="text" class="layui-input" name="pciChuckWidth_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Inner bead distance of tire</th>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_1" value=""></td>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_2" value=""></td>
                        <td><input type="text" class="layui-input" name="innerBeadDistance_3" value=""></td>
                    </tr>
                    <tr>
                        <th>风险识别项:</th>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_1" value=""></td>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_2" value=""></td>
                        <td><input type="text" class="layui-input" name="innerTubeRecord_3" value=""></td>
                    </tr>
                    <tr class="highlight-row">
                        <th>Confirmation</th>
                        <td><input type="text" class="layui-input" name="confirmation_1" value="E-BOM"></td>
                        <td><input type="text" class="layui-input" name="confirmation_2" value="Performance parameter"></td>
                        <td><input type="text" class="layui-input" name="confirmation_3" value="Cut section analysis"></td>
                    </tr>
                    <tr>
                        <th>Base Rubber Gauge</th>
                        <td><input type="text" class="layui-input" name="baseRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="baseRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="baseRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Shoulder Rubber Gauge</th>
                        <td><input type="text" class="layui-input" name="shoulderRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="shoulderRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="shoulderRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>S/W Min. Rubber Gauge</th>
                        <td><input type="text" class="layui-input" name="swMinRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="swMinRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="swMinRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>PDW point rubber gauge</th>
                        <td><input type="text" class="layui-input" name="pdwPointRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="pdwPointRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="pdwPointRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Rim check line rubber gauge</th>
                        <td><input type="text" class="layui-input" name="rimCheckLineRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="rimCheckLineRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="rimCheckLineRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Rim cursion rubber gauge on bead area</th>
                        <td><input type="text" class="layui-input" name="rimCursionRubberGauge_1" value=""></td>
                        <td><input type="text" class="layui-input" name="rimCursionRubberGauge_2" value=""></td>
                        <td><input type="text" class="layui-input" name="rimCursionRubberGauge_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Height Of 1#Belt Width</th>
                        <td><input type="text" class="layui-input" name="heightOf1BeltWidth_1" value=""></td>
                        <td><input type="text" class="layui-input" name="heightOf1BeltWidth_2" value=""></td>
                        <td><input type="text" class="layui-input" name="heightOf1BeltWidth_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Ply Turnups</th>
                        <td><input type="text" class="layui-input" name="plyTurnups_1" value=""></td>
                        <td><input type="text" class="layui-input" name="plyTurnups_2" value=""></td>
                        <td><input type="text" class="layui-input" name="plyTurnups_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Apex Height</th>
                        <td><input type="text" class="layui-input" name="apexHeight_1" value=""></td>
                        <td><input type="text" class="layui-input" name="apexHeight_2" value=""></td>
                        <td><input type="text" class="layui-input" name="apexHeight_3" value=""></td>
                    </tr>
                    <tr>
                        <th>Inner Periphery</th>
                        <td><input type="text" class="layui-input" name="innerPeriphery_1" value=""></td>
                        <td><input type="text" class="layui-input" name="innerPeriphery_2" value=""></td>
                        <td><input type="text" class="layui-input" name="innerPeriphery_3" value=""></td>
                    </tr>
                    <tr>
                        <th>COMMENT</th>
                        <td><input type="text" class="layui-input" name="comment_1" value=""></td>
                        <td><input type="text" class="layui-input" name="comment_2" value=""></td>
                        <td><input type="text" class="layui-input" name="comment_3" value=""></td>
                    </tr>
                </table>
                <div class="note" style="margin-top: 20px; color: #666;">
                    注：超低温硫化时必须使用全弹氮气管
                </div>
            </form>

            <div class="action-buttons">
                <button type="button" class="layui-btn layui-btn-primary" id="saveDraft">
                    <i class="fas fa-save"></i> 保存草稿
                </button>
                <button type="button" class="layui-btn" id="submitDesign">
                    <i class="fas fa-check"></i> 提交审核
                </button>
            </div>
        </div>
    </div>

    <div class="show-preview-btn" id="showPreviewBtn">显示方案对比</div>
    <div class="scheme-preview" id="schemePreview">
        <h3>方案对比预览</h3>
        <span class="close-btn">&times;</span>
        <ul id="previewList"></ul>
    </div>

    <script src="/boot/layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 验证规则
        var validationRules = {
            tdw: { min: 50, max: 500 },
            od: { min: 300, max: 1500 },
            sw: { min: 50, max: 500 },
            gd: { min: 0.5, max: 10 },
            seaLandRatio: { pattern: /^\d{1,2}(\.\d{1,2})?%$/ },
            beadWidth: { min: 10, max: 50 }
        };

        // 验证函数
        function validateInput(input) {
            var name = input.attr('name').split('_')[0];
            var value = input.val();
            var rule = validationRules[name];
            
            if (!rule) return true;

            if (rule.min !== undefined && rule.max !== undefined) {
                var numValue = parseFloat(value);
                if (isNaN(numValue) || numValue < rule.min || numValue > rule.max) {
                    input.addClass('invalid-input');
                    layer.tips('值应在 ' + rule.min + ' 到 ' + rule.max + ' 之间', input);
                    return false;
                }
            }

            if (rule.pattern && !rule.pattern.test(value)) {
                input.addClass('invalid-input');
                layer.tips('格式不正确', input);
                return false;
            }

            input.removeClass('invalid-input');
            return true;
        }

        // 导入参考方案
        $('#importScheme').on('click', function(){
            layer.open({
                type: 2,
                title: '导入参考方案',
                area: ['80%', '80%'],
                content: '/boot/reference-schemes',
                btn: ['确定', '取消'],
                yes: function(index, layero){
                    // 获取iframe中选中的方案数据
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    var selectedScheme = iframeWin.getSelectedScheme();
                    if(selectedScheme){
                        insertSchemeAtFirst(selectedScheme);
                        layer.close(index);
                    } else {
                        layer.msg('请选择一个参考方案');
                    }
                }
            });
        });

        // 在方案一位置插入参考方案，其他方案右移
        function insertSchemeAtFirst(schemeData) {
            var table = $('#moldInfoTable');
            
            // 保存所有现有方案的数据
            var existingSchemes = [];
            var columnCount = table.find('tr:first th').length - 1; // 减去参数名称列
            
            for(var i = 1; i <= columnCount; i++) {
                var schemeData = {};
                table.find('tr:gt(0)').each(function(){
                    var input = $(this).find('td:eq(' + (i-1) + ') input');
                    schemeData[input.attr('name').split('_')[0]] = input.val();
                });
                existingSchemes.push(schemeData);
            }
            
            // 清除现有的所有方案列
            table.find('tr').each(function(){
                $(this).find('th:gt(0), td').remove();
            });
            
            // 添加参考方案作为第一列
            table.find('tr:first').append('<th>参考方案 <i class="fas fa-trash delete-scheme" data-index="1" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>');
            table.find('tr:gt(0)').each(function(){
                var paramName = $(this).find('th').text().toLowerCase().replace(/[^a-z0-9]/g, '');
                var value = schemeData[paramName] || '';
                $(this).append('<td><input type="text" class="layui-input" name="' + paramName + '_1" value="' + value + '"></td>');
            });
            
            // 添加原有方案
            existingSchemes.forEach(function(scheme, index) {
                var schemeIndex = index + 2;
                table.find('tr:first').append('<th>方案' + (index + 1) + ' <i class="fas fa-trash delete-scheme" data-index="' + schemeIndex + '" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>');
                table.find('tr:gt(0)').each(function(){
                    var paramName = $(this).find('th').text();
                    var value = scheme[paramName] || '';
                    $(this).append('<td><input type="text" class="layui-input" name="' + paramName + '_' + schemeIndex + '" value="' + value + '"></td>');
                });
            });
            
            form.render();
            watchValueChanges();
            bindDeleteEvents();
        }

        // 删除方案
        function bindDeleteEvents() {
            $('.delete-scheme').off('click').on('click', function(){
                var index = $(this).data('index');
                var table = $('#moldInfoTable');
                
                layer.confirm('确认删除该方案？', {
                    btn: ['确定','取消']
                }, function(){
                    // 删除该列
                    table.find('tr').each(function(){
                        $(this).find('th:eq(' + index + '), td:eq(' + (index-1) + ')').remove();
                    });
                    
                    // 重新编号剩余方案
                    table.find('tr:first th:gt(0)').each(function(i){
                        var text = $(this).text().replace(/\d+/, i + 1);
                        $(this).html(text + ' <i class="fas fa-trash delete-scheme" data-index="' + (i+1) + '" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i>');
                    });
                    
                    // 更新input的name属性
                    table.find('tr:gt(0)').each(function(){
                        $(this).find('input').each(function(i){
                            var name = $(this).attr('name').split('_')[0];
                            $(this).attr('name', name + '_' + (i+1));
                        });
                    });
                    
                    form.render();
                    watchValueChanges();
                    bindDeleteEvents();
                    layer.msg('方案已删除');
                });
            });
        }

        // 新增方案
        $('#addScheme').on('click', function(){
            var table = $('#moldInfoTable');
            var columnCount = table.find('tr:first th').length;
            
            // 添加表头
            var newColumnNum = columnCount;
            table.find('tr:first').append('<th>方案' + (newColumnNum) + ' <i class="fas fa-trash delete-scheme" data-index="' + newColumnNum + '" style="margin-left: 8px; cursor: pointer; color: #ff4d4f;"></i></th>');
            
            // 添加数据单元格
            table.find('tr:gt(0)').each(function(){
                var paramName = $(this).find('th').text();
                $(this).append('<td><input type="text" class="layui-input" name="' + paramName + '_' + newColumnNum + '"></td>');
            });
            
            form.render();
            watchValueChanges();
            bindDeleteEvents();
        });

        // 监听值变化
        function watchValueChanges(){
            $('.layui-input').off('input').on('input', function(){
                var $input = $(this);
                var baseValue = $('#moldInfoTable').find('tr:eq(' + $input.closest('tr').index() + ') td:eq(0) input').val();
                var currentValue = $input.val();
                var $td = $input.closest('td');
                
                validateInput($input);

                if(baseValue && currentValue) {
                    if(baseValue !== currentValue) {
                        $td.addClass('value-changed');
                        if(parseFloat(currentValue) < parseFloat(baseValue)) {
                            $td.addClass('value-decreased');
                        } else {
                            $td.removeClass('value-decreased');
                        }
                    } else {
                        $td.removeClass('value-changed value-decreased');
                    }
                } else {
                    $td.removeClass('value-changed value-decreased');
                }

                updateSchemePreview();
            });
            
            // 添加关闭按钮事件处理
            $('.scheme-preview .close-btn').off('click').on('click', function(e){
                e.stopPropagation();
                $('#schemePreview').removeClass('active');
                $('#showPreviewBtn').show();
            });
            
            // 添加显示预览按钮事件处理
            $('#showPreviewBtn').off('click').on('click', function(){
                $(this).hide();
                $('#schemePreview').addClass('active');
            });
        }

        // 更新方案预览
        function updateSchemePreview() {
            var $preview = $('#schemePreview');
            var $previewList = $('#previewList');
            var $showPreviewBtn = $('#showPreviewBtn');
            var changes = [];

            $('#moldInfoTable tr:gt(0)').each(function() {
                var $row = $(this);
                var paramName = $row.find('th').text();
                var $changedCells = $row.find('td.value-changed');

                if ($changedCells.length > 0) {
                    var baseValue = $row.find('td:eq(0) input').val();
                    $changedCells.each(function() {
                        var schemeNum = $(this).index();
                        var currentValue = $(this).find('input').val();
                        changes.push({
                            param: paramName,
                            scheme: schemeNum,
                            from: baseValue,
                            to: currentValue
                        });
                    });
                }
            });

            if (changes.length > 0) {
                $previewList.empty();
                changes.forEach(function(change) {
                    $previewList.append(
                        '<li>' + change.param + ' (方案' + change.scheme + '): ' +
                        '<span class="changed-value">' + change.from + ' → ' + change.to + '</span></li>'
                    );
                });
                $preview.addClass('active');
                $showPreviewBtn.hide();
            } else {
                $preview.removeClass('active');
                $showPreviewBtn.hide(); // 如果没有变化，两个按钮都不显示
            }
        }

        // 保存草稿
        $('#saveDraft').on('click', function(){
            var formData = form.val('schemeForm');
            var isValid = true;

            // 验证所有输入
            $('.layui-input').each(function() {
                if (!validateInput($(this))) {
                    isValid = false;
                    return false;
                }
            });

            if (!isValid) {
                layer.msg('请修正标红的输入项', {icon: 2});
                return;
            }

            // 保存到localStorage
            localStorage.setItem('schemeDraft', JSON.stringify(formData));
            
            // 这里添加保存草稿的Ajax请求
            layer.msg('草稿已保存');
        });

        // 加载草稿
        function loadDraft() {
            var savedDraft = localStorage.getItem('schemeDraft');
            if (savedDraft) {
                try {
                    var formData = JSON.parse(savedDraft);
                    form.val('schemeForm', formData);
                    watchValueChanges();
                    updateSchemePreview();
                } catch (e) {
                    console.error('Failed to load draft:', e);
                }
            }
        }

        // 初始化
        loadDraft();
        watchValueChanges();
        bindDeleteEvents();
    });
    </script>
</body>
</html>