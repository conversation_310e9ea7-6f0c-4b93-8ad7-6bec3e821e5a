<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目详情 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/boot/layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .project-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #EBEEF5;
        }

        .header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .header h2 i {
            margin-right: 8px;
            color: #409EFF;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .action-buttons .layui-btn {
            display: flex;
            align-items: center;
        }

        .action-buttons .layui-btn i {
            margin-right: 5px;
        }

        .project-info {
            background-color: #F5F7FA;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .project-info .info-row {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 8px;
        }

        .project-info .info-item {
            display: flex;
            width: 33%;
            margin-bottom: 12px;
            padding-right: 15px;
        }

        .project-info .info-label {
            font-weight: 600;
            width: 120px;
            color: #606266;
        }

        .project-info .info-value {
            flex: 1;
            color: #303133;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 24px 0 16px;
            color: #303133;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 8px;
            color: #409EFF;
        }

        .product-concepts {
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #EBEEF5;
        }

        .concept-table {
            width: 100%;
            border-collapse: collapse;
        }

        .concept-table th, .concept-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #EBEEF5;
        }

        .concept-table th {
            background-color: #F5F7FA;
            font-weight: 600;
            color: #606266;
        }

        .concept-table tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #fff;
        }

        .status-pending {
            background-color: #E6A23C;
        }

        .status-approved {
            background-color: #67C23A;
        }

        .status-rejected {
            background-color: #F56C6C;
        }

        .status-draft {
            background-color: #909399;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            color: #606266;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .back-link:hover {
            color: #409EFF;
        }

        .back-link i {
            margin-right: 4px;
        }

        .details-section {
            margin-bottom: 20px;
            background-color: #F5F7FA;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .details-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 0 0 12px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #EBEEF5;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table th, .details-table td {
            padding: 10px;
            text-align: left;
        }
        
        .details-table th {
            width: 120px;
            font-weight: 600;
            color: #606266;
        }
        
        .details-table td {
            color: #303133;
        }
        
        .project-details {
            margin-bottom: 30px;
        }
        
        /* 可编辑单元格样式 */
        .editable-cell {
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
        }
        
        .editable-cell:hover {
            background-color: #f0f9ff;
        }
        
        .editable-cell:hover::after {
            content: '\f044';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 2px;
            right: 2px;
            color: #409EFF;
            font-size: 12px;
            opacity: 0.7;
        }
        
        .cell-edit-input {
            width: 100%;
            border: 1px solid #409EFF !important;
            box-shadow: 0 0 3px rgba(64, 158, 255, 0.5);
        }

        /* 可编辑项目信息字段样式 */
        .editable-field {
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
            padding: 6px 10px;
            border-radius: 4px;
        }
        
        .editable-field:hover {
            background-color: #f0f9ff;
        }
        
        .editable-field:hover::after {
            content: '\f044';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            color: #409EFF;
            font-size: 12px;
            opacity: 0.7;
        }
        
        .field-edit-input {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #409EFF;
            border-radius: 4px;
            box-shadow: 0 0 3px rgba(64, 158, 255, 0.5);
            outline: none;
        }
        
        .field-edit-select {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #409EFF;
            border-radius: 4px;
            box-shadow: 0 0 3px rgba(64, 158, 255, 0.5);
            outline: none;
        }
        
        .save-all-btn {
            display: none;
            margin-top: 15px;
        }

        /* 参数标题删除按钮样式 */
        .delete-param-btn {
            opacity: 0.2;
            margin-left: 5px;
            transition: all 0.3s;
            padding: 2px 3px;
            height: 20px;
            line-height: 14px;
            min-width: auto;
            position: relative;
            top: -1px;
        }
        
        .delete-param-btn i {
            font-size: 10px;
            margin: 0;
        }
        
        th:hover .delete-param-btn {
            opacity: 1;
        }
        
        /* 导航按钮美化 */
        .nav-buttons {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .nav-btn {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            font-size: 13px;
            color: #606266;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 3px;
            margin-right: 8px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .nav-btn:hover {
            color: #409EFF;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
        
        .nav-btn i {
            margin-right: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="nav-buttons">
            <div class="back-link" id="backToListBtn">
                        <i class="fas fa-arrow-left"></i> 返回列表
            </div>
            <button class="nav-btn" id="homeBtn">
                <i class="fas fa-home"></i> 返回首页
            </button>
        </div>
        
        <div class="header-actions" style="display: flex; margin-bottom: 15px;">
            <button class="layui-btn layui-btn-primary layui-btn-sm" id="homeBtn">
                <i class="fas fa-home"></i> 返回首页
            </button>
        </div>
        
        <div class="project-container">
            <div class="header">
                <h2><i class="fas fa-project-diagram"></i>项目详情</h2>
                <div class="status-badge" id="projectStatus"></div>
            </div>

            <div class="project-info" id="projectInfo">
                <!-- 项目信息将通过JS动态填充 -->
                </div>

            <div class="section-title">
                <i class="fas fa-cube"></i>项目详细信息
            </div>

            <div class="project-details">
                <div class="details-section">
                    <h4>基本信息</h4>
                    <table class="details-table">
                    <tr>
                        <th>项目编号</th>
                        <td id="projectNo">-</td>
                        <th>项目名称</th>
                        <td id="projectName" class="editable-field" data-field="projectName">-</td>
                    </tr>
                    <tr>
                            <th>尺寸</th>
                        <td id="size" class="editable-field" data-field="size">-</td>
                            <th>目的</th>
                            <td id="purpose" class="editable-field" data-field="purpose" data-type="select" data-options="RE,OE,OE/RE,其他">-</td>
                    </tr>
                    <tr>
                        <th>轮胎组别</th>
                        <td id="tireGroup" class="editable-field" data-field="tireGroup" data-type="select" data-options="SUV,PCR,TBR,OTR,其他">-</td>
                        <th>市场</th>
                        <td id="market" class="editable-field" data-field="market" data-type="select" data-options="China,America,Europe,Asia,Global,Other">-</td>
                    </tr>
                    <tr>
                            <th>轮胎细分</th>
                            <td id="tireSegment" class="editable-field" data-field="tireSegment" data-type="select" data-options="SUV,Passenger,Van,Pick Up,ETC">-</td>
                        <th>品牌</th>
                        <td id="brand" class="editable-field" data-field="brand" data-type="select" data-options="LL,ATLAS,其他">-</td>
                    </tr>
                    <tr>
                            <th>工厂</th>
                            <td id="plant" class="editable-field" data-field="plant" data-type="select" data-options="Zhao yuan,PCR,PCR5,其他">-</td>
                            <th>基础</th>
                            <td id="basis" class="editable-field" data-field="basis">-</td>
                    </tr>
                    <tr>
                            <th>OE厂商</th>
                            <td id="oeMaker" class="editable-field" data-field="oeMaker">-</td>
                            <th>是否草稿</th>
                            <td id="isDraft">-</td>
                    </tr>
                </table>
                <button class="layui-btn layui-btn-sm save-all-btn" id="saveAllBtn">
                    <i class="fas fa-save"></i> 保存所有修改
                </button>
            </div>

                <div class="details-section">
                    <h4>申请信息</h4>
                    <table class="details-table">
                        <tr>
                            <th>申请人</th>
                            <td id="applicant" class="editable-field" data-field="applicant">-</td>
                            <th>申请时间</th>
                            <td id="applyTime">-</td>
                        </tr>
                        <tr>
                            <th>部门</th>
                            <td id="department" class="editable-field" data-field="department" data-type="select" data-options="RD,PM,QA,其他">-</td>
                            <th>状态</th>
                            <td><span id="statusBadge" class="status-badge">-</span>
                                <select id="statusSelect" class="field-edit-select" style="display:none; width: auto; margin-left: 10px;">
                                    <option value="pending">待审批</option>
                                    <option value="approved">已通过</option>
                                    <option value="rejected">已拒绝</option>
                                    <option value="draft">草稿</option>
                                </select>
                                <button id="changeStatusBtn" class="layui-btn layui-btn-xs layui-btn-primary">
                                    <i class="fas fa-edit"></i> 更改
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <div class="details-section">
                    <h4>研发信息</h4>
                    <table class="details-table">
                        <tr>
                            <th>研发目的</th>
                            <td colspan="3" id="purposeOfDevelopment" class="editable-field" data-field="purposeOfDevelopment" data-type="textarea">-</td>
                        </tr>
                        <tr>
                            <th>创建人</th>
                            <td id="createBy">-</td>
                            <th>创建时间</th>
                            <td id="createTime">-</td>
                        </tr>
                        <tr>
                            <th>更新人</th>
                            <td id="updateBy">-</td>
                            <th>更新时间</th>
                            <td id="updateTime">-</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="section-title">
                <i class="fas fa-sitemap"></i>产品概念
                <button class="layui-btn layui-btn-sm" id="addParamBtn" style="margin-left: 10px;">
                    <i class="fas fa-plus"></i> 添加参数
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" id="addCompetitorBtn">
                    <i class="fas fa-plus"></i> 添加竞品
                </button>
            </div>
            
            <div class="product-concepts">
                <table class="concept-table" id="conceptTable">
                    <!-- 表格内容将由JS动态生成 -->
                </table>
                <div class="empty-concept" id="emptyConcept" style="display: none; text-align: center; padding: 40px 0;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; color: #DCDFE6; display: block; margin-bottom: 15px;"></i>
                    <p style="color: #909399;">该项目暂无产品概念参数</p>
                    <p style="color: #909399;">点击"添加参数"按钮添加新参数</p>
                </div>
            </div>
            
            <div class="action-buttons" style="margin-top: 24px; text-align: center;">
                <button class="layui-btn" id="schemeBtn">
                    <i class="fas fa-sitemap"></i>查看方案
                </button>
                <button class="layui-btn layui-btn-primary nav-btn" id="backBtn">
                    <i class="fas fa-arrow-left"></i>返回列表
                </button>
                <button class="layui-btn layui-btn-danger" id="debugBtn" style="display:none;">
                    <i class="fas fa-bug"></i>调试数据
                </button>
            </div>
        </div>
    </div>

    <script src="/boot/layui/layui.js"></script>
    <script>
    layui.use(['layer', 'util'], function(){
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.jquery;
        
        // 获取URL中的项目ID
        var pathParts = window.location.pathname.split('/');
        var projectId = pathParts[pathParts.length - 1];
        
        // 用于存储项目编辑数据的对象
        var projectEditData = {};
        
        // 标准化参数名称 - 用于匹配不同格式的参数名
        function normalizeParamName(name) {
            if (!name) return '';
            return name.toLowerCase()
                .replace(/\s+/g, '') // 移除空格
                .replace(/[_\-]/g, '') // 移除下划线和连字符
                .replace(/braking/i, 'break') // 常见替代词
                .replace(/handling/i, 'handle'); // 常见替代词
        }
        
        // 智能匹配参数名和值
        function smartMatchParam(params, values) {
            var result = {};
            
            // 检查values是否为对象
            if (!values || typeof values !== 'object') {
                return result;
            }
            
            // 1. 先尝试直接匹配ID
            params.forEach(function(param) {
                if (param.id && values[param.id] !== undefined) {
                    result[param.id] = values[param.id];
                }
            });
            
            // 2. 尝试匹配参数名称
            var paramMap = {};
            params.forEach(function(param) {
                if (param.paramName) {
                    var normalizedName = normalizeParamName(param.paramName);
                    if (normalizedName) {
                        paramMap[normalizedName] = param.id;
                    }
                }
            });
            
            // 遍历values中的所有键
            for (var key in values) {
                // 尝试标准化键名并查找匹配
                var normalizedKey = normalizeParamName(key);
                if (normalizedKey && paramMap[normalizedKey] && result[paramMap[normalizedKey]] === undefined) {
                    result[paramMap[normalizedKey]] = values[key];
                }
                // 如果是数字字符串，尝试作为ID
                else if (!isNaN(key) && params[parseInt(key)-1] && params[parseInt(key)-1].id) {
                    var paramId = params[parseInt(key)-1].id;
                    if (result[paramId] === undefined) {
                        result[paramId] = values[key];
                    }
                }
            }
            
            console.log("智能匹配结果:", result);
            return result;
        }
        
        // 加载项目详情
        loadProjectDetail(projectId);
        
        // 绑定按钮事件
        $('#schemeBtn').on('click', function() {
            window.location.href = '/boot/eto-project/scheme/' + projectId;
        });
        
        // 添加调试功能 - 按Ctrl+Shift+D显示调试按钮
        $(document).keydown(function(e) {
            if (e.ctrlKey && e.shiftKey && e.keyCode === 68) { // 'D' key
                $('#debugBtn').toggle();
            }
        });
        
        // 调试按钮事件
        $('#debugBtn').on('click', function() {
            $.ajax({
                url: '/boot/api/eto-project-concept-debug/' + projectId,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0) {
                        // 将调试数据显示在一个新的弹窗中
                        var debugContent = '<div style="max-height: 600px; overflow: auto; padding: 20px;">';
                        debugContent += '<h3>调试数据</h3>';
                        
                        // 参数列表
                        debugContent += '<h4>参数列表 (' + (res.data.params_raw ? res.data.params_raw.length : 0) + '项)</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.params_raw, null, 2) + '</pre>';
                        
                        // 参数ID-名称映射
                        debugContent += '<h4>参数ID-名称映射</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.params_map, null, 2) + '</pre>';
                        
                        // 目标值
                        debugContent += '<h4>目标值原始数据 (' + (res.data.targets_raw ? res.data.targets_raw.length : 0) + '项)</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.targets_raw, null, 2) + '</pre>';
                        
                        // 目标值Map
                        debugContent += '<h4>目标值Map</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.targets_map, null, 2) + '</pre>';
                        
                        // 竞品
                        debugContent += '<h4>竞品原始数据 (' + (res.data.competitors_raw ? res.data.competitors_raw.length : 0) + '项)</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.competitors_raw, null, 2) + '</pre>';
                        
                        // 竞品值
                        if (res.data.competitor_values_raw) {
                            debugContent += '<h4>竞品值原始数据 (' + res.data.competitor_values_raw.length + '项)</h4>';
                            debugContent += '<pre>' + JSON.stringify(res.data.competitor_values_raw, null, 2) + '</pre>';
                        }
                        
                        // 标准格式数据
                        debugContent += '<h4>标准格式数据</h4>';
                        debugContent += '<pre>' + JSON.stringify(res.data.concept_data, null, 2) + '</pre>';
                        
                        debugContent += '</div>';
                        
                        layer.open({
                            type: 1,
                            title: '产品概念调试数据',
                            area: ['800px', '600px'],
                            content: debugContent
                        });
                    } else {
                        layer.msg(res.msg || '获取调试数据失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('系统错误', {icon: 2});
                }
            });
        });
        
        // 添加参数按钮事件
        $('#addParamBtn').on('click', function() {
            layer.open({
                type: 1,
                title: '添加参数',
                area: ['400px', '200px'],
                content: '<div style="padding: 20px;">' +
                    '<form class="layui-form" id="paramForm">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">参数名称</label>' +
                    '<div class="layui-input-block">' +
                    '<input type="text" name="paramName" required lay-verify="required" placeholder="请输入参数名称" class="layui-input">' +
                    '</div></div>' +
                    '<div class="layui-form-item" style="text-align: center; margin-top: 30px;">' +
                    '<button type="button" class="layui-btn" lay-submit lay-filter="saveParam">保存</button>' +
                    '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>' +
                    '</div>' +
                    '</form></div>',
                success: function() {
                    layui.form.render();
                    layui.form.on('submit(saveParam)', function(data) {
                        var params = data.field;
                        params.projectId = projectId;
                        
                        $.ajax({
                            url: '/boot/api/eto-project-params',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(params),
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg('添加参数成功', {icon: 1});
                                    layer.closeAll('page');
                                    loadProjectDetail(projectId); // 重新加载详情
                                } else {
                                    layer.msg(res.msg || '添加失败', {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('系统错误', {icon: 2});
                            }
                        });
                        return false;
                    });
                }
            });
        });
        
        // 添加竞品按钮事件
        $('#addCompetitorBtn').on('click', function() {
            // 首先检查是否有参数
            $.ajax({
                url: '/boot/api/eto-project-params/' + projectId,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0 && res.data && res.data.length > 0) {
                        showAddCompetitorDialog(res.data);
                    } else {
                        layer.msg('请先添加参数', {icon: 2});
                    }
                }
            });
        });
        
        // 显示添加竞品对话框
        function showAddCompetitorDialog(params) {
            layer.open({
                type: 1,
                title: '添加竞品',
                area: ['600px', '400px'],
                content: '<div style="padding: 20px;">' +
                    '<form class="layui-form" id="competitorForm">' +
                    '<div class="layui-form-item">' +
                    '<label class="layui-form-label">竞品名称</label>' +
                    '<div class="layui-input-block">' +
                    '<input type="text" name="competitorName" required lay-verify="required" placeholder="请输入竞品名称" class="layui-input">' +
                    '</div></div>' +
                    '<div id="paramInputs"></div>' +
                    '<div class="layui-form-item" style="text-align: center; margin-top: 30px;">' +
                    '<button type="button" class="layui-btn" lay-submit lay-filter="saveCompetitor">保存</button>' +
                    '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>' +
                    '</div>' +
                    '</form></div>',
                success: function() {
                    // 生成参数输入项
                    var paramsHtml = '';
                    params.forEach(function(param) {
                        paramsHtml += '<div class="layui-form-item">' +
                            '<label class="layui-form-label">' + param.paramName + '</label>' +
                            '<div class="layui-input-block">' +
                            '<input type="text" name="param_' + param.id + '" placeholder="请输入' + param.paramName + '的值" class="layui-input">' +
                            '</div></div>';
                    });
                    $('#paramInputs').html(paramsHtml);
                    
                    layui.form.render();
                    layui.form.on('submit(saveCompetitor)', function(data) {
                        var formData = {
                            projectId: projectId,
                            competitorName: data.field.competitorName,
                            paramValues: []
                        };
                        
                        // 收集参数值
                        for (var key in data.field) {
                            if (key.startsWith('param_')) {
                                var paramId = key.split('_')[1];
                                formData.paramValues.push({
                                    paramId: paramId,
                                    value: data.field[key]
                                });
                            }
                        }
                        
                        $.ajax({
                            url: '/boot/api/eto-project-competitors',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(formData),
                            success: function(res) {
                                if (res.code === 0) {
                                    layer.msg('添加竞品成功', {icon: 1});
                                    layer.closeAll('page');
                                    loadProjectDetail(projectId); // 重新加载详情
                                } else {
                                    layer.msg(res.msg || '添加失败', {icon: 2});
                    }
                },
                error: function() {
                                layer.msg('系统错误', {icon: 2});
                            }
                        });
                        return false;
                    });
                }
            });
        }
        
        // 设置目标值事件
        $(document).on('click', '.set-target-btn', function() {
            // 获取参数列表
            $.ajax({
                url: '/boot/api/eto-project-params/' + projectId,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0 && res.data && res.data.length > 0) {
                        showTargetDialog(res.data);
                    } else {
                        layer.msg('请先添加参数', {icon: 2});
                    }
                }
            });
        });
        
        // 显示设置目标值对话框
        function showTargetDialog(params) {
            // 获取当前目标值
            $.ajax({
                url: '/boot/api/eto-project-concept/' + projectId,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0) {
                        // 确保有匹配的目标值
                        var targetValues = res.data.target || {};
                        var matchedTarget = smartMatchParam(params, targetValues);
                        
                        layer.open({
                            type: 1,
                            title: '设置目标值',
                            area: ['500px', '400px'],
                            content: '<div style="padding: 20px;">' +
                                '<form class="layui-form" id="targetForm">' +
                                '<div id="targetInputs"></div>' +
                                '<div class="layui-form-item" style="text-align: center; margin-top: 30px;">' +
                                '<button type="button" class="layui-btn" lay-submit lay-filter="saveTarget">保存</button>' +
                                '<button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>' +
                                '</div>' +
                                '</form></div>',
                            success: function() {
                                // 生成参数输入项
                                var inputsHtml = '';
                                params.forEach(function(param) {
                                    var targetValue = matchedTarget[param.id] || '';
                                    inputsHtml += '<div class="layui-form-item">' +
                                        '<label class="layui-form-label">' + param.paramName + '</label>' +
                                        '<div class="layui-input-block">' +
                                        '<input type="text" name="target_' + param.id + '" value="' + targetValue + '" placeholder="请输入目标值" class="layui-input">' +
                                        '</div></div>';
                                });
                                $('#targetInputs').html(inputsHtml);
                                
                                layui.form.render();
                                layui.form.on('submit(saveTarget)', function(data) {
                                    var targetData = {
                                        projectId: projectId,
                                        targetValues: {}
                                    };
                                    
                                    // 只收集有输入值的字段
                                    for (var key in data.field) {
                                        if (key.startsWith('target_')) {
                                            var paramId = key.split('_')[1];
                                            // 只有当输入不为空时才发送
                                            if (data.field[key].trim() !== '') {
                                            targetData.targetValues[paramId] = data.field[key];
                                            }
                                        }
                                    }
                                    
                                    $.ajax({
                                        url: '/boot/api/eto-project-targets',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify(targetData),
                                        success: function(res) {
                                            if (res.code === 0) {
                                                layer.msg('设置目标值成功', {icon: 1});
                                                layer.closeAll('page');
                                                loadProjectDetail(projectId); // 重新加载详情
                                            } else {
                                                layer.msg(res.msg || '设置失败', {icon: 2});
                                            }
                                        },
                                        error: function() {
                                            layer.msg('系统错误', {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                            }
                        });
                    }
                }
            });
        }
        
        // 删除竞品按钮事件
        $(document).on('click', '.delete-competitor-btn', function() {
            var competitorId = $(this).data('id');
            
            layer.confirm('确定要删除这个竞品吗？', {icon: 3, title:'提示'}, function(index){
                $.ajax({
                    url: '/boot/api/eto-project-competitors/' + competitorId,
                    type: 'DELETE',
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('删除成功', {icon: 1});
                            loadProjectDetail(projectId); // 重新加载详情
                        } else {
                            layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('系统错误', {icon: 2});
                    }
                });
                
                layer.close(index);
            });
        });
        
        // 删除参数按钮事件
        $(document).on('click', '.delete-param-btn', function() {
            var paramId = $(this).data('id');
            
            layer.confirm('确定要删除这个参数吗？删除后相关的目标值和竞品参数值也将被删除。', {
                icon: 3, 
                title:'删除确认',
                btn: ['确定删除','取消']
            }, function(index){
                $.ajax({
                    url: '/boot/api/eto-project-params/' + paramId,
                    type: 'DELETE',
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('参数删除成功', {icon: 1});
                            loadProjectDetail(projectId); // 重新加载详情
                        } else {
                            layer.msg(res.msg || '删除失败', {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('系统错误，请重试', {icon: 2});
                    }
                });
                
                layer.close(index);
            });
        });
        
        // 加载项目详情函数
        function loadProjectDetail(id) {
            layer.load(2);
            
            // 先加载项目基本信息
            $.ajax({
                url: '/boot/api/eto-project/' + id,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0) {
                        renderProjectInfo(res.data);
                        
                        // 再单独加载产品概念数据
                        loadConceptData(id);
                    } else {
                        layer.closeAll('loading');
                        layer.msg(res.msg || '加载失败，请重试', {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('系统错误，请重试', {icon: 2});
                }
            });
        }
        
        // 单独加载产品概念数据
        function loadConceptData(projectId) {
            $.ajax({
                url: '/boot/api/eto-project-concept/' + projectId,
                type: 'GET',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        console.log("单独请求的产品概念数据(原始):", res.data);
                        
                        // 直接传递原始数据给渲染函数，由渲染函数处理所有数据格式问题
                        try {
                            renderConceptTable(res.data);
                        } catch (e) {
                            console.error("渲染产品概念表格失败:", e);
                            $('#conceptTable').hide();
                            $('#emptyConcept').show();
                        }
                    } else {
                        // 如果获取失败，尝试显示空表格
                        console.error("获取产品概念数据失败:", res.msg);
                        $('#conceptTable').hide();
                        $('#emptyConcept').show();
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll('loading');
                    console.error("API请求错误:", status, error);
                    console.error("响应内容:", xhr.responseText);
                    // 显示空表格
                    $('#conceptTable').hide();
                    $('#emptyConcept').show();
                }
            });
        }
        
        // 渲染项目信息
        function renderProjectInfo(project) {
            // 设置状态样式
            var statusText = '未知';
            var statusClass = '';
            
            switch (project.status) {
                case 'pending':
                    statusText = '待审批';
                    statusClass = 'status-pending';
                    break;
                case 'approved':
                    statusText = '已通过';
                    statusClass = 'status-approved';
                    break;
                case 'rejected':
                    statusText = '已拒绝';
                    statusClass = 'status-rejected';
                    break;
                case 'draft':
                    statusText = '草稿';
                    statusClass = 'status-draft';
                    break;
            }
            
            $('#projectStatus').text(statusText).addClass(statusClass);
            $('#statusBadge').text(statusText).addClass(statusClass);
            
            // 填充详细字段
            $('#projectNo').text(project.projectNo || '-');
            $('#projectName').text(project.projectName || '-');
            $('#size').text(project.size || '-');
            $('#purpose').text(project.purpose || '-');
            $('#tireGroup').text(project.tireGroup || '-');
            $('#market').text(project.market || '-');
            $('#tireSegment').text(project.tireSegment || '-');
            $('#brand').text(project.brand || '-');
            $('#plant').text(project.plant || '-');
            $('#basis').text(project.basis || '-');
            $('#oeMaker').text(project.oeMaker || '-');
            $('#isDraft').text(project.isDraft ? '是' : '否');
            
            $('#applicant').text(project.applicant || '-');
            $('#applyTime').text(project.applyTime ? util.toDateString(new Date(project.applyTime), 'yyyy-MM-dd HH:mm:ss') : '-');
            $('#department').text(project.department || '-');
            
            $('#purposeOfDevelopment').text(project.purposeOfDevelopment || '-');
            $('#createBy').text(project.createBy || '-');
            $('#createTime').text(project.createTime ? util.toDateString(new Date(project.createTime), 'yyyy-MM-dd HH:mm:ss') : '-');
            $('#updateBy').text(project.updateBy || '-');
            $('#updateTime').text(project.updateTime ? util.toDateString(new Date(project.updateTime), 'yyyy-MM-dd HH:mm:ss') : '-');
            
            // 构建项目信息HTML（简要信息）
            var infoHtml = '<div class="info-row">';
            infoHtml += infoItem('项目编号', project.projectNo || '-');
            infoHtml += infoItem('项目名称', project.projectName || '-');
            infoHtml += infoItem('尺寸', project.size || '-');
            infoHtml += '</div><div class="info-row">';
            infoHtml += infoItem('目的', project.purpose || '-');
            infoHtml += infoItem('轮胎组别', project.tireGroup || '-');
            infoHtml += infoItem('市场', project.market || '-');
            infoHtml += '</div>';
            
            $('#projectInfo').html(infoHtml);
        }
        
        // 渲染概念参数表格
        function renderConceptTable(conceptData) {
            console.log("产品概念数据:", conceptData);
            
            // 根据控制台输出调整数据处理逻辑
            var params = conceptData.params || [];
            // 将params处理成可访问的数组
            if (!Array.isArray(params)) {
                try {
                    params = Object.values(params);
                } catch (e) {
                    console.error("参数转换失败:", e);
                    params = [];
                }
            }
            
            if (params.length === 0) {
                $('#conceptTable').hide();
                $('#emptyConcept').show();
                return;
            }
            
            // 输出每个参数的详细信息
            console.log("参数详细信息:");
            params.forEach(function(param, index) {
                console.log("索引:", index, "ID:", param.id, "名称:", param.paramName);
            });
            
            // 处理目标值 - 根据控制台显示的格式处理
            var target = {};
            try {
                if (typeof conceptData.target === 'object') {
                    target = conceptData.target;
                } else if (typeof conceptData.target === 'string') {
                    target = JSON.parse(conceptData.target);
                }
                // 尝试访问不同的属性名
                if (Object.keys(target).length === 0 && conceptData.targetValues) {
                    target = conceptData.targetValues;
                }
            } catch (e) {
                console.error("目标值解析失败:", e);
                target = {};
            }
            
            // 输出目标值的详细信息
            console.log("目标值详细信息:");
            for (var key in target) {
                console.log("键:", key, "值:", target[key]);
            }
            
            // 智能匹配目标值
            var matchedTarget = smartMatchParam(params, target);
            
            // 处理竞品数据 - 根据控制台显示的格式处理
            var competitors = [];
            try {
                if (Array.isArray(conceptData.competitors)) {
                    competitors = conceptData.competitors;
                } else if (typeof conceptData.competitors === 'object') {
                    competitors = Object.values(conceptData.competitors);
                }
                // 尝试访问不同的属性名
                if (competitors.length === 0 && Array.isArray(conceptData.competitorList)) {
                    competitors = conceptData.competitorList;
                }
            } catch (e) {
                console.error("竞品数据解析失败:", e);
                competitors = [];
            }
            
            // 为每个竞品智能匹配参数值
            competitors.forEach(function(competitor) {
                if (competitor.values) {
                    competitor.matchedValues = smartMatchParam(params, competitor.values);
                } else {
                    competitor.matchedValues = {};
                }
            });
            
            console.log("处理后数据:", {
                "参数数量": params.length,
                "目标值": matchedTarget,
                "竞品数量": competitors.length
            });
            
            $('#conceptTable').show();
            $('#emptyConcept').hide();
            
            var tableHtml = '';
            
            // 构建表头 - 参数名称行
            tableHtml += '<thead><tr><th>产品/参数</th>';
            params.forEach(function(param, index) {
                var paramId = param.id || index;
                var paramName = param.paramName || '参数'+(index+1);
                // 添加删除按钮
                tableHtml += '<th>' + paramName + 
                    ' <button class="layui-btn layui-btn-xs layui-btn-danger delete-param-btn" data-id="' + paramId + '" title="删除此参数">' +
                    '<i class="fas fa-times"></i></button></th>';
            });
            tableHtml += '</tr></thead><tbody>';
            
            // 构建目标值行 - 使用智能匹配的结果
            tableHtml += '<tr><td><strong>目标值</strong> <button class="layui-btn layui-btn-xs set-target-btn"><i class="fas fa-edit"></i> 设置</button></td>';
            params.forEach(function(param, index) {
                var paramId = param.id || index;
                var targetValue = matchedTarget[paramId] || '-';
                
                console.log('目标值检索(智能匹配): 参数ID=' + paramId + ', 参数名=' + param.paramName + ', 结果=' + targetValue);
                
                // 添加可编辑的目标值单元格
                tableHtml += '<td class="editable-cell" data-type="target" data-param-id="' + paramId + '">' + targetValue + '</td>';
            });
            tableHtml += '</tr>';
            
            // 构建竞品行 - 使用智能匹配的结果
            competitors.forEach(function(competitor, compIndex) {
                var compName = competitor.name || '竞品'+(compIndex+1);
                tableHtml += '<tr><td>' + compName + 
                            ' <button class="layui-btn layui-btn-xs layui-btn-danger delete-competitor-btn" data-id="' + competitor.id + '">' +
                            '<i class="fas fa-trash"></i> 删除</button></td>';
                
                console.log('竞品' + (compIndex+1) + '[' + compName + ']的智能匹配值:', competitor.matchedValues);
                
                params.forEach(function(param, index) {
                    var paramId = param.id || index;
                    var paramValue = competitor.matchedValues[paramId] || '-';
                    
                    console.log('竞品参数值(智能匹配): 竞品=' + compName + ', 参数名=' + param.paramName + ', 参数ID=' + paramId + ', 值=' + paramValue);
                    
                    // 添加可编辑的竞品参数值单元格
                    tableHtml += '<td class="editable-cell" data-type="competitor" data-competitor-id="' + competitor.id + '" data-param-id="' + paramId + '">' + paramValue + '</td>';
                });
                tableHtml += '</tr>';
            });
            
            tableHtml += '</tbody>';
            $('#conceptTable').html(tableHtml);
        }
        
        // 生成信息项HTML的辅助函数
        function infoItem(label, value) {
            return '<div class="info-item">' +
                   '<div class="info-label">' + label + ':</div>' +
                   '<div class="info-value">' + value + '</div>' +
                   '</div>';
        }
        
        // 编辑项目信息字段
        $(document).on('click', '.editable-field', function() {
            var $field = $(this);
            var currentValue = $field.text().trim();
            var fieldName = $field.data('field');
            var fieldType = $field.data('type') || 'text';
            
            // 如果已经是编辑状态，不重复创建
            if ($field.find('input, select, textarea').length > 0) {
                return;
            }
            
            // 显示保存按钮
            $('.save-all-btn').show();
    
            // 根据字段类型创建不同的编辑控件
            if (fieldType === 'select') {
                // 创建下拉选择框
                var options = $field.data('options') ? $field.data('options').split(',') : [];
                var $select = $('<select class="field-edit-select"></select>');
                
                // 添加默认空选项
                $select.append('<option value="">请选择</option>');
                
                // 添加选项
                options.forEach(function(option) {
                    var selected = option === currentValue ? 'selected' : '';
                    $select.append('<option value="' + option + '" ' + selected + '>' + option + '</option>');
                });
                
                $field.html($select);
                $select.focus();
                      
          // 处理选择框变化事件
                $select.on('change', function() {
                    var newValue = $(this).val();
                    projectEditData[fieldName] = newValue;
                });
                
                // 处理失去焦点事件
                $select.on('blur', function() {
                    var newValue = $(this).val() || '-';
                    $field.text(newValue);
                });
            } else if (fieldType === 'textarea') {
                // 创建文本区域
                var $textarea = $('<textarea class="field-edit-input" style="min-height: 60px;"></textarea>');
                $textarea.val(currentValue === '-' ? '' : currentValue);
                
                $field.html($textarea);
                $textarea.focus();
                
                // 处理文本区域失去焦点事件
                $textarea.on('blur', function() {
                    var newValue = $(this).val().trim();
                    $field.text(newValue === '' ? '-' : newValue);
                    
                    // 保存修改值
                    if (newValue !== '') {
                        projectEditData[fieldName] = newValue;
                    }
                });
            } else {
                // 创建文本输入框
                var $input = $('<input type="text" class="field-edit-input">');
                $input.val(currentValue === '-' ? '' : currentValue);
                
                $field.html($input);
                $input.focus();
                
                // 处理输入框失去焦点或按下回车事件
                $input.on('blur keypress', function(e) {
                    if (e.type === 'blur' || (e.type === 'keypress' && e.which === 13)) {
                        var newValue = $(this).val().trim();
                        $field.text(newValue === '' ? '-' : newValue);
                        
                        // 保存修改值
                        if (newValue !== '') {
                            projectEditData[fieldName] = newValue;
                        }
                        
                        if (e.type === 'keypress') {
                            e.preventDefault();
                        }
                    }
                });
            }
        });
        
        // 状态更改按钮事件
        $('#changeStatusBtn').on('click', function() {
            var $statusBadge = $('#statusBadge');
            var $statusSelect = $('#statusSelect');
            
            if ($statusSelect.is(':visible')) {
                // 应用更改
                var newStatus = $statusSelect.val();
                var statusText = '';
                var statusClass = '';
                
                switch (newStatus) {
                    case 'pending':
                        statusText = '待审批';
                        statusClass = 'status-pending';
                        break;
                    case 'approved':
                        statusText = '已通过';
                        statusClass = 'status-approved';
                        break;
                    case 'rejected':
                        statusText = '已拒绝';
                        statusClass = 'status-rejected';
                        break;
                    case 'draft':
                        statusText = '草稿';
                        statusClass = 'status-draft';
                        break;
                }
                
                $statusBadge.text(statusText)
                    .removeClass('status-pending status-approved status-rejected status-draft')
                    .addClass(statusClass);
                
                $statusSelect.hide();
                $(this).html('<i class="fas fa-edit"></i> 更改');
                
                // 保存状态更改
                projectEditData['status'] = newStatus;
                $('.save-all-btn').show();
            } else {
                // 显示选择框
                var currentStatus = '';
                if ($statusBadge.hasClass('status-pending')) {
                    currentStatus = 'pending';
                } else if ($statusBadge.hasClass('status-approved')) {
                    currentStatus = 'approved';
                } else if ($statusBadge.hasClass('status-rejected')) {
                    currentStatus = 'rejected';
                } else if ($statusBadge.hasClass('status-draft')) {
                    currentStatus = 'draft';
                }
                
                $statusSelect.val(currentStatus).show();
                $(this).html('<i class="fas fa-check"></i> 应用');
            }
        });
        
        // 保存所有修改按钮事件
        $('#saveAllBtn').on('click', function() {
            if (Object.keys(projectEditData).length === 0) {
                layer.msg('没有需要保存的修改', {icon: 2});
                return;
            }
            
            // 添加项目ID
            projectEditData.id = projectId;
            
            // 发送更新请求
            $.ajax({
                url: '/boot/api/eto-project/' + projectId,
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(projectEditData),
                success: function(res) {
                    if (res.code === 0) {
                        layer.msg('保存成功', {icon: 1});
                        projectEditData = {}; // 清空修改数据
                        $('.save-all-btn').hide();
                        loadProjectDetail(projectId); // 重新加载项目详情
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('系统错误', {icon: 2});
                }
            });
        });
        
        // 编辑产品概念表格单元格的事件处理
        $(document).on('click', '.editable-cell', function() {
            var $cell = $(this);
            var currentValue = $cell.text().trim();
            var type = $cell.data('type');
            var paramId = $cell.data('paramId');
            var competitorId = $cell.data('competitorId');
            
            // 如果已经是编辑状态，不重复创建
            if ($cell.find('input').length > 0) {
                return;
            }
            
            // 创建输入框
            var $input = $('<input type="text" class="layui-input cell-edit-input" style="height: 30px; padding: 0 5px;">')
                .val(currentValue === '-' ? '' : currentValue);
            
            // 替换单元格内容为输入框
            $cell.html($input);
            $input.focus();
            
            // 处理输入框失去焦点或按下回车时的保存操作
            $input.on('blur keypress', function(e) {
                if (e.type === 'blur' || (e.type === 'keypress' && e.which === 13)) {
                    if (e.type === 'keypress') {
                        e.preventDefault();
                    }
                    
                    var newValue = $(this).val().trim();
                    
                    // 如果没有输入，显示为"-"
                    $cell.text(newValue === '' ? '-' : newValue);
                    
                    // 根据类型保存值
                    if (type === 'target') {
                        saveTargetValue(paramId, newValue);
                    } else if (type === 'competitor') {
                        saveCompetitorValue(competitorId, paramId, newValue);
                    }
                }
            });
        });
        
        // 保存目标值
        function saveTargetValue(paramId, value) {
            if (!paramId) return;
            
            var targetData = {
                projectId: projectId,
                targetValues: {}
            };
            
            // 只有当输入不为空时才发送
            if (value !== '-' && value !== '') {
                targetData.targetValues[paramId] = value;
                
                $.ajax({
                    url: '/boot/api/eto-project-targets',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(targetData),
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('保存成功', {icon: 1});
                        } else {
                            layer.msg(res.msg || '保存失败', {icon: 2});
                            loadProjectDetail(projectId); // 失败时重新加载
                        }
                    },
                    error: function() {
                        layer.msg('系统错误', {icon: 2});
                        loadProjectDetail(projectId); // 失败时重新加载
                    }
                });
            }
        }
        
        // 保存竞品参数值
        function saveCompetitorValue(competitorId, paramId, value) {
            if (!competitorId || !paramId) return;
            
            var competitorData = {
                projectId: projectId,
                competitorId: competitorId,
                paramValues: []
            };
            
            // 只有当输入不为空时才发送
            if (value !== '-' && value !== '') {
                competitorData.paramValues.push({
                    paramId: paramId,
                    value: value
                });
                
                $.ajax({
                    url: '/boot/api/eto-project-competitor-values',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(competitorData),
                    success: function(res) {
                        if (res.code === 0) {
                            layer.msg('保存成功', {icon: 1});
                        } else {
                            layer.msg(res.msg || '保存失败', {icon: 2});
                            loadProjectDetail(projectId); // 失败时重新加载
                        }
                    },
                    error: function() {
                        layer.msg('系统错误', {icon: 2});
                        loadProjectDetail(projectId); // 失败时重新加载
                    }
                });
            }
        }
        
        // 绑定返回首页按钮
        $('#homeBtn').on('click', function() {
            window.location.href = '/boot/index';
        });
        
        // 绑定返回列表按钮
        $('#backToListBtn, #backBtn').on('click', function() {
        window.location.href = '/boot/eto-projects';
        });
    });
    </script>
</body>
</html> 