package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.*;
import java.util.List;
import java.util.Map;

/**
 * 半部件管理服务接口
 */
public interface IPartManagementService {
    
    // ========== 胎面管理 ==========
    /**
     * 获取所有胎面部件
     */
    List<PartTread> getAllTreadParts();
    
    /**
     * 根据名称获取胎面部件
     */
    PartTread getTreadPartByName(String partName);
    
    /**
     * 保存胎面部件
     */
    boolean saveTreadPart(PartTread partTread);
    
    // ========== 胎侧管理 ==========
    /**
     * 获取所有胎侧部件
     */
    List<PartSidewall> getAllSidewallParts();
    
    /**
     * 根据名称获取胎侧部件
     */
    PartSidewall getSidewallPartByName(String partName);
    
    /**
     * 保存胎侧部件
     */
    boolean saveSidewallPart(PartSidewall partSidewall);
    
    // ========== 综合查询 ==========
    /**
     * 根据部件类型获取部件列表
     * @param partType 部件类型：tread, sidewall, carcass, innerliner, capply, beltlayer, beadwire, apex
     */
    List<Map<String, Object>> getPartsByType(String partType);
    
    /**
     * 获取所有部件类型的统计信息
     */
    Map<String, Integer> getPartStatistics();
    
    /**
     * 根据SAP代码搜索部件
     */
    List<Map<String, Object>> searchPartsBySapCode(String sapCode);
    
    /**
     * 获取部件的详细信息（包含所有字段）
     */
    Map<String, Object> getPartDetails(String partType, String partName);
    
    /**
     * 批量导入部件数据
     */
    boolean batchImportParts(String partType, List<Map<String, Object>> partDataList);
    
    /**
     * 删除部件
     */
    boolean deletePart(String partType, String partName);
    
    /**
     * 更新部件信息
     */
    boolean updatePart(String partType, String partName, Map<String, Object> updateData);
}
