package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoMoldinfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ETO模具信息服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoMoldinfoService extends IService<EtoMoldinfo> {
    
    /**
     * 根据方案ID获取模具信息
     * @param schemeId 方案ID
     * @return 模具信息
     */
    EtoMoldinfo getBySchemeId(Integer schemeId);
}
