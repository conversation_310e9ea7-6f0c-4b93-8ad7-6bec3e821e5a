<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目列表 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .eto-list-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #EBEEF5;
        }

        .header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .header h2 i {
            margin-right: 10px;
            color: #409EFF;
        }

        .search-bar {
            margin-bottom: 20px;
            padding: 15px;
            background: #F5F7FA;
            border-radius: 8px;
        }

        .search-bar .layui-form-item {
            margin-bottom: 0;
        }

        .operation-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .operation-bar .layui-btn {
            margin-right: 10px;
        }

        .operation-bar .layui-btn:last-child {
            margin-right: 0;
        }

        .layui-badge {
            padding: 0 8px;
            height: 22px;
            line-height: 22px;
            border-radius: 11px;
            font-weight: normal;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            color: #fff;
            font-size: 12px;
        }

        .status-pending {
            background-color: #E6A23C;
        }

        .status-approved {
            background-color: #67C23A;
        }

        .status-rejected {
            background-color: #F56C6C;
        }

        .status-draft {
            background-color: #909399;
        }

        .empty-tips {
            padding: 60px 0;
            text-align: center;
            color: #909399;
        }

        .empty-tips i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #DCDFE6;
        }

        .empty-tips p {
            font-size: 16px;
        }

        .layui-table-tool {
            background-color: #F5F7FA;
            border-radius: 4px 4px 0 0;
            padding: 10px 16px;
        }

        .table-footer {
            margin-top: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-info {
            color: #606266;
        }
        
        /* 自定义表格操作按钮 */
        .table-btn {
            padding: 3px 5px;
            font-size: 12px;
            margin: 0 2px;
            border-radius: 3px;
            min-width: 46px;
            height: 24px;
            line-height: 18px;
            text-align: center;
            display: inline-block;
            white-space: nowrap;
        }
        
        .table-btn i {
            margin-right: 3px;
            font-size: 12px;
        }

        /* 操作列样式 */
        .table-ops {
            white-space: nowrap;
            text-align: center;
        }
        
        /* 表格单元格样式 */
        .layui-table-cell {
            height: 38px;
            line-height: 28px;
            padding: 5px 10px;
            position: relative;
            overflow: visible;
            text-overflow: clip;
        }
        
        /* 修改表格内容列宽 */
        .layui-table-main {
            width: 100%;
        }

        .layui-table-fixed {
            display: none;
        }

        /* 分页控件样式优化 */
        .layui-table-page {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
        }
        
        /* 导航按钮美化 */
        .nav-buttons {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .nav-btn {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            font-size: 13px;
            color: #606266;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 3px;
            margin-right: 8px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .nav-btn:hover {
            color: #409EFF;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
        
        .nav-btn i {
            margin-right: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="nav-buttons">
            <button class="nav-btn" id="homeBtn">
                <i class="fas fa-home"></i> 返回首页
            </button>
        </div>
        
        <div class="eto-list-container">
            <div class="header">
                <h2><i class="fas fa-project-diagram"></i>项目列表</h2>
            </div>

            <!-- 搜索工具栏 -->
            <div class="search-bar">
                <form class="layui-form" id="searchForm" lay-filter="searchForm">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">项目编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="projectNo" placeholder="请输入" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">项目名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="projectName" placeholder="请输入" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">尺寸</label>
                            <div class="layui-input-inline">
                                <input type="text" name="size" placeholder="请输入" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="">全部</option>
                                    <option value="pending">待审批</option>
                                    <option value="approved">已通过</option>
                                    <option value="rejected">已拒绝</option>
                                    <option value="draft">草稿</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn" lay-submit lay-filter="searchBtn">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="reset" class="layui-btn layui-btn-primary" id="resetBtn">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 操作按钮区 -->
            <div class="operation-bar">
                <div>
                    <button class="layui-btn" id="createEtoBtn">
                        <i class="fas fa-plus"></i> 创建ETO项目
                    </button>
                    <button class="layui-btn layui-btn-danger" id="batchDeleteBtn" disabled>
                        <i class="fas fa-trash-alt"></i> 批量删除
                    </button>
                </div>
                <div>
                    <button class="layui-btn layui-btn-primary" id="exportBtn">
                        <i class="fas fa-file-export"></i> 导出
                    </button>
                    <button class="layui-btn layui-btn-primary" id="refreshBtn">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="etoProjectTable" lay-filter="etoProjectTable"></table>
            
            <!-- 表格工具条模板 -->
            <script type="text/html" id="tableToolbar">
                <div class="table-ops">
                    <button class="layui-btn layui-btn-xs table-btn" lay-event="view">
                        <i class="fas fa-eye"></i>查看
                    </button>
                    <button class="layui-btn layui-btn-normal layui-btn-xs table-btn" lay-event="scheme">
                        <i class="fas fa-sitemap"></i>方案
                    </button>
                    <button class="layui-btn layui-btn-danger layui-btn-xs table-btn" lay-event="delete">
                        <i class="fas fa-trash-alt"></i>删除
                    </button>
                </div>
            </script>
            
            <!-- 状态模板 -->
            <script type="text/html" id="statusTpl">
                {{#  if(d.status === 'pending'){ }}
                <span class="status-badge status-pending">待审批</span>
                {{#  } else if(d.status === 'approved'){ }}
                <span class="status-badge status-approved">已通过</span>
                {{#  } else if(d.status === 'rejected'){ }}
                <span class="status-badge status-rejected">已拒绝</span>
                {{#  } else if(d.status === 'draft'){ }}
                <span class="status-badge status-draft">草稿</span>
                {{#  } else { }}
                {{ d.status || '-' }}
                {{#  } }}
            </script>
            
            <!-- 空数据模板 -->
            <script type="text/html" id="emptyTpl">
                <div class="empty-tips">
                    <i class="fas fa-inbox"></i>
                    <p>暂无数据</p>
                    <p>您可以点击"创建ETO项目"按钮添加新项目</p>
                </div>
            </script>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['table', 'form', 'layer'], function(){
        var table = layui.table;
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.jquery;
        
        // 绑定返回首页按钮
        $('#homeBtn').on('click', function() {
            window.location.href = '/boot/index';
        });
        
        // 初始化表格
        var tableIns = table.render({
            elem: '#etoProjectTable',
            url: '/boot/api/eto-projects',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                {type: 'checkbox', fixed: 'left', width: 40},
                {field: 'projectNo', title: '项目编号', width: 100, sort: true},
                {field: 'projectName', title: '项目名称', width: 110},
                {field: 'size', title: '尺寸', width: 100},
                {field: 'purpose', title: '目的', width: 70},
                {field: 'tireGroup', title: '轮胎组别', width: 90},
                {field: 'market', title: '市场', width: 80},
                {field: 'applicant', title: '申请人', width: 80},
                {field: 'status', title: '状态', width: 80, templet: '#statusTpl'},
                {field: 'createTime', title: '创建时间', width: 140, sort: true, 
                    templet: function(d){
                        if(!d.createTime) return '-';
                        return layui.util.toDateString(new Date(d.createTime), 'yyyy-MM-dd HH:mm');
                    }
                },
                {title: '操作', toolbar: '#tableToolbar', width: 210, align: 'center', fixed: 'right'}
            ]],
            response: {
                statusCode: 0
            },
            parseData: function(res){
                // 添加调试日志，帮助排查问题
                console.log("API返回数据:", res);
                
                // 添加安全检查，防止undefined错误
                if (!res || !res.data) {
                    console.error("API返回数据格式异常:", res);
                    return {
                        "code": res ? res.code : -1,
                        "msg": res ? res.msg || "获取数据失败" : "无响应数据",
                        "count": 0,
                        "data": []
                    };
                }
                
                return {
                    "code": res.code,
                    "msg": res.msg,
                    "count": res.data.total,
                    "data": res.data.list || []
                };
            },
            text: {
                none: $('#emptyTpl').html()
            },
            done: function(res){
                // 启用/禁用批量删除按钮
                $('#batchDeleteBtn').attr('disabled', !res.data || res.data.length === 0);
                
                // 添加表格外部的信息显示
                if(res.count) {
                    $('.layui-table-page').append(
                        '<div class="table-info">共 ' + res.count + ' 条记录</div>'
                    );
                }
            }
        });
        
        // 搜索表单提交
        form.on('submit(searchBtn)', function(data){
            tableIns.reload({
                where: data.field,
                page: {
                    curr: 1
                }
            });
            return false;
        });
        
        // 重置搜索
        $('#resetBtn').on('click', function(){
            $('#searchForm')[0].reset();
            form.render('select');
            tableIns.reload({
                where: {},
                page: {
                    curr: 1
                }
            });
        });
        
        // 刷新数据
        $('#refreshBtn').on('click', function(){
            tableIns.reload();
        });
        
        // 新建ETO项目
        $('#createEtoBtn').on('click', function(){
            window.location.href = '/boot/create-eto-project';
        });
        
        // 批量删除
        $('#batchDeleteBtn').on('click', function(){
            if($(this).attr('disabled')) return;
            
            var checkStatus = table.checkStatus('etoProjectTable');
            var data = checkStatus.data;
            if(data.length === 0){
                layer.msg('请先选择要删除的项目', {icon: 2});
                return;
            }
            
            var ids = data.map(function(item){
                return item.id;
            });
            
            layer.confirm('确定要删除选中的'+data.length+'个项目吗？<br>删除后将无法恢复！', {
                icon: 3,
                title: '删除确认'
            }, function(index){
                layer.close(index);
                layer.load(2);
                
                $.ajax({
                    url: '/boot/api/eto-projects/batch',
                    type: 'DELETE',
                    contentType: 'application/json',
                    data: JSON.stringify(ids),
                    success: function(res){
                        layer.closeAll('loading');
                        if(res.code === 0){
                            layer.msg('删除成功', {icon: 1});
                            tableIns.reload();
                        }else{
                            layer.msg(res.msg || '删除失败，请重试', {icon: 2});
                        }
                    },
                    error: function(){
                        layer.closeAll('loading');
                        layer.msg('系统错误，请重试', {icon: 2});
                    }
                });
            });
        });
        
        // 导出功能
        $('#exportBtn').on('click', function(){
            // 获取当前搜索条件
            var searchParams = form.val('searchForm');
            
            layer.confirm('确定要导出当前搜索结果吗？', function(index){
                layer.close(index);
                
                // 构建导出URL
                var exportUrl = '/boot/api/eto-projects/export';
                if(Object.keys(searchParams).length > 0) {
                    exportUrl += '?' + $.param(searchParams);
                }
                
                // 发起下载
                window.location.href = exportUrl;
            });
        });
        
        // 监听工具条点击事件
        table.on('tool(etoProjectTable)', function(obj){
            var data = obj.data;
            var event = obj.event;
            
            if(event === 'view'){
                // 查看项目详情
                window.location.href = '/boot/eto-project/' + data.id;
            } else if(event === 'scheme'){
                // 查看/编辑方案
                window.location.href = '/boot/eto-project/scheme/' + data.id;
            } else if(event === 'delete'){
                // 删除项目
                layer.confirm('确定要删除该项目吗？<br>删除后将无法恢复！', {
                    icon: 3,
                    title: '删除确认'
                }, function(index){
                    layer.close(index);
                    layer.load(2);
                    
                    $.ajax({
                        url: '/boot/api/eto-project/' + data.id,
                        type: 'DELETE',
                        success: function(res){
                            layer.closeAll('loading');
                            if(res.code === 0){
                                layer.msg('删除成功', {icon: 1});
                                obj.del();
                                // 如果当前页没有数据了，跳转到上一页
                                var curr = $('.layui-laypage-curr em:last').text();
                                if($('.layui-table-main tr').length === 1 && curr > 1) {
                                    tableIns.reload({
                                        page: {
                                            curr: curr - 1
                                        }
                                    });
                                }
                            }else{
                                layer.msg(res.msg || '删除失败，请重试', {icon: 2});
                            }
                        },
                        error: function(){
                            layer.closeAll('loading');
                            layer.msg('系统错误，请重试', {icon: 2});
                        }
                    });
                });
            }
        });
        
        // 监听表格复选框选择
        table.on('checkbox(etoProjectTable)', function(obj){
            var checkStatus = table.checkStatus('etoProjectTable');
            $('#batchDeleteBtn').attr('disabled', checkStatus.data.length === 0);
        });
    });
    </script>
</body>
</html> 