package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoProjectScheme;
import com.oracle.SpringBoot.mapper.EtoProjectSchemeMapper;
import com.oracle.SpringBoot.service.IEtoProjectSchemeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <p>
 * ETO项目方案服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoProjectSchemeServiceImpl extends ServiceImpl<EtoProjectSchemeMapper, EtoProjectScheme> implements IEtoProjectSchemeService {

    @Override
    public List<EtoProjectScheme> listByProjectId(Long projectId) {
        return this.lambdaQuery()
                .eq(EtoProjectScheme::getProjectId, projectId)
                .orderByAsc(EtoProjectScheme::getCreateTime)
                .list();
    }
} 