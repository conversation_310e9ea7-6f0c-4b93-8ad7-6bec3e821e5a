package com.oracle.SpringBoot.entity;

import java.util.Date;

import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ETO方案实体类 - 对应原有表ETO_Scheme
 */
@Data
@Table(name = "ETO_Scheme")
public class EtoScheme {
    @Id
    private Integer id;
    private String code;          // 代码
    private String plant;         // 工厂
    private String schemeName;    // 方案名称
    private Integer schemeIndex;  // 方案序号，如方案1、方案2
    private Integer etoId;        // 关联到ETO表的ID
    private String status;        // 状态
    private String createBy;      // 创建人
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间
}
