package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("myLog")
public class Mylog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "uid", type = IdType.AUTO)
    private Integer uid;

    private String people;

    private String behaviour;

    private LocalDate date;


}
