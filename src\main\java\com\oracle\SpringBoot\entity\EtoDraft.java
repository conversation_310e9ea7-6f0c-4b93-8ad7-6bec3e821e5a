package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * ETO草稿实体类
 */
@Data
@TableName("eto_draft")
public class EtoDraft {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private Long projectId;       // 关联项目ID
    private String draftData;     // JSON格式的草稿数据
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间
    private String createBy;      // 创建人
} 