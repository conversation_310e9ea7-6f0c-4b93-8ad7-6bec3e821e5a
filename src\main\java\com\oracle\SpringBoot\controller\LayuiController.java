package com.oracle.SpringBoot.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import com.oracle.SpringBoot.service.IEtoMainService;
import com.oracle.SpringBoot.service.IEtoBasicinfoService;
import com.oracle.SpringBoot.service.IEtoSchemeService;
import com.oracle.SpringBoot.service.IEtoRubberinfoService;
import com.oracle.SpringBoot.service.IEtoDesigninfoService;
import com.oracle.SpringBoot.service.IEtoTestdataService;
import com.oracle.SpringBoot.service.IEtoMoldinfoService;
import com.oracle.SpringBoot.service.IEtoProjectService;
import com.oracle.SpringBoot.entity.EtoMain;
import com.oracle.SpringBoot.entity.EtoBasicinfo;
import com.oracle.SpringBoot.entity.EtoScheme;
import com.oracle.SpringBoot.entity.EtoRubberinfo;
import com.oracle.SpringBoot.entity.EtoDesigninfo;
import com.oracle.SpringBoot.entity.EtoTestdata;
import com.oracle.SpringBoot.entity.EtoMoldinfo;
import com.oracle.SpringBoot.entity.EtoProject;
import java.util.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

@Controller
public class LayuiController {
    private static final Logger logger = LoggerFactory.getLogger(LayuiController.class);

    @Autowired
    private IEtoMainService etoMainService;
    
    @Autowired
    private IEtoBasicinfoService etoBasicinfoService;
    
    @Autowired
    private IEtoSchemeService etoSchemeService;
    
    @Autowired
    private IEtoRubberinfoService etoRubberinfoService;
    
    @Autowired
    private IEtoDesigninfoService etoDesigninfoService;
    
    @Autowired
    private IEtoTestdataService etoTestdataService;
    
    @Autowired
    private IEtoMoldinfoService etoMoldinfoService;
    
    @Autowired
    private IEtoProjectService etoProjectService;

    @RequestMapping("/index")
    public String Index(HttpServletRequest request) {
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "主页面");
        return "index";
    }

    @RequestMapping("/quit")
    public String quit(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 在 {} 退出系统", username, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        session.invalidate();
        return "login-out";
    }
    
    @RequestMapping("/forgetPassword")
    public String forgetPassword(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 xx 忘记密码", username);
        session.invalidate();
        return "ForgetPassword";
    }
    
    @RequestMapping("/changePassword")
    public String changePassword(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 xx 更改密码", username);
        session.invalidate();
        return "ChangePassword";
    }
    
    @RequestMapping("/log")
    public String log(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "用户日志操作页面");
        return "log";
    }

    @RequestMapping("/create-eto")
    public String createEto(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "创建ETO表单页面");
        return "create-eto";
    }

    @RequestMapping("/my-eto")
    public String myEto(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "我的ETO表单页面");
        return "my-eto";
    }
    
    @RequestMapping("/test-api")
    public String testApi(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "API测试页面");
        return "test-api";
    }
    
    @RequestMapping("/scheme-design")
    public String schemeDesign(HttpServletRequest request, @RequestParam String etoId){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "方案设计页面");
        return "scheme-design";
    }
    
    @RequestMapping("/create-eto-project")
    public String createEtoProject(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "创建ETO项目页面");
        return "create-eto-project";
    }

    @RequestMapping("/test-part-selector")
    public String testPartSelector(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "半部件选择测试页面");
        return "test-part-selector";
    }
    
    @RequestMapping("/eto-projects")
    public String etoProjects(HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "ETO项目列表页面");
        return "eto-projects";
    }
    
    @RequestMapping("/eto-project/{id}")
    public String viewProject(@PathVariable Long id, HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "ETO项目详情页面");
        return "project-detail";
    }
    
    @RequestMapping("/eto-project/scheme/{id}")
    public String projectScheme(@PathVariable Long id, HttpServletRequest request){
        HttpSession session = request.getSession();
        String username = (String) session.getAttribute("username");
        logger.info("用户 {} 访问了 {} 页面", username, "ETO项目方案管理页面");
        return "eto-project-scheme";
    }

    @PostMapping("/saveEtoForm")
    @ResponseBody
    public Map<String, Object> saveEtoForm(@RequestBody Map<String, Object> formData, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 记录日志
            logger.info("用户 {} 保存ETO表单数据: {}", username, formData);
            
            // 1. 创建ETO主记录
            EtoMain etoMain = new EtoMain();
            String etoCode = "ETO" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
            etoMain.setCode(etoCode);
            etoMain.setPlant((String) formData.getOrDefault("plant", "DEFAULT"));
            etoMain.setFullId(etoCode);
            etoMain.setStatus("DRAFT");
            etoMain.setCreateBy(username);
            etoMain.setCreateTime(new Date());
            etoMain.setUpdateTime(new Date());
            
            // 关联到项目
            if (formData.containsKey("projectId")) {
                Long projectId = Long.valueOf(String.valueOf(formData.get("projectId")));
                etoMain.setProjectId(projectId);
            }
            
            etoMainService.save(etoMain);
            
            // 2. 保存基本信息
            EtoBasicinfo basicInfo = new EtoBasicinfo();
            basicInfo.setEtoId(etoMain.getId());
            
            // 设置基本信息字段
            Map<String, Object> basicData = (Map<String, Object>) formData.getOrDefault("basicInfo", new HashMap<>());
            basicInfo.setSizeLiSi((String) basicData.getOrDefault("sizeLiSi", ""));
            basicInfo.setPattern((String) basicData.getOrDefault("pattern", ""));
            basicInfo.setProductCode((String) basicData.getOrDefault("productCode", ""));
            basicInfo.setStatus("DRAFT");
            basicInfo.setSerialNumber((String) basicData.getOrDefault("serialNumber", ""));
            basicInfo.setSap((String) basicData.getOrDefault("sap", ""));
            basicInfo.setMarket((String) basicData.getOrDefault("market", ""));
            basicInfo.setConstructionGroup((String) basicData.getOrDefault("constructionGroup", ""));
            basicInfo.setCurrentTest((String) basicData.getOrDefault("currentTest", ""));
            basicInfo.setSubject((String) basicData.getOrDefault("subject", ""));
            basicInfo.setMainTestItems((String) basicData.getOrDefault("mainTestItems", ""));
            basicInfo.setRequiredNumber((String) basicData.getOrDefault("requiredNumber", "0"));
            basicInfo.setMoldDrawingUpdate((String) basicData.getOrDefault("moldDrawingUpdate", ""));
            basicInfo.setElectronPrevulcanization((String) basicData.getOrDefault("electronPrevulcanization", ""));
            basicInfo.setFullSpringVent((String) basicData.getOrDefault("fullSpringVent", ""));
            basicInfo.setImpactTest((String) basicData.getOrDefault("impactTest", ""));
            basicInfo.setStandardRimWidth((String) basicData.getOrDefault("standardRimWidth", ""));
            basicInfo.setCreateTime(new Date());
            basicInfo.setUpdateTime(new Date());
            
            etoBasicinfoService.save(basicInfo);
            
            // 3. 创建默认方案
            EtoScheme scheme = new EtoScheme();
            scheme.setEtoId(etoMain.getId());
            scheme.setSchemeName("方案 1");
            scheme.setSchemeIndex(1);
            scheme.setStatus("DRAFT");
            scheme.setCreateBy(username);
            scheme.setCreateTime(new Date());
            scheme.setUpdateTime(new Date());
            etoSchemeService.save(scheme);
            
            // 4. 保存胶料信息
            Map<String, Object> rubberData = (Map<String, Object>) formData.getOrDefault("rubberInfo", new HashMap<>());
            EtoRubberinfo rubberInfo = new EtoRubberinfo();
            rubberInfo.setSchemeId(scheme.getId());
            
            // 设置胶料信息字段
            rubberInfo.setInnerLinerType1((String) rubberData.getOrDefault("innerLinerType1", ""));
            rubberInfo.setInnerLinerThickness1((String) rubberData.getOrDefault("innerLinerThickness1", "0"));
            rubberInfo.setInnerLinerWidth1((String) rubberData.getOrDefault("innerLinerWidth1", "0"));
            rubberInfo.setInnerLinerType2((String) rubberData.getOrDefault("innerLinerType2", ""));
            rubberInfo.setInnerLinerThickness2((String) rubberData.getOrDefault("innerLinerThickness2", "0"));
            rubberInfo.setInnerLinerWidth2((String) rubberData.getOrDefault("innerLinerWidth2", "0"));
            rubberInfo.setSidewallCompound1((String) rubberData.getOrDefault("sidewallCompound1", ""));
            rubberInfo.setSidewallCompound2((String) rubberData.getOrDefault("sidewallCompound2", ""));
            rubberInfo.setBeadFillerCompound((String) rubberData.getOrDefault("beadFillerCompound", ""));
            rubberInfo.setBeadFillerHT((String) rubberData.getOrDefault("beadFillerHT", "0"));
            rubberInfo.setTreadCompound((String) rubberData.getOrDefault("treadCompound", ""));
            rubberInfo.setTreadWing((String) rubberData.getOrDefault("treadWing", ""));
            rubberInfo.setTreadBaseType((String) rubberData.getOrDefault("treadBaseType", ""));
            rubberInfo.setTreadBaseThickness((String) rubberData.getOrDefault("treadBaseThickness", "0"));
            rubberInfo.setTreadChimney((String) rubberData.getOrDefault("treadChimney", ""));
            rubberInfo.setCreateTime(new Date());
            rubberInfo.setUpdateTime(new Date());
            
            etoRubberinfoService.save(rubberInfo);
            
            // 5. 保存骨架材料信息
            Map<String, Object> skeletonData = (Map<String, Object>) formData.getOrDefault("skeletonMaterial", new HashMap<>());
            // EtoSkeletonmaterial skeletonMaterial = new EtoSkeletonmaterial(); // Removed
            // skeletonMaterial.setSchemeId(scheme.getId()); // Removed
            
            // 设置骨架材料信息字段
            // skeletonMaterial.setPlyMaterial((String) skeletonData.getOrDefault("plyMaterial", "")); // Removed
            // skeletonMaterial.setPlyModel((String) skeletonData.getOrDefault("plyModel", "")); // Removed
            // skeletonMaterial.setPlyCount((String) skeletonData.getOrDefault("plyCount", "")); // Removed
            // skeletonMaterial.setPlyCompound((String) skeletonData.getOrDefault("plyCompound", "")); // Removed
            // skeletonMaterial.setCarcassPlyWidth1((String) skeletonData.getOrDefault("carcassPlyWidth1", "0")); // Removed
            // skeletonMaterial.setCarcassPlyWidth2((String) skeletonData.getOrDefault("carcassPlyWidth2", "0")); // Removed
            // skeletonMaterial.setTurnupHeight((String) skeletonData.getOrDefault("turnupHeight", "0")); // Removed
            // skeletonMaterial.setSettingPosition((String) skeletonData.getOrDefault("settingPosition", "0")); // Removed
            // skeletonMaterial.setCarcassAngle((String) skeletonData.getOrDefault("carcassAngle", "0")); // Removed
            // skeletonMaterial.setCreateTime(new Date()); // Removed
            // skeletonMaterial.setUpdateTime(new Date()); // Removed
            
            // etoSkeletonmaterialService.save(skeletonMaterial); // Removed
            
            // 6. 保存设计信息
            Map<String, Object> designData = (Map<String, Object>) formData.getOrDefault("designInfo", new HashMap<>());
            EtoDesigninfo designInfo = new EtoDesigninfo();
            designInfo.setSchemeId(scheme.getId());
            
            // 设置设计信息字段
            designInfo.setDrumWidth((String) designData.getOrDefault("drumWidth", "0"));
            designInfo.setBeltDrumCircumference((String) designData.getOrDefault("beltDrumCircumference", "0"));
            designInfo.setCircumference((String) designData.getOrDefault("circumference", "0"));
            designInfo.setBuildingType((String) designData.getOrDefault("buildingType", ""));
            designInfo.setCuringTemperature((String) designData.getOrDefault("curingTemperature", ""));
            designInfo.setPciChuckWidth((String) designData.getOrDefault("pciChuckWidth", "0"));
            designInfo.setInnerBeadDistance((String) designData.getOrDefault("innerBeadDistance", "0"));
            designInfo.setCreateTime(new Date());
            designInfo.setUpdateTime(new Date());
            
            etoDesigninfoService.save(designInfo);
            
            // 7. 保存检测数据
            Map<String, Object> testData = (Map<String, Object>) formData.getOrDefault("testData", new HashMap<>());
            EtoTestdata testDataEntity = new EtoTestdata();
            testDataEntity.setSchemeId(scheme.getId());
            
            // 设置检测数据字段
            testDataEntity.setMainGrooveBasePerimeter((String) testData.getOrDefault("mainGrooveBasePerimeter", "0"));
            testDataEntity.setCalculatedBeadWidth((String) testData.getOrDefault("calculatedBeadWidth", ""));
            testDataEntity.setCuredBeadWidth((String) testData.getOrDefault("curedBeadWidth", "0"));
            testDataEntity.setTreadColorLine((String) testData.getOrDefault("treadColorLine", ""));
            testDataEntity.setCalculatedWeight((String) testData.getOrDefault("calculatedWeight", "0"));
            testDataEntity.setRiskIdentification((String) testData.getOrDefault("riskIdentification", ""));
            testDataEntity.setConfirmation((String) testData.getOrDefault("confirmation", ""));
            testDataEntity.setComment((String) testData.getOrDefault("comment", ""));
            testDataEntity.setCreateTime(new Date());
            testDataEntity.setUpdateTime(new Date());
            
            etoTestdataService.save(testDataEntity);
            
            // 8. 保存模具信息
            Map<String, Object> moldData = (Map<String, Object>) formData.getOrDefault("moldInfo", new HashMap<>());
            EtoMoldinfo moldInfo = new EtoMoldinfo();
            moldInfo.setSchemeId(scheme.getId());
            
            // 设置模具信息字段
            moldInfo.setTdw((String) moldData.getOrDefault("tdw", "0"));
            moldInfo.setOd((String) moldData.getOrDefault("od", "0"));
            moldInfo.setSw((String) moldData.getOrDefault("sw", "0"));
            moldInfo.setGd((String) moldData.getOrDefault("gd", "0"));
            moldInfo.setSeaLandRatio((String) moldData.getOrDefault("seaLandRatio", ""));
            moldInfo.setSeaLandRatioDiff((String) moldData.getOrDefault("seaLandRatioDiff", ""));
            moldInfo.setBeadWidth((String) moldData.getOrDefault("beadWidth", "0"));
            moldInfo.setCreateTime(new Date());
            moldInfo.setUpdateTime(new Date());
            
            etoMoldinfoService.save(moldInfo);
            
            // 创建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "保存成功");
            
            Map<String, Object> data = new HashMap<>();
            data.put("etoId", etoMain.getId());
            data.put("etoCode", etoCode);
            data.put("schemeId", scheme.getId());
            response.put("data", data);
            
            logger.info("返回响应数据: {}", response);
            return response;
        } catch (Exception e) {
            logger.error("保存ETO表单失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "保存失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @PostMapping("/api/layui/eto-project/draft")
    @ResponseBody
    public Map<String, Object> saveProjectDraft(@RequestBody Map<String, Object> formData, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 记录日志
            logger.info("用户 {} 保存ETO项目草稿: {}", username, formData);
            
            // TODO: 添加保存项目草稿的逻辑
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "草稿保存成功");
            return response;
        } catch (Exception e) {
            logger.error("保存ETO项目草稿失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "保存失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @PostMapping("/api/layui/eto-project")
    @ResponseBody
    public Map<String, Object> createProject(@RequestBody Map<String, Object> formData, HttpServletRequest request) {
        // 此方法与EtoProjectController中的方法冲突，暂时禁用
        Map<String, Object> response = new HashMap<>();
        response.put("code", 1);
        response.put("msg", "此API端点已被禁用，请使用/api/eto-project端点");
        return response;
        
        /*
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 记录日志
            logger.info("用户 {} 创建ETO项目: {}", username, formData);
            
            // 创建项目实体
            EtoProject project = new EtoProject();
            project.setProjectNo("PRJ" + UUID.randomUUID().toString().substring(0, 6).toUpperCase());
            project.setProjectName((String) formData.getOrDefault("projectName", ""));
            project.setTireGroup((String) formData.getOrDefault("tireGroup", ""));
            project.setSize((String) formData.getOrDefault("size", ""));
            project.setMarket((String) formData.getOrDefault("market", ""));
            project.setBrand((String) formData.getOrDefault("brand", ""));
            project.setStatus("active");
            project.setCreateBy(username);
            project.setCreateTime(new Date());
            project.setUpdateTime(new Date());
            
            // 保存项目
            etoProjectService.save(project);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "项目创建成功");
            
            Map<String, Object> data = new HashMap<>();
            data.put("projectId", project.getId());
            data.put("projectNo", project.getProjectNo());
            response.put("data", data);
            
            return response;
        } catch (Exception e) {
            logger.error("创建ETO项目失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "创建失败：" + e.getMessage());
            return errorResponse;
        }
        */
    }

    @GetMapping("/api/layui/eto-projects")
    @ResponseBody
    public Map<String, Object> getEtoProjects(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            // 构建查询条件
            Page<EtoProject> pageParam = new Page<>(page, limit);
            List<EtoProject> projectList = etoProjectService.lambdaQuery()
                    .eq(status != null && !status.isEmpty(), EtoProject::getStatus, status)
                    .like(keyword != null && !keyword.isEmpty(), EtoProject::getProjectName, keyword)
                    .or()
                    .like(keyword != null && !keyword.isEmpty(), EtoProject::getProjectNo, keyword)
                    .or()
                    .like(keyword != null && !keyword.isEmpty(), EtoProject::getSize, keyword)
                    .orderByDesc(EtoProject::getCreateTime)
                    .page(pageParam)
                    .getRecords();
            
            // 获取总记录数
            long total = pageParam.getTotal();
            
            // 转换为前端数据格式
            List<Map<String, Object>> data = new ArrayList<>();
            for (EtoProject project : projectList) {
                Map<String, Object> projectData = new HashMap<>();
                projectData.put("id", project.getId());
                projectData.put("projectNo", project.getProjectNo());
                projectData.put("projectName", project.getProjectName());
                projectData.put("tireGroup", project.getTireGroup());
                projectData.put("size", project.getSize());
                projectData.put("market", project.getMarket());
                projectData.put("brand", project.getBrand());
                projectData.put("status", project.getStatus());
                projectData.put("createTime", project.getCreateTime() != null ? 
                        new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(project.getCreateTime()) : "");
                
                // 查询关联的ETO方案数量
                long etoCount = etoMainService.lambdaQuery()
                        .eq(EtoMain::getProjectId, project.getId())
                        .count();
                projectData.put("etoCount", etoCount);
                
                data.add(projectData);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "");
            response.put("count", total);
            response.put("data", data);
            
            return response;
        } catch (Exception e) {
            logger.error("获取ETO项目列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "获取数据失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @DeleteMapping("/api/layui/eto-project/{id}")
    @ResponseBody
    public Map<String, Object> deleteEtoProject(@PathVariable Long id) {
        try {
            // 检查项目是否存在
            EtoProject project = etoProjectService.getById(id);
            if (project == null) {
                throw new RuntimeException("项目不存在");
            }
            
            // 检查是否有关联的ETO设计方案
            long etoCount = etoMainService.lambdaQuery()
                    .eq(EtoMain::getProjectId, id)
                    .count();
            
            if (etoCount > 0) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 2);
                errorResponse.put("msg", "该项目下有关联的ETO设计方案，无法删除");
                return errorResponse;
            }
            
            // 删除项目
            etoProjectService.removeById(id);
            
            logger.info("删除项目，ID: {}", id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "删除成功");
            return response;
        } catch (Exception e) {
            logger.error("删除项目失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "删除失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @GetMapping("/api/eto/{etoId}/schemes")
    @ResponseBody
    public Map<String, Object> getSchemeList(@PathVariable Integer etoId) {
        try {
            // 获取方案列表
            List<EtoScheme> schemeList = etoSchemeService.lambdaQuery()
                    .eq(EtoScheme::getEtoId, etoId)
                    .orderByAsc(EtoScheme::getSchemeIndex)
                    .list();
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "获取成功");
            response.put("data", schemeList);
            return response;
        } catch (Exception e) {
            logger.error("获取方案列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "获取失败：" + e.getMessage());
            return errorResponse;
        }
    }
    
    @PostMapping("/api/eto/{etoId}/scheme")
    @ResponseBody
    public Map<String, Object> createScheme(@PathVariable Integer etoId, @RequestBody Map<String, Object> formData, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 获取ETO主记录
            EtoMain etoMain = etoMainService.getById(etoId);
            if (etoMain == null) {
                throw new RuntimeException("ETO记录不存在");
            }
            
            // 获取方案数量，用于确定新方案的序号
            long schemeCount = etoSchemeService.lambdaQuery()
                    .eq(EtoScheme::getEtoId, etoId)
                    .count();
            
            // 创建新方案
            EtoScheme scheme = new EtoScheme();
            scheme.setSchemeName("方案 " + (schemeCount + 1));
            scheme.setSchemeIndex((int) (schemeCount + 1));
            scheme.setEtoId(etoId);
            scheme.setStatus("DRAFT");
            scheme.setCreateBy(username);
            scheme.setCreateTime(new Date());
            scheme.setUpdateTime(new Date());
            etoSchemeService.save(scheme);
            
            // 如果指定了复制源方案
            Integer sourceSchemeId = formData.get("sourceSchemeId") != null ? 
                    Integer.valueOf(String.valueOf(formData.get("sourceSchemeId"))) : null;
            
            if (sourceSchemeId != null) {
                // 复制胶料信息
                EtoRubberinfo sourceRubber = etoRubberinfoService.lambdaQuery()
                        .eq(EtoRubberinfo::getSchemeId, sourceSchemeId)
                        .one();
                
                if (sourceRubber != null) {
                    EtoRubberinfo newRubber = new EtoRubberinfo();
                    // 复制属性
                    newRubber.setSchemeId(scheme.getId());
                    newRubber.setInnerLinerType1(sourceRubber.getInnerLinerType1());
                    newRubber.setInnerLinerThickness1(sourceRubber.getInnerLinerThickness1());
                    newRubber.setInnerLinerWidth1(sourceRubber.getInnerLinerWidth1());
                    newRubber.setInnerLinerType2(sourceRubber.getInnerLinerType2());
                    newRubber.setInnerLinerThickness2(sourceRubber.getInnerLinerThickness2());
                    newRubber.setInnerLinerWidth2(sourceRubber.getInnerLinerWidth2());
                    newRubber.setSidewallCompound1(sourceRubber.getSidewallCompound1());
                    newRubber.setSidewallCompound2(sourceRubber.getSidewallCompound2());
                    newRubber.setBeadFillerCompound(sourceRubber.getBeadFillerCompound());
                    newRubber.setBeadFillerHT(sourceRubber.getBeadFillerHT());
                    newRubber.setTreadCompound(sourceRubber.getTreadCompound());
                    newRubber.setTreadWing(sourceRubber.getTreadWing());
                    newRubber.setTreadBaseType(sourceRubber.getTreadBaseType());
                    newRubber.setTreadBaseThickness(sourceRubber.getTreadBaseThickness());
                    newRubber.setTreadChimney(sourceRubber.getTreadChimney());
                    etoRubberinfoService.save(newRubber);
                }
                
                // 复制骨架材料信息
                // EtoSkeletonmaterial sourceSkeleton = etoSkeletonmaterialService.lambdaQuery() // Removed
                //         .eq(EtoSkeletonmaterial::getSchemeId, sourceSchemeId) // Removed
                //         .one(); // Removed
                
                // if (sourceSkeleton != null) { // Removed
                //     EtoSkeletonmaterial newSkeleton = new EtoSkeletonmaterial(); // Removed
                //     newSkeleton.setSchemeId(scheme.getId()); // Removed
                //     newSkeleton.setPlyMaterial(sourceSkeleton.getPlyMaterial()); // Removed
                //     newSkeleton.setPlyModel(sourceSkeleton.getPlyModel()); // Removed
                //     newSkeleton.setPlyCount(sourceSkeleton.getPlyCount()); // Removed
                //     newSkeleton.setPlyCompound(sourceSkeleton.getPlyCompound()); // Removed
                //     newSkeleton.setCarcassPlyWidth1(sourceSkeleton.getCarcassPlyWidth1()); // Removed
                //     newSkeleton.setCarcassPlyWidth2(sourceSkeleton.getCarcassPlyWidth2()); // Removed
                //     newSkeleton.setTurnupHeight(sourceSkeleton.getTurnupHeight()); // Removed
                //     newSkeleton.setSettingPosition(sourceSkeleton.getSettingPosition()); // Removed
                //     newSkeleton.setCarcassAngle(sourceSkeleton.getCarcassAngle()); // Removed
                //     newSkeleton.setCreateTime(new Date()); // Removed
                //     newSkeleton.setUpdateTime(new Date()); // Removed
                //     etoSkeletonmaterialService.save(newSkeleton); // Removed
                // } // Removed
                
                // 复制设计信息
                EtoDesigninfo sourceDesign = etoDesigninfoService.lambdaQuery()
                        .eq(EtoDesigninfo::getSchemeId, sourceSchemeId)
                        .one();
                
                if (sourceDesign != null) {
                    EtoDesigninfo newDesign = new EtoDesigninfo();
                    newDesign.setSchemeId(scheme.getId());
                    newDesign.setDrumWidth(sourceDesign.getDrumWidth());
                    newDesign.setBeltDrumCircumference(sourceDesign.getBeltDrumCircumference());
                    newDesign.setCircumference(sourceDesign.getCircumference());
                    newDesign.setBuildingType(sourceDesign.getBuildingType());
                    newDesign.setCuringTemperature(sourceDesign.getCuringTemperature());
                    newDesign.setPciChuckWidth(sourceDesign.getPciChuckWidth());
                    newDesign.setInnerBeadDistance(sourceDesign.getInnerBeadDistance());
                    newDesign.setCreateTime(new Date());
                    newDesign.setUpdateTime(new Date());
                    etoDesigninfoService.save(newDesign);
                }
                
                // 复制测试数据
                EtoTestdata sourceTest = etoTestdataService.lambdaQuery()
                        .eq(EtoTestdata::getSchemeId, sourceSchemeId)
                        .one();
                
                if (sourceTest != null) {
                    EtoTestdata newTest = new EtoTestdata();
                    newTest.setSchemeId(scheme.getId());
                    newTest.setMainGrooveBasePerimeter(sourceTest.getMainGrooveBasePerimeter());
                    newTest.setCalculatedBeadWidth(sourceTest.getCalculatedBeadWidth());
                    newTest.setCuredBeadWidth(sourceTest.getCuredBeadWidth());
                    newTest.setTreadColorLine(sourceTest.getTreadColorLine());
                    newTest.setCalculatedWeight(sourceTest.getCalculatedWeight());
                    newTest.setRiskIdentification(sourceTest.getRiskIdentification());
                    newTest.setConfirmation(sourceTest.getConfirmation());
                    newTest.setComment(sourceTest.getComment());
                    newTest.setCreateTime(new Date());
                    newTest.setUpdateTime(new Date());
                    etoTestdataService.save(newTest);
                }
                
                // 复制模具信息
                EtoMoldinfo sourceMold = etoMoldinfoService.lambdaQuery()
                        .eq(EtoMoldinfo::getSchemeId, sourceSchemeId)
                        .one();
                
                if (sourceMold != null) {
                    EtoMoldinfo newMold = new EtoMoldinfo();
                    newMold.setSchemeId(scheme.getId());
                    newMold.setTdw(sourceMold.getTdw());
                    newMold.setOd(sourceMold.getOd());
                    newMold.setSw(sourceMold.getSw());
                    newMold.setGd(sourceMold.getGd());
                    newMold.setSeaLandRatio(sourceMold.getSeaLandRatio());
                    newMold.setSeaLandRatioDiff(sourceMold.getSeaLandRatioDiff());
                    newMold.setBeadWidth(sourceMold.getBeadWidth());
                    newMold.setCreateTime(new Date());
                    newMold.setUpdateTime(new Date());
                    etoMoldinfoService.save(newMold);
                }
            } else {
                // 创建新的空记录
                // 胶料信息
                EtoRubberinfo rubberInfo = new EtoRubberinfo();
                rubberInfo.setSchemeId(scheme.getId());
                etoRubberinfoService.save(rubberInfo);
                
                // 骨架材料信息
                // EtoSkeletonmaterial skeletonMaterial = new EtoSkeletonmaterial(); // Removed
                // skeletonMaterial.setSchemeId(scheme.getId()); // Removed
                // etoSkeletonmaterialService.save(skeletonMaterial); // Removed
                
                // 设计信息
                EtoDesigninfo designInfo = new EtoDesigninfo();
                designInfo.setSchemeId(scheme.getId());
                designInfo.setCreateTime(new Date());
                designInfo.setUpdateTime(new Date());
                etoDesigninfoService.save(designInfo);
                
                // 测试数据
                EtoTestdata testData = new EtoTestdata();
                testData.setSchemeId(scheme.getId());
                testData.setCreateTime(new Date());
                testData.setUpdateTime(new Date());
                etoTestdataService.save(testData);
                
                // 模具信息
                EtoMoldinfo moldInfo = new EtoMoldinfo();
                moldInfo.setSchemeId(scheme.getId());
                moldInfo.setCreateTime(new Date());
                moldInfo.setUpdateTime(new Date());
                etoMoldinfoService.save(moldInfo);
            }
            
            // 返回响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "方案创建成功");
            Map<String, Object> data = new HashMap<>();
            data.put("schemeId", scheme.getId());
            data.put("schemeName", scheme.getSchemeName());
            response.put("data", data);
            
            return response;
        } catch (Exception e) {
            logger.error("创建方案失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "创建失败：" + e.getMessage());
            return errorResponse;
        }
    }
    
    @GetMapping("/api/eto/scheme/{schemeId}")
    @ResponseBody
    public Map<String, Object> getSchemeDetail(@PathVariable Integer schemeId) {
        try {
            // 获取方案信息
            EtoScheme scheme = etoSchemeService.getById(schemeId);
            if (scheme == null) {
                throw new RuntimeException("方案不存在");
            }
            
            // 获取各模块数据
            EtoRubberinfo rubberInfo = etoRubberinfoService.lambdaQuery()
                    .eq(EtoRubberinfo::getSchemeId, schemeId)
                    .one();
            
            // EtoSkeletonmaterial skeletonMaterial = etoSkeletonmaterialService.lambdaQuery() // Removed
            //         .eq(EtoSkeletonmaterial::getSchemeId, schemeId) // Removed
            //         .one(); // Removed
            
            EtoDesigninfo designInfo = etoDesigninfoService.lambdaQuery()
                    .eq(EtoDesigninfo::getSchemeId, schemeId)
                    .one();
            
            EtoTestdata testData = etoTestdataService.lambdaQuery()
                    .eq(EtoTestdata::getSchemeId, schemeId)
                    .one();
            
            EtoMoldinfo moldInfo = etoMoldinfoService.lambdaQuery()
                    .eq(EtoMoldinfo::getSchemeId, schemeId)
                    .one();
            
            // 组装返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("scheme", scheme);
            data.put("rubberInfo", rubberInfo != null ? rubberInfo : new HashMap<>());
            // data.put("skeletonMaterial", skeletonMaterial != null ? skeletonMaterial : new HashMap<>()); // Removed
            data.put("designInfo", designInfo != null ? designInfo : new HashMap<>());
            data.put("testData", testData != null ? testData : new HashMap<>());
            data.put("moldInfo", moldInfo != null ? moldInfo : new HashMap<>());
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "获取成功");
            response.put("data", data);
            
            return response;
        } catch (Exception e) {
            logger.error("获取方案详情失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "获取失败：" + e.getMessage());
            return errorResponse;
        }
    }
    
    @PutMapping("/api/layui/project/{id}")
    @ResponseBody
    public Map<String, Object> updateProject(@PathVariable Long id, @RequestBody Map<String, Object> formData) {
        try {
            // 检查项目是否存在
            EtoProject project = etoProjectService.getById(id);
            if (project == null) {
                throw new RuntimeException("项目不存在");
            }
            
            // 更新项目信息
            if (formData.containsKey("projectName")) project.setProjectName((String) formData.get("projectName"));
            if (formData.containsKey("tireGroup")) project.setTireGroup((String) formData.get("tireGroup"));
            if (formData.containsKey("size")) project.setSize((String) formData.get("size"));
            if (formData.containsKey("market")) project.setMarket((String) formData.get("market"));
            if (formData.containsKey("brand")) project.setBrand((String) formData.get("brand"));
            if (formData.containsKey("status")) project.setStatus((String) formData.get("status"));
            
            project.setUpdateTime(new Date());
            
            // 保存更新
            etoProjectService.updateById(project);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "更新成功");
            return response;
        } catch (Exception e) {
            logger.error("更新项目失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "更新失败：" + e.getMessage());
            return errorResponse;
        }
    }

    @GetMapping("/api/eto-project/{projectId}/designs")
    @ResponseBody
    public Map<String, Object> getProjectDesigns(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            // 检查项目是否存在
            EtoProject project = etoProjectService.getById(projectId);
            if (project == null) {
                throw new RuntimeException("项目不存在");
            }
            
            // 查询关联的ETO设计方案
            Page<EtoMain> pageParam = new Page<>(page, limit);
            List<EtoMain> etoList = etoMainService.lambdaQuery()
                    .eq(EtoMain::getProjectId, projectId)
                    .orderByDesc(EtoMain::getCreateTime)
                    .page(pageParam)
                    .getRecords();
            
            // 获取总记录数
            long total = pageParam.getTotal();
            
            // 转换为前端数据格式
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (EtoMain eto : etoList) {
                EtoBasicinfo basicInfo = etoBasicinfoService.lambdaQuery()
                        .eq(EtoBasicinfo::getEtoId, eto.getId())
                        .one();
                
                if (basicInfo != null) {
                    Map<String, Object> etoInfo = new HashMap<>();
                    etoInfo.put("id", eto.getId());
                    etoInfo.put("code", eto.getCode());
                    etoInfo.put("status", eto.getStatus());
                    etoInfo.put("createTime", eto.getCreateTime());
                    etoInfo.put("updateTime", eto.getUpdateTime());
                    etoInfo.put("sizeLiSi", basicInfo.getSizeLiSi());
                    etoInfo.put("pattern", basicInfo.getPattern());
                    etoInfo.put("productCode", basicInfo.getProductCode());
                    etoInfo.put("serialNumber", basicInfo.getSerialNumber());
                    etoInfo.put("market", basicInfo.getMarket());
                    etoInfo.put("subject", basicInfo.getSubject());
                    
                    // 获取方案数量
                    long schemeCount = etoSchemeService.lambdaQuery()
                            .eq(EtoScheme::getEtoId, eto.getId())
                            .count();
                    etoInfo.put("schemeCount", schemeCount);
                    
                    resultList.add(etoInfo);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 0);
            response.put("msg", "");
            response.put("count", total);
            response.put("data", resultList);
            
            return response;
        } catch (Exception e) {
            logger.error("获取项目关联的ETO列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 1);
            errorResponse.put("msg", "获取失败：" + e.getMessage());
            return errorResponse;
        }
    }
}
