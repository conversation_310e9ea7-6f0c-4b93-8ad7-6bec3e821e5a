package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoDesigninfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * ETO设计信息服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoDesigninfoService extends IService<EtoDesigninfo> {
    
    /**
     * 根据方案ID获取设计信息
     * @param schemeId 方案ID
     * @return 设计信息
     */
    EtoDesigninfo getBySchemeId(Integer schemeId);
}
