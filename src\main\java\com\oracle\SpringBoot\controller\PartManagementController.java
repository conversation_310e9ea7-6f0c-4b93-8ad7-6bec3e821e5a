package com.oracle.SpringBoot.controller;

import com.oracle.SpringBoot.common.R;
import com.oracle.SpringBoot.service.IPartManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 半部件管理控制器
 */
@RestController
@RequestMapping({"/api/parts", "/boot/api/parts"})
public class PartManagementController {
    
    private static final Logger logger = LoggerFactory.getLogger(PartManagementController.class);
    
    @Autowired
    private IPartManagementService partManagementService;
    
    /**
     * 获取指定类型的部件列表
     */
    @GetMapping("/{partType}")
    public R<List<Map<String, Object>>> getPartsByType(@PathVariable String partType) {
        try {
            List<Map<String, Object>> parts = partManagementService.getPartsByType(partType);
            return R.success(parts);
        } catch (Exception e) {
            logger.error("获取{}部件列表失败", partType, e);
            return R.error("获取部件列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取部件详细信息
     */
    @GetMapping("/{partType}/{partName}")
    public R<Map<String, Object>> getPartDetails(@PathVariable String partType, 
                                                 @PathVariable String partName) {
        try {
            Map<String, Object> partDetails = partManagementService.getPartDetails(partType, partName);
            if (partDetails != null) {
                return R.success(partDetails);
            } else {
                return R.error("未找到指定的部件");
            }
        } catch (Exception e) {
            logger.error("获取部件详情失败：{}/{}", partType, partName, e);
            return R.error("获取部件详情失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据SAP代码搜索部件
     */
    @GetMapping("/search")
    public R<List<Map<String, Object>>> searchPartsBySapCode(@RequestParam String sapCode) {
        try {
            List<Map<String, Object>> parts = partManagementService.searchPartsBySapCode(sapCode);
            return R.success(parts);
        } catch (Exception e) {
            logger.error("搜索部件失败：{}", sapCode, e);
            return R.error("搜索部件失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取所有部件类型的统计信息
     */
    @GetMapping("/statistics")
    public R<Map<String, Integer>> getPartStatistics() {
        try {
            Map<String, Integer> statistics = partManagementService.getPartStatistics();
            return R.success(statistics);
        } catch (Exception e) {
            logger.error("获取部件统计信息失败", e);
            return R.error("获取统计信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 创建或更新部件
     */
    @PostMapping("/{partType}")
    public R<Void> createOrUpdatePart(@PathVariable String partType,
                                     @RequestBody Map<String, Object> partData,
                                     HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 设置创建人/修改人信息
            partData.put("creator", username);
            partData.put("modifier", username);
            
            String partName = (String) partData.get("partName");
            boolean success;
            
            if (partName != null && partManagementService.getPartDetails(partType, partName) != null) {
                // 更新现有部件
                success = partManagementService.updatePart(partType, partName, partData);
            } else {
                // 创建新部件
                List<Map<String, Object>> singlePartList = new ArrayList<>();
                singlePartList.add(partData);
                success = partManagementService.batchImportParts(partType, singlePartList);
            }
            
            if (success) {
                return R.success();
            } else {
                return R.error("保存部件失败");
            }
        } catch (Exception e) {
            logger.error("保存{}部件失败", partType, e);
            return R.error("保存部件失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除部件
     */
    @DeleteMapping("/{partType}/{partName}")
    public R<Void> deletePart(@PathVariable String partType, 
                             @PathVariable String partName) {
        try {
            boolean success = partManagementService.deletePart(partType, partName);
            if (success) {
                return R.success();
            } else {
                return R.error("删除部件失败");
            }
        } catch (Exception e) {
            logger.error("删除部件失败：{}/{}", partType, partName, e);
            return R.error("删除部件失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量导入部件数据
     */
    @PostMapping("/{partType}/batch")
    public R<Void> batchImportParts(@PathVariable String partType,
                                   @RequestBody List<Map<String, Object>> partDataList,
                                   HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String username = (String) session.getAttribute("username");
            
            // 为每个部件设置创建人信息
            partDataList.forEach(partData -> {
                partData.put("creator", username);
                partData.put("modifier", username);
            });
            
            boolean success = partManagementService.batchImportParts(partType, partDataList);
            if (success) {
                return R.success();
            } else {
                return R.error("批量导入失败");
            }
        } catch (Exception e) {
            logger.error("批量导入{}部件失败", partType, e);
            return R.error("批量导入失败：" + e.getMessage());
        }
    }
}
