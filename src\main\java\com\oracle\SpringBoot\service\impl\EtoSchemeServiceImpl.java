package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoScheme;
import com.oracle.SpringBoot.mapper.EtoSchemeMapper;
import com.oracle.SpringBoot.service.IEtoSchemeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <p>
 * ETO方案服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoSchemeServiceImpl extends ServiceImpl<EtoSchemeMapper, EtoScheme> implements IEtoSchemeService {

    @Override
    public List<EtoScheme> listByEtoId(Integer etoId) {
        return this.lambdaQuery()
                .eq(EtoScheme::getEtoId, etoId)
                .orderByAsc(EtoScheme::getSchemeIndex)
                .list();
    }
}
