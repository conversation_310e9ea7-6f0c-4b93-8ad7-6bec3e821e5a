package com.oracle.SpringBoot.service;

import com.oracle.SpringBoot.entity.EtoScheme;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * ETO方案服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IEtoSchemeService extends IService<EtoScheme> {
    
    /**
     * 根据ETO主表ID获取方案列表
     * @param etoId ETO主表ID
     * @return 方案列表
     */
    List<EtoScheme> listByEtoId(Integer etoId);
}
