package com.oracle.SpringBoot.vo;

import com.oracle.SpringBoot.entity.EtoConceptParam;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 产品概念数据前端展示VO
 */
@Data
public class ConceptDataVO {
    
    /**
     * 参数列表
     */
    private List<EtoConceptParam> params;
    
    /**
     * 目标值，key是参数ID，value是目标值
     */
    private Map<Long, String> target;
    
    /**
     * 竞品列表
     */
    private List<CompetitorVO> competitors;
    
    @Data
    public static class CompetitorVO {
        /**
         * 竞品ID
         */
        private Long id;
        
        /**
         * 竞品名称
         */
        private String name;
        
        /**
         * 参数值，key是参数ID，value是参数值
         */
        private Map<Long, String> values;
    }
} 