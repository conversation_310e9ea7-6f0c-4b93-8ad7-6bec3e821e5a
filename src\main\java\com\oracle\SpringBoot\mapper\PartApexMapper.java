package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartApex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 三角胶部件Mapper接口
 */
@Mapper
public interface PartApexMapper extends BaseMapper<PartApex> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartApex WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartApex selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartApex WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartApex> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的三角胶部件
     */
    @Select("SELECT * FROM PartApex WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartApex> selectAllActive();
    
    /**
     * 根据硬芯胶料查询
     */
    @Select("SELECT * FROM PartApex WHERE HardCoreCompound = #{compound} AND FLAG = 1")
    List<PartApex> selectByHardCoreCompound(String compound);
    
    /**
     * 根据高度范围查询
     */
    @Select("SELECT * FROM PartApex WHERE Height BETWEEN #{minHeight} AND #{maxHeight} AND FLAG = 1")
    List<PartApex> selectByHeightRange(Double minHeight, Double maxHeight);
}
