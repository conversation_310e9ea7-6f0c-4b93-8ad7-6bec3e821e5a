package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartCapPly;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 冠带层部件Mapper接口
 */
@Mapper
public interface PartCapPlyMapper extends BaseMapper<PartCapPly> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartCapPly WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartCapPly selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartCapPly WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartCapPly> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的冠带层部件
     */
    @Select("SELECT * FROM PartCapPly WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartCapPly> selectAllActive();
    
    /**
     * 根据冠带层材料查询
     */
    @Select("SELECT * FROM PartCapPly WHERE CapPlyMaterial = #{material} AND FLAG = 1")
    List<PartCapPly> selectByCapPlyMaterial(String material);
    
    /**
     * 根据密度查询
     */
    @Select("SELECT * FROM PartCapPly WHERE Density = #{density} AND FLAG = 1")
    List<PartCapPly> selectByDensity(String density);
}
