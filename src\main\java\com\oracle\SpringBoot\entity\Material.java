package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Material")
public class Material implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "material_id", type = IdType.AUTO)
    private Integer materialId;

    private String materialCode;

    private String materialName;

    private String specification;

    private String unit;

    private BigDecimal unitPrice;

    private Integer quantity;

    private Integer minimumStock;

    private String supplier;

    private String supplierContact;

    private String storageLocation;

    private String category;

    private String status; // 正常、缺货、停用等

    private String remarks;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}