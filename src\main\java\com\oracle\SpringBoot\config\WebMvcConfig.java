package com.oracle.SpringBoot.config;

import com.oracle.SpringBoot.interceptor.LoginInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(@SuppressWarnings("null") InterceptorRegistry registry) {
        registry.addInterceptor(new LoginInterceptor())
                .addPathPatterns("/**") // 拦截所有路径
                .excludePathPatterns("/boot/loginETO") // 排除登录页面
                .excludePathPatterns("/loginInfo")// 排除登录请求
                .excludePathPatterns("/login")// 排除登录请求
                .excludePathPatterns("/images/**")
                .excludePathPatterns("/layui/**")
                .excludePathPatterns("/js/**")
                .excludePathPatterns("/images/**")
                .excludePathPatterns("/forgetPassword")
                .excludePathPatterns("/changePassword")
                .excludePathPatterns("/changeP")
                .excludePathPatterns("/loginETO");// 排除登录请求

    }
}