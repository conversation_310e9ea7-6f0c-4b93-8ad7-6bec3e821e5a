package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("Project")
public class Project implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "project_id", type = IdType.AUTO)
    private Integer projectId;

    private String projectName;

    private String projectCode;

    private String customerName;

    private String customerContact;

    private String projectDescription;

    private BigDecimal estimatedCost;

    private BigDecimal actualCost;

    private LocalDateTime startDate;

    private LocalDateTime expectedEndDate;

    private LocalDateTime actualEndDate;

    private String status; // 进行中、已完成、已暂停等

    private Integer managerId;

    private String location;

    private String remarks;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
