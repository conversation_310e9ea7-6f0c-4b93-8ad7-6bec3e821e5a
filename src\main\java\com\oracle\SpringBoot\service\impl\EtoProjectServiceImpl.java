package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoProject;
import com.oracle.SpringBoot.mapper.EtoProjectMapper;
import com.oracle.SpringBoot.service.IEtoProjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.oracle.SpringBoot.entity.EtoMain;
import com.oracle.SpringBoot.service.IEtoMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO项目服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoProjectServiceImpl extends ServiceImpl<EtoProjectMapper, EtoProject> implements IEtoProjectService {
    
    @Autowired
    private IEtoMainService etoMainService;
    
    @Override
    public EtoProject getProjectByEtoMainId(Long etoMainId) {
        EtoMain etoMain = etoMainService.getById(etoMainId);
        if (etoMain != null && etoMain.getProjectId() != null) {
            return this.getById(etoMain.getProjectId());
        }
        return null;
    }
} 