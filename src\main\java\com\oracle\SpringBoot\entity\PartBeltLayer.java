package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 带束层部件实体类
 */
@Data
@TableName("PartBeltLayer")
public class PartBeltLayer {
    
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 部件名称
     */
    @TableField("PART_NAME")
    private String partName;
    
    /**
     * SAP代码
     */
    @TableField("PART_SAPCODE")
    private String partSapcode;
    
    /**
     * 版本
     */
    @TableField("PART_VERSION")
    private String partVersion;
    
    /**
     * 部件描述
     */
    @TableField("PART_DESC")
    private String partDesc;
    
    /**
     * 带束层类型
     */
    @TableField("BeltLayerType")
    private Integer beltLayerType;
    
    /**
     * 边胶宽度
     */
    @TableField("EdgeRubberWidth")
    private BigDecimal edgeRubberWidth;
    
    /**
     * 钢丝材料
     */
    @TableField("SteelMaterial")
    private String steelMaterial;
    
    /**
     * 边胶厚度
     */
    @TableField("EdgeRubberThickness")
    private BigDecimal edgeRubberThickness;
    
    /**
     * 切割角度
     */
    @TableField("CutAngle")
    private BigDecimal cutAngle;
    
    /**
     * 橡胶胶料
     */
    @TableField("RubberCompound")
    private String rubberCompound;
    
    /**
     * 密度
     */
    @TableField("Density")
    private String density;
    
    /**
     * 钢丝厚度
     */
    @TableField("SteelThickness")
    private BigDecimal steelThickness;
    
    /**
     * 钢丝覆胶胶料
     */
    @TableField("SteelCoatingCompound")
    private String steelCoatingCompound;
    
    /**
     * 带束宽度
     */
    @TableField("BeltWidth")
    private BigDecimal beltWidth;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;
    
    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;
    
    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    private Date modifyTime;
    
    /**
     * 标志位
     */
    @TableField("FLAG")
    private Integer flag;
}
