<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ETO项目方案管理 - ETO线上系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/boot/layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        .scheme-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #EBEEF5;
        }

        .header h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
            display: flex;
            align-items: center;
        }

        .header h2 i {
            margin-right: 10px;
            color: #409EFF;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            color: #606266;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .back-link:hover {
            color: #409EFF;
        }

        .back-link i {
            margin-right: 4px;
        }

        .project-info {
            background-color: #F5F7FA;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .project-info .info-row {
            display: flex;
            flex-wrap: wrap;
        }

        .project-info .info-item {
            display: flex;
            width: 33%;
            margin-bottom: 12px;
            padding-right: 15px;
        }

        .project-info .info-label {
            font-weight: 600;
            width: 120px;
            color: #606266;
        }

        .project-info .info-value {
            flex: 1;
            color: #303133;
        }

        .scheme-list {
            margin-bottom: 24px;
        }

        .scheme-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .scheme-actions h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .layui-table {
            width: 100%;
            white-space: nowrap;
        }

        .layui-table th {
            background-color: #F5F7FA;
            font-weight: bold;
        }

        .layui-table td, .layui-table th {
            padding: 12px 15px;
            min-width: 80px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            color: #fff;
        }

        .status-pending {
            background-color: #E6A23C;
        }

        .status-approved {
            background-color: #67C23A;
        }

        .status-rejected {
            background-color: #F56C6C;
        }

        .status-draft {
            background-color: #909399;
        }

        .scheme-footer {
            text-align: center;
            margin-top: 24px;
        }

        .layui-btn {
            display: inline-flex;
            align-items: center;
        }

        .layui-btn i {
            margin-right: 5px;
        }

        .empty-data {
            text-align: center;
            padding: 40px 0;
            color: #909399;
        }

        .empty-data i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #DCDFE6;
        }

        .operation-btns .layui-btn {
            margin: 0 3px;
        }
        
        /* 导航按钮美化 */
        .nav-buttons {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .nav-btn {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            font-size: 13px;
            color: #606266;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 3px;
            margin-right: 8px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .nav-btn:hover {
            color: #409EFF;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
        
        .nav-btn i {
            margin-right: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="nav-buttons">
            <div class="back-link" id="backToProjectBtn">
            <i class="fas fa-arrow-left"></i> 返回项目详情
            </div>
            <button class="nav-btn" id="homeBtn">
                <i class="fas fa-home"></i> 返回首页
            </button>
        </div>
        
        <div class="scheme-container">
            <div class="header">
                <h2><i class="fas fa-sitemap"></i>项目方案管理</h2>
            </div>
            
            <div class="project-info" id="projectInfo">
                <!-- 项目信息将通过JS动态填充 -->
            </div>
            
            <div class="scheme-list">
                <div class="scheme-actions">
                    <h3><i class="fas fa-list"></i> 方案列表</h3>
                    <button class="layui-btn" id="createSchemeBtn">
                        <i class="fas fa-plus"></i> 创建新方案
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="layui-table" id="schemeTable">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>方案名称</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="schemeList">
                            <!-- 方案列表将通过JS动态填充 -->
                        </tbody>
                    </table>
                </div>
                
                <div id="emptyData" class="empty-data" style="display: none;">
                    <i class="fas fa-file-alt"></i>
                    <p>暂无方案数据</p>
                    <p>点击"创建新方案"按钮开始添加</p>
                </div>
            </div>
            
            <div class="scheme-footer">
                <button class="nav-btn" id="backBtn">
                    <i class="fas fa-arrow-left"></i> 返回项目详情
                </button>
            </div>
        </div>
    </div>
    
    <script src="/boot/layui/layui.js"></script>
    <script>
    layui.use(['layer', 'util'], function(){
        var layer = layui.layer;
        var util = layui.util;
        var $ = layui.jquery;
        
        // 获取URL中的项目ID
        var pathParts = window.location.pathname.split('/');
        var projectId = pathParts[pathParts.length - 1];
        
        // 加载项目详情和方案列表
        loadProjectDetail(projectId);
        loadSchemeList(projectId);
        
        // 创建新方案按钮事件
        $('#createSchemeBtn').on('click', function() {
            window.location.href = '/boot/eto-scheme/create?projectId=' + projectId;
        });
        
        // 绑定返回首页按钮
        $('#homeBtn').on('click', function() {
            window.location.href = '/boot/index';
        });
        
        // 绑定返回项目详情按钮
        $('#backToProjectBtn, #backBtn').on('click', function() {
            window.location.href = '/boot/eto-project/' + projectId;
        });
        
        // 加载项目详情函数
        function loadProjectDetail(id) {
            $.ajax({
                url: '/boot/api/eto-project/' + id,
                type: 'GET',
                success: function(res) {
                    if (res.code === 0) {
                        renderProjectInfo(res.data);
                    } else {
                        layer.msg(res.msg || '加载项目详情失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('系统错误，请重试', {icon: 2});
                }
            });
        }
        
        // 加载方案列表函数
        function loadSchemeList(projectId) {
            layer.load(2);
            $.ajax({
                url: '/boot/api/eto-project/' + projectId + '/schemes',
                type: 'GET',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.code === 0) {
                        renderSchemeList(res.data);
                    } else {
                        layer.msg(res.msg || '加载方案列表失败', {icon: 2});
                        $('#emptyData').show();
                        $('#schemeTable').hide();
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('系统错误，请重试', {icon: 2});
                    $('#emptyData').show();
                    $('#schemeTable').hide();
                }
            });
        }
        
        // 渲染项目信息函数
        function renderProjectInfo(project) {
            var infoHtml = '<div class="info-row">';
            infoHtml += infoItem('项目编号', project.projectNo || '-');
            infoHtml += infoItem('项目名称', project.projectName || '-');
            infoHtml += infoItem('尺寸', project.size || '-');
            infoHtml += '</div><div class="info-row">';
            infoHtml += infoItem('目的', project.purpose || '-');
            infoHtml += infoItem('轮胎组别', project.tireGroup || '-');
            infoHtml += infoItem('市场', project.market || '-');
            infoHtml += '</div>';
            
            $('#projectInfo').html(infoHtml);
        }
        
        // 渲染方案列表函数
        function renderSchemeList(schemes) {
            if (!schemes || schemes.length === 0) {
                $('#emptyData').show();
                $('#schemeTable').hide();
                return;
            }
            
            $('#emptyData').hide();
            $('#schemeTable').show();
            
            var html = '';
            $.each(schemes, function(index, scheme) {
                var statusBadge = getStatusBadge(scheme.status);
                var createTime = scheme.createTime ? util.toDateString(new Date(scheme.createTime), 'yyyy-MM-dd HH:mm:ss') : '-';
                var updateTime = scheme.updateTime ? util.toDateString(new Date(scheme.updateTime), 'yyyy-MM-dd HH:mm:ss') : '-';
                
                html += '<tr>';
                html += '<td>' + (index + 1) + '</td>';
                html += '<td>' + (scheme.schemeName || '-') + '</td>';
                html += '<td>' + (scheme.createBy || '-') + '</td>';
                html += '<td>' + createTime + '</td>';
                html += '<td>' + statusBadge + '</td>';
                html += '<td>' + updateTime + '</td>';
                html += '<td class="operation-btns">';
                html += '<button class="layui-btn layui-btn-xs" onclick="viewScheme(' + scheme.id + ')"><i class="fas fa-eye"></i>查看</button>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="editScheme(' + scheme.id + ')"><i class="fas fa-edit"></i>编辑</button>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteScheme(' + scheme.id + ')"><i class="fas fa-trash-alt"></i>删除</button>';
                html += '</td>';
                html += '</tr>';
            });
            
            $('#schemeList').html(html);
        }
        
        // 获取状态徽章HTML的辅助函数
        function getStatusBadge(status) {
            var badgeClass = '';
            var statusText = '';
            
            switch (status) {
                case 'pending':
                    badgeClass = 'status-pending';
                    statusText = '待审批';
                    break;
                case 'approved':
                    badgeClass = 'status-approved';
                    statusText = '已通过';
                    break;
                case 'rejected':
                    badgeClass = 'status-rejected';
                    statusText = '已拒绝';
                    break;
                case 'draft':
                    badgeClass = 'status-draft';
                    statusText = '草稿';
                    break;
                default:
                    statusText = status || '未知';
            }
            
            return badgeClass ? '<span class="status-badge ' + badgeClass + '">' + statusText + '</span>' : statusText;
        }
        
        // 生成信息项HTML的辅助函数
        function infoItem(label, value) {
            return '<div class="info-item">' +
                   '<div class="info-label">' + label + ':</div>' +
                   '<div class="info-value">' + value + '</div>' +
                   '</div>';
        }
    });
    
    // 查看方案详情
    function viewScheme(schemeId) {
        window.location.href = '/boot/eto-scheme/detail/' + schemeId;
    }
    
    // 编辑方案
    function editScheme(schemeId) {
        window.location.href = '/boot/eto-scheme/edit/' + schemeId;
    }
    
    // 删除方案
    function deleteScheme(schemeId) {
        layui.layer.confirm('确定要删除该方案吗？<br>删除后将无法恢复！', {
            icon: 3,
            title: '删除确认'
        }, function(index) {
            layui.layer.close(index);
            layui.layer.load(2);
            
            layui.$.ajax({
                url: '/boot/api/eto-scheme/' + schemeId,
                type: 'DELETE',
                success: function(res) {
                    layui.layer.closeAll('loading');
                    if (res.code === 0) {
                        layui.layer.msg('删除成功', {icon: 1});
                        // 刷新页面
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        layui.layer.msg(res.msg || '删除失败，请重试', {icon: 2});
                    }
                },
                error: function() {
                    layui.layer.closeAll('loading');
                    layui.layer.msg('系统错误，请重试', {icon: 2});
                }
            });
        });
    }
    </script>
</body>
</html> 