package com.oracle.SpringBoot.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

@RestController
@RequestMapping("/SpringBoot/log")
public class LogBackupController {

    @PostMapping("/backup-manually")
    public String backupLogsManually() {
        try {
            String sourceLogFile = "logs/spring-boot.log";
            String backupFolder = "backup-logs";
            File sourceFile = new File(sourceLogFile);
            File backupFile = new File(backupFolder + "/spring-boot-backup-" + System.currentTimeMillis() + ".log");
            File backupDir = new File(backupFolder);
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }
            copyFileUsingStream(sourceFile, backupFile);
            return "日志备份成功";
        } catch (IOException e) {
            return "日志备份失败：" + e.getMessage();
        }
    }

    private void copyFileUsingStream(File source, File dest) throws IOException {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer))!= -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
    }
}