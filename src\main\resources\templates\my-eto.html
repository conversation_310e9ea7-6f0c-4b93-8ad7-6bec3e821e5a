<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的表单 - TDBIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        /* 继承主题样式 */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
        }

        .main-content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            position: relative;
            padding-bottom: 60px;
            transition: all 0.3s ease;
        }

        /* ETO表单列表样式 */
        .eto-container {
            background: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .eto-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
        }

        .eto-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e1e2d;
        }

        .eto-actions {
            display: flex;
            gap: 12px;
        }

        .search-box {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .search-input {
            flex: 1;
            max-width: 300px;
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-approved {
            background: #d1fae5;
            color: #059669;
        }

        .status-rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        /* 操作按钮样式 */
        .table-actions {
            display: flex;
            gap: 8px;
        }

        .table-actions .layui-btn {
            padding: 0 12px;
            height: 32px;
            line-height: 32px;
            font-size: 13px;
        }

        /* 分页样式 */
        .layui-laypage {
            margin: 0;
            padding: 20px 0 0;
        }

        .layui-laypage a, .layui-laypage span {
            margin: 0 4px;
            border-radius: 4px;
        }

        /* 表格样式优化 */
        .layui-table {
            margin: 0;
        }

        .layui-table th {
            font-weight: 600;
            background: #f8fafc;
            color: #1e1e2d;
        }

        .layui-table td, .layui-table th {
            padding: 12px 16px;
        }

        /* 过滤器样式 */
        .filter-group {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            color: #4b5563;
            white-space: nowrap;
        }

        .filter-value {
            min-width: 120px;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="eto-container">
            <div class="eto-header">
                <div class="eto-title">我的表单</div>
                <div class="eto-actions">
                    <button class="layui-btn layui-btn-normal" onclick="window.location.href='create-eto'">
                        <i class="fas fa-plus"></i> 新建表单
                    </button>
                </div>
            </div>

            <!-- 搜索和过滤区 -->
            <div class="filter-group">
                <div class="filter-item">
                    <span class="filter-label">时间范围：</span>
                    <div class="filter-value">
                        <input type="text" class="layui-input" id="dateRange" placeholder="请选择时间范围">
                    </div>
                </div>
                <div class="filter-item">
                    <span class="filter-label">状态：</span>
                    <div class="filter-value">
                        <select lay-filter="status">
                            <option value="">全部</option>
                            <option value="draft">草稿</option>
                            <option value="pending">待审核</option>
                            <option value="approved">已通过</option>
                            <option value="rejected">已驳回</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="search-box">
                <div class="search-input">
                    <input type="text" class="layui-input" placeholder="搜索表单编号/名称">
                </div>
                <button class="layui-btn">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>

            <!-- 表单列表 -->
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th>表单编号</th>
                        <th>轮胎规格</th>
                        <th>关联项目</th>
                        <th>创建时间</th>
                        <th>最后修改</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ETO-20240301-001</td>
                        <td>T135/70R19 105M</td>
                        <td>PRJ123456 - 测试项目1</td>
                        <td>2024-03-01 10:30</td>
                        <td>2024-03-01 15:45</td>
                        <td>
                            <span class="status-tag status-pending">待审核</span>
                        </td>
                        <td>
                            <div class="table-actions">
                                <button class="layui-btn layui-btn-primary" onclick="viewDetails('ETO-20240301-001')">
                                    <i class="fas fa-eye"></i> 查看
                                </button>
                                <button class="layui-btn layui-btn-primary" onclick="editForm('ETO-20240301-001')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="layui-btn layui-btn-primary" onclick="deleteForm('ETO-20240301-001')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    <!-- 更多表单项... -->
                </tbody>
            </table>

            <!-- 分页 -->
            <div id="pagination"></div>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['laypage', 'layer', 'form', 'laydate'], function(){
        var laypage = layui.laypage;
        var layer = layui.layer;
        var form = layui.form;
        var laydate = layui.laydate;
        var $ = layui.$;
        
        // 数据加载条件
        var searchCondition = {
            page: 1,
            limit: 10,
            keyword: '',
            status: ''
        };

        // 初始化日期范围选择器
        laydate.render({
            elem: '#dateRange',
            range: true
        });
        
        // 加载ETO数据
        function loadData(page, limit, filters) {
            searchCondition.page = page || 1;
            searchCondition.limit = limit || 10;
            
            if (filters) {
                if (filters.status !== undefined) searchCondition.status = filters.status;
                if (filters.keyword !== undefined) searchCondition.keyword = filters.keyword;
            }
            
            $.ajax({
                url: '/boot/api/my-eto',
                type: 'GET',
                data: searchCondition,
                success: function(res) {
                    if (res.code === 0) {
                        renderTable(res.data);
                        // 更新分页
                        laypage.render({
                            elem: 'pagination',
                            count: res.count,
                            limit: searchCondition.limit,
                            limits: [10, 20, 30, 50],
                            curr: searchCondition.page,
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                            jump: function(obj, first) {
                                if (!first) {
                                    loadData(obj.curr, obj.limit);
                                }
                            }
                        });
                    } else {
                        layer.msg(res.msg || '加载数据失败');
                    }
                },
                error: function() {
                    layer.msg('服务器错误，请稍后重试');
                }
            });
        }
        
        // 渲染表格
        function renderTable(data) {
            var html = '';
            if (data && data.length > 0) {
                data.forEach(function(item) {
                    var statusClass = getStatusClass(item.status);
                    var statusText = getStatusText(item.status);
                    
                    // 项目信息
                    var projectInfo = item.projectName ? 
                        (item.projectNo + ' - ' + item.projectName) : 
                        '<span style="color:#999;">未关联项目</span>';
                    
                    html += '<tr>' +
                        '<td>' + item.code + '</td>' +
                        '<td>' + item.sizeLiSi + '</td>' +
                        '<td>' + projectInfo + '</td>' +
                        '<td>' + formatDate(item.createTime) + '</td>' +
                        '<td>' + formatDate(item.updateTime) + '</td>' +
                        '<td><span class="status-tag ' + statusClass + '">' + statusText + '</span></td>' +
                        '<td>' +
                        '<div class="table-actions">' +
                        '<button class="layui-btn layui-btn-primary" onclick="viewDetails(' + item.id + ')">' +
                        '<i class="fas fa-eye"></i> 查看</button>' +
                        '<button class="layui-btn layui-btn-primary" onclick="editForm(' + item.id + ')">' +
                        '<i class="fas fa-edit"></i> 编辑</button>' +
                        '<button class="layui-btn layui-btn-primary" onclick="deleteForm(' + item.id + ')">' +
                        '<i class="fas fa-trash"></i> 删除</button>' +
                        '</div>' +
                        '</td>' +
                        '</tr>';
                });
            } else {
                html = '<tr><td colspan="7" style="text-align:center;">暂无数据</td></tr>';
            }
            
            $('table tbody').html(html);
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case 'DRAFT': return 'status-draft';
                case 'SUBMITTED': return 'status-pending';
                case 'APPROVED': return 'status-approved';
                case 'REJECTED': return 'status-rejected';
                default: return '';
            }
        }
        
        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'DRAFT': return '草稿';
                case 'SUBMITTED': return '待审核';
                case 'APPROVED': return '已通过';
                case 'REJECTED': return '已驳回';
                default: return '未知';
            }
        }
        
        // 格式化日期
        function formatDate(timestamp) {
            if (!timestamp) return '-';
            var date = new Date(timestamp);
            return date.getFullYear() + '-' + 
                   padZero(date.getMonth() + 1) + '-' + 
                   padZero(date.getDate()) + ' ' + 
                   padZero(date.getHours()) + ':' + 
                   padZero(date.getMinutes());
        }
        
        // 补零
        function padZero(num) {
            return num < 10 ? '0' + num : num;
        }
        
        // 初始化表格
        loadData(1, 10);

        // 初始化分页
        laypage.render({
            elem: 'pagination',
            count: 0,
            limit: 10,
            limits: [10, 20, 30, 50],
            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
        });

        // 监听状态筛选
        form.on('select(status)', function(data){
            loadData(1, 10, {status: data.value});
        });
        
        // 监听搜索按钮点击
        $('.search-box button').on('click', function() {
            var keyword = $('.search-box input').val();
            loadData(1, 10, {keyword: keyword});
        });
        
        // 回车搜索
        $('.search-box input').on('keypress', function(e) {
            if(e.which === 13) {
                $('.search-box button').click();
            }
        });
    });

    // 在全局范围定义这些函数，以便onclick事件能够访问
    // 查看详情
    function viewDetails(id) {
        layui.layer.open({
            type: 2,
            title: 'ETO表单详情',
            area: ['90%', '90%'],
            content: 'scheme-design?etoId=' + id
        });
    }

    // 编辑表单
    function editForm(id) {
        window.location.href = 'scheme-design?etoId=' + id;
    }

    // 删除表单
    function deleteForm(id) {
        layui.layer.confirm('确认删除该ETO表单吗？', {icon: 3, title:'提示'}, function(index){
            $.ajax({
                url: '/boot/api/eto/' + id,
                type: 'DELETE',
                success: function(res) {
                    if(res.code === 0) {
                        layui.layer.msg('删除成功');
                        // 重新加载数据
                        window.location.reload();
                    } else {
                        layui.layer.msg(res.msg || '删除失败');
                    }
                },
                error: function() {
                    layui.layer.msg('服务器错误，请稍后重试');
                }
            });
            layui.layer.close(index);
        });
    }
    </script>
</body>
</html> 