package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ETO模具信息实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_Moldinfo")
public class EtoMoldinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("SchemeId")
    private Integer schemeId;
    
    @TableField("TDW")
    private String tdw;
    
    @TableField("OD")
    private String od;
    
    @TableField("SW")
    private String sw;
    
    @TableField("GD")
    private String gd;
    
    @TableField("SeaLandRatio")
    private String seaLandRatio;
    
    @TableField("SeaLandRatioDiff")
    private String seaLandRatioDiff;
    
    @TableField("BeadWidth")
    private String beadWidth;
    
    @TableField("CreateTime")
    private Date createTime;
    
    @TableField("UpdateTime")
    private Date updateTime;
}
