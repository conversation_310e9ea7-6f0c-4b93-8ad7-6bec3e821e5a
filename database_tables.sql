-- 1. ETO项目主表
CREATE TABLE eto_project (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    project_no NVARCHAR(50) NOT NULL,                  -- 项目编号
    project_name NVARCHAR(100) NOT NULL,               -- 项目名称
    purpose NVARCHAR(10) NULL,                         -- 目的(RE/OE)
    basis NVARCHAR(200) NULL,                          -- 基础依据
    tire_group NVARCHAR(20) NULL,                      -- 轮胎组别
    oe_maker NVARCHAR(100) NULL,                       -- OE制造商
    market NVARCHAR(50) NULL,                          -- 市场
    tire_segment NVARCHAR(30) NULL,                    -- 轮胎细分
    size NVARCHAR(50) NULL,                            -- 尺寸
    product_name NVARCHAR(100) NULL,                   -- 产品名称
    brand NVARCHAR(50) NULL,                           -- 品牌
    plant NVARCHAR(50) NULL,                           -- 工厂
    purpose_of_development NVARCHAR(MAX) NULL,         -- 开发目的
    applicant NVARCHAR(50) NULL,                       -- 申请人
    apply_time DATETIME NULL,                          -- 申请时间
    department NVARCHAR(50) NULL,                      -- 部门
    is_draft BIT DEFAULT 0,                            -- 是否草稿
    status NVARCHAR(20) DEFAULT 'pending',             -- 状态(pending/approved/rejected)
    create_time DATETIME DEFAULT GETDATE(),            -- 创建时间
    update_time DATETIME DEFAULT GETDATE(),            -- 更新时间
    create_by NVARCHAR(50) NULL,                       -- 创建人
    update_by NVARCHAR(50) NULL                        -- 更新人
);

-- 2. 产品概念与策略表
CREATE TABLE eto_product_concept (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    project_id BIGINT NOT NULL,                        -- 关联项目ID
    param_name NVARCHAR(50) NOT NULL,                  -- 参数名称
    target_value NVARCHAR(100) NULL,                   -- 目标值
    competitor_value NVARCHAR(100) NULL,               -- 竞争对手值
    competitor_name NVARCHAR(100) NULL,                -- 竞争对手名称
    param_order INT NULL,                              -- 参数顺序
    FOREIGN KEY (project_id) REFERENCES eto_project(id) ON DELETE CASCADE
);

-- 3. ETO项目方案表
CREATE TABLE eto_project_scheme (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    project_id BIGINT NOT NULL,                        -- 关联项目ID
    scheme_name NVARCHAR(50) NOT NULL,                 -- 方案名称
    scheme_description NVARCHAR(MAX) NULL,             -- 方案描述
    is_reference BIT DEFAULT 0,                        -- 是否为参考方案
    status NVARCHAR(20) DEFAULT 'draft',               -- 状态
    create_time DATETIME DEFAULT GETDATE(),            -- 创建时间
    update_time DATETIME DEFAULT GETDATE(),            -- 更新时间
    create_by NVARCHAR(50) NULL,                       -- 创建人
    FOREIGN KEY (project_id) REFERENCES eto_project(id) ON DELETE CASCADE
);

-- 4. 方案参数表
CREATE TABLE eto_scheme_param (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    scheme_id BIGINT NOT NULL,                         -- 关联方案ID
    param_group NVARCHAR(50) NOT NULL,                 -- 参数分组(模具/基本/胶料/骨架/设计/检测)
    param_name NVARCHAR(50) NOT NULL,                  -- 参数名称
    param_value NVARCHAR(500) NULL,                    -- 参数值
    param_unit NVARCHAR(20) NULL,                      -- 参数单位
    FOREIGN KEY (scheme_id) REFERENCES eto_project_scheme(id) ON DELETE CASCADE
);

-- 5. ETO审核记录表
CREATE TABLE eto_approval (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    project_id BIGINT NOT NULL,                        -- 关联项目ID
    approver NVARCHAR(50) NOT NULL,                    -- 审批人
    approval_time DATETIME DEFAULT GETDATE(),          -- 审批时间
    status NVARCHAR(20) NOT NULL,                      -- 状态(approved/rejected)
    comments NVARCHAR(MAX) NULL,                       -- 审批意见
    FOREIGN KEY (project_id) REFERENCES eto_project(id) ON DELETE CASCADE
);

-- 6. ETO草稿表 - 存储草稿版本
CREATE TABLE eto_draft (
    id BIGINT PRIMARY KEY IDENTITY(1,1),
    project_id BIGINT NULL,                            -- 关联项目ID（可能为空，表示新项目的草稿）
    draft_data NVARCHAR(MAX) NOT NULL,                 -- JSON格式的草稿数据
    create_time DATETIME DEFAULT GETDATE(),            -- 创建时间
    update_time DATETIME DEFAULT GETDATE(),            -- 更新时间
    create_by NVARCHAR(50) NULL                        -- 创建人
); 