<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - ETO线上系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "Microsoft YaHei", sans-serif;
            background: #f5f7fa;
            color: #1e1e2d;
        }

        .change-password-container {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1); 
            width: 90%;
            max-width: 420px;
            padding: 40px;
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .form-header h2 {
            font-size: 24px;
            color: #1e1e2d;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .form-header p {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group .layui-input {
            height: 44px;
            line-height: 44px;
            padding: 0 40px 0 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.3s;
        }

        .form-group .layui-input:focus {
            border-color: #3B82F6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .form-group .layui-input:hover {
            border-color: #3B82F6;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            cursor: pointer;
            font-size: 16px;
            transition: color 0.3s;
        }

        .password-toggle:hover {
            color: #3B82F6;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .strength-meter {
            height: 4px;
            flex: 1;
            background: #e2e8f0;
            border-radius: 2px;
            overflow: hidden;
        }

        .strength-meter div {
            height: 100%;
            width: 0;
            transition: all 0.3s;
        }

        .weak { background: #dc2626; width: 33.33%; }
        .medium { background: #f59e0b; width: 66.66%; }
        .strong { background: #10b981; width: 100%; }

        .strength-text {
            color: #666;
            min-width: 60px;
        }

        .submit-btn {
            width: 100%;
            height: 44px;
            line-height: 44px;
            background: #3B82F6;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .submit-btn:hover {
            background: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .form-footer {
            text-align: center;
            margin-top: 24px;
            font-size: 14px;
            color: #666;
        }

        .form-footer a {
            color: #3B82F6;
            text-decoration: none;
            transition: color 0.3s;
        }

        .form-footer a:hover {
            color: #2563eb;
        }

        .password-rules {
            margin-top: 8px;
            font-size: 12px;
            color: #666;
            padding-left: 16px;
        }

        .password-rules li {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .password-rules i {
            font-size: 12px;
        }

        .rule-valid { color: #10b981; }
        .rule-invalid { color: #666; }
    </style>
</head>
<body>
    <div class="change-password-container">
        <div class="form-header">
            <h2>修改密码</h2>
            <p>请设置一个安全的新密码，建议使用字母、数字和符号的组合</p>
        </div>
        <form class="layui-form" action="changepassword" method="post">
            <div class="form-group">
                <input type="password" name="oldPassword" required lay-verify="required" 
                       placeholder="请输入原密码" autocomplete="current-password" class="layui-input">
                <i class="fas fa-eye-slash password-toggle"></i>
            </div>
            
            <div class="form-group">
                <input type="password" name="newPassword" required lay-verify="required|password" 
                       placeholder="请输入新密码" autocomplete="new-password" class="layui-input">
                <i class="fas fa-eye-slash password-toggle"></i>
                <div class="password-strength">
                    <div class="strength-meter">
                        <div></div>
                    </div>
                    <span class="strength-text">强度：弱</span>
                </div>
                <ul class="password-rules">
                    <li><i class="fas fa-circle"></i> 至少8个字符</li>
                    <li><i class="fas fa-circle"></i> 包含大小写字母</li>
                    <li><i class="fas fa-circle"></i> 包含数字</li>
                    <li><i class="fas fa-circle"></i> 包含特殊字符</li>
                </ul>
            </div>
            
            <div class="form-group">
                <input type="password" name="confirmPassword" required lay-verify="required|confirmPassword" 
                       placeholder="请确认新密码" autocomplete="new-password" class="layui-input">
                <i class="fas fa-eye-slash password-toggle"></i>
            </div>
            
            <button class="layui-btn submit-btn" lay-submit lay-filter="changePassword">
                确认修改
            </button>
        </form>
        
        <div class="form-footer">
            <a href="index">返回首页</a>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.$;

        // 密码显示切换
        $('.password-toggle').on('click', function(){
            var input = $(this).prev('input');
            if(input.attr('type') === 'password'){
                input.attr('type', 'text');
                $(this).removeClass('fa-eye-slash').addClass('fa-eye');
            } else {
                input.attr('type', 'password');
                $(this).removeClass('fa-eye').addClass('fa-eye-slash');
            }
        });

        // 密码强度检测
        $('input[name="newPassword"]').on('input', function(){
            var password = $(this).val();
            var strength = checkPasswordStrength(password);
            var meter = $(this).siblings('.password-strength').find('.strength-meter div');
            var text = $(this).siblings('.password-strength').find('.strength-text');
            
            meter.removeClass('weak medium strong');
            if(strength > 0) {
                meter.addClass(strength === 1 ? 'weak' : strength === 2 ? 'medium' : 'strong');
                text.text('强度：' + (strength === 1 ? '弱' : strength === 2 ? '中' : '强'));
            } else {
                text.text('强度：弱');
            }

            // 更新密码规则指示器
            updatePasswordRules(password);
        });

        // 自定义验证规则
        form.verify({
            password: function(value){
                if(value.length < 8){
                    return '密码长度不能小于8个字符';
                }
                if(!/(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])/.test(value)){
                    return '密码必须包含大小写字母、数字和特殊字符';
                }
            },
            confirmPassword: function(value){
                var password = $('input[name="newPassword"]').val();
                if(value !== password){
                    return '两次输入的密码不一致';
                }
            }
        });

        // 表单提交
        form.on('submit(changePassword)', function(data){
            // 这里可以添加额外的表单验证逻辑
            return true;
        });

        // 密码强度检测函数
        function checkPasswordStrength(password) {
            var strength = 0;
            if(password.length >= 8) strength++;
            if(/(?=.*[A-Z])(?=.*[a-z])/.test(password)) strength++;
            if(/(?=.*[0-9])(?=.*[^A-Za-z0-9])/.test(password)) strength++;
            return strength;
        }

        // 更新密码规则指示器
        function updatePasswordRules(password) {
            var rules = {
                length: password.length >= 8,
                letters: /(?=.*[A-Z])(?=.*[a-z])/.test(password),
                numbers: /[0-9]/.test(password),
                special: /[^A-Za-z0-9]/.test(password)
            };

            $('.password-rules li').each(function(index){
                var icon = $(this).find('i');
                var isValid = index === 0 ? rules.length :
                             index === 1 ? rules.letters :
                             index === 2 ? rules.numbers :
                             rules.special;
                
                icon.removeClass('fa-circle fa-check-circle rule-valid rule-invalid')
                    .addClass(isValid ? 'fa-check-circle rule-valid' : 'fa-circle rule-invalid');
            });
        }
    });
    </script>
</body>
</html>