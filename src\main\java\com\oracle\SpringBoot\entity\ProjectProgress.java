package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("project_progress")
public class ProjectProgress implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "progress_id", type = IdType.AUTO)
    private Integer progressId;

    private Integer projectId;

    private String stage;  // 当前阶段：设计中、待审核、已审核、已完成等

    private String status; // 状态：正常、延期、暂停等

    private Integer currentVersion;  // 当前版本号

    private String designerId;  // 设计人员ID

    private String reviewerId;  // 审核人员ID

    private String reviewComments;  // 审核意见

    private LocalDateTime designStartTime;  // 设计开始时间

    private LocalDateTime designEndTime;    // 设计完成时间

    private LocalDateTime reviewStartTime;  // 审核开始时间

    private LocalDateTime reviewEndTime;    // 审核完成时间

    private String designChanges;  // 设计变更说明

    private Integer completionPercentage;  // 完成百分比

    private String nextStep;  // 下一步操作

    private String blockingIssues;  // 阻塞问题

    private String remarks;  // 备注

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;
}
