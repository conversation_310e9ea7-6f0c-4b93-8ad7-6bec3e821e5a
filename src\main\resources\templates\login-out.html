<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出 - TDBIP</title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: url('./images/login1.jpg') center/cover no-repeat;
            font-family: "Microsoft YaHei", sans-serif;
            position: relative;
        }

        .logout-container {
            width: 400px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .logout-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logout-header img {
            height: 60px;
            margin-bottom: 15px;
        }

        .logout-header h2 {
            color: #333;
            font-size: 24px;
            margin: 0;
            font-weight: normal;
        }

        .logout-header p {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        .logout-content {
            padding: 20px 0;
        }

        .logout-message {
            font-size: 18px;
            color: #333;
            margin-bottom: 25px;
        }

        .return-button {
            display: inline-block;
            padding: 12px 30px;
            background-color: #3B82F6;
            color: #fff;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .return-button:hover {
            background-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .success-icon {
            font-size: 48px;
            color: #10B981;
            margin-bottom: 20px;
        }

        .copyright {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            color: #666;
            font-size: 12px;
            padding: 0 20px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="logout-container animate-fadeInUp">
        <div class="logout-header">
            <img src="./images/26.png" alt="Logo">
            <h2>TDBIP</h2>
            <p style="color: #666; font-size: 14px; margin-top: 5px;">TireDesign BuildInno Platform</p>
        </div>
        <div class="logout-content">
            <i class="fas fa-check-circle success-icon"></i>
            <div class="logout-message">您已成功退出系统</div>
            <a href="loginETO" class="return-button">
                <i class="fas fa-sign-in-alt"></i>
                返回登录
            </a>
        </div>
    </div>
    <div class="copyright">
        版权所有 &copy; 山东玲珑轮胎股份有限公司，保留所有权利&nbsp;&nbsp;All Rights Reserved, Version 1.0.0
    </div>
</body>
</html>