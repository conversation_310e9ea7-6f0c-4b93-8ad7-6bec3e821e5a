package com.oracle.SpringBoot.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ETO设计信息实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ETO_Designinfo")
public class EtoDesigninfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("SchemeId")
    private Integer schemeId;
    
    @TableField("DrumWidth")
    private String drumWidth;
    
    @TableField("BeltDrumCircumference")
    private String beltDrumCircumference;
    
    @TableField("Circumference")
    private String circumference;
    
    @TableField("BuildingType")
    private String buildingType;
    
    @TableField("CuringTemperature")
    private String curingTemperature;
    
    @TableField("PciChuckWidth")
    private String pciChuckWidth;
    
    @TableField("InnerBeadDistance")
    private String innerBeadDistance;
    
    @TableField("CreateTime")
    private Date createTime;
    
    @TableField("UpdateTime")
    private Date updateTime;
}
