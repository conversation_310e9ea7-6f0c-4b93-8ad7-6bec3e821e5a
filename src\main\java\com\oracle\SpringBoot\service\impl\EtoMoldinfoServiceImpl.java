package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoMoldinfo;
import com.oracle.SpringBoot.mapper.EtoMoldinfoMapper;
import com.oracle.SpringBoot.service.IEtoMoldinfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO模具信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoMoldinfoServiceImpl extends ServiceImpl<EtoMoldinfoMapper, EtoMoldinfo> implements IEtoMoldinfoService {

    @Override
    public EtoMoldinfo getBySchemeId(Integer schemeId) {
        return this.lambdaQuery()
                .eq(EtoMoldinfo::getSchemeId, schemeId)
                .one();
    }
}
