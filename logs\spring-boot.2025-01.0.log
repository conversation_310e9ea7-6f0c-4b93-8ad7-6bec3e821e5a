2025-01-12 16:46:51 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 用户日志操作页面 页面
2025-01-12 16:46:53 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 在 2025-01-12T16:46:53.478 退出系统
2025-01-12 16:47:27 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-12 16:47:27 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:27 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:47:27 [Thread-6] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-12 16:47:27 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:27 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:27 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-12 16:47:27 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-12 16:47:27 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 27560 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:47:27 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:47:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:47:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:47:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:47:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:47:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:47:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:47:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:47:28 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:28 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:47:28 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:47:28 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 313 ms
2025-01-12 16:47:28 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:47:28 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:47:28 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:47:28 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.753 seconds (JVM running for 874.937)
2025-01-12 16:47:28 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:47:29 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 1 deletion, 0 modifications)
2025-01-12 16:47:30 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:30 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:47:30 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:30 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:30 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-01-12 16:47:30 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-01-12 16:47:30 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 27560 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:47:30 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:47:30 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:47:30 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:47:30 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:47:30 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:47:30 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:47:30 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:47:30 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:47:30 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:30 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:47:30 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:47:30 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 303 ms
2025-01-12 16:47:30 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:47:30 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:47:30 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:47:30 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.769 seconds (JVM running for 877.344)
2025-01-12 16:47:30 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:47:32 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-01-12 16:47:32 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:32 [Thread-12] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:47:32 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:32 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:32 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-01-12 16:47:32 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-01-12 16:47:32 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 27560 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:47:32 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:47:32 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:47:32 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:47:32 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-12 16:47:32 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:47:32 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:47:32 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:47:32 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:47:32 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:47:32 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:32 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:47:32 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:47:32 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:47:32 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 281 ms
2025-01-12 16:47:32 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:47:33 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-01-12 16:47:33 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:47:33 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:47:33 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:47:33 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:47:33 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.626 seconds (JVM running for 879.62)
2025-01-12 16:47:33 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:47:33 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-12 16:47:33 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-12 16:47:33 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-12 16:48:03 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-01-12 16:48:03 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-01-12 16:48:51 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@1a804c56
2025-01-12 16:48:52 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:48:52 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:48:52 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-12 16:48:52 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-12 16:48:52 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:48:52 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:48:52 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-01-12 16:48:53 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:48:53 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:48:53 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:48:53 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:48:53 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:48:53 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:48:53 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:48:53 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:48:53 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:48:53 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1435 ms
2025-01-12 16:48:53 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:48:54 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-12 16:48:54 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:48:55 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:48:55 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:48:55 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:48:55 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 3.742 seconds (JVM running for 4.306)
2025-01-12 16:49:08 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-12 16:49:08 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-12 16:49:08 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-12 16:49:18 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-12 16:49:18 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:18 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:49:18 [Thread-6] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-12 16:49:18 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:18 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:18 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-12 16:49:18 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-12 16:49:18 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:49:18 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:49:19 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:49:19 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:49:19 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:49:19 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:49:19 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:49:19 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:49:19 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:49:19 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:19 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:49:19 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:49:19 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 334 ms
2025-01-12 16:49:19 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:49:19 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:49:19 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:49:19 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.794 seconds (JVM running for 28.254)
2025-01-12 16:49:19 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:49:21 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-12 16:49:21 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:21 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:49:21 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:21 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:21 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-01-12 16:49:21 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-01-12 16:49:21 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:49:21 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:49:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:49:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:49:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:49:22 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:49:22 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:49:22 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:49:22 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:49:22 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:22 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:49:22 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:49:22 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 341 ms
2025-01-12 16:49:22 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:49:22 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:49:22 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:49:22 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.708 seconds (JVM running for 31.061)
2025-01-12 16:49:22 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:49:22 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-12 16:49:22 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-12 16:49:22 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-12 16:52:20 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 9 class path changes (0 additions, 4 deletions, 5 modifications)
2025-01-12 16:52:20 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:20 [Thread-12] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:52:20 [Thread-12] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-12 16:52:20 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:20 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:20 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-01-12 16:52:20 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-01-12 16:52:20 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:52:20 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:52:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:52:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:52:20 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-12 16:52:20 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:52:20 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:52:20 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:52:20 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:52:20 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:52:20 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:20 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:52:20 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:52:20 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:52:20 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 317 ms
2025-01-12 16:52:20 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:52:20 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-01-12 16:52:21 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:52:21 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:52:21 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:21 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:52:21 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.712 seconds (JVM running for 209.681)
2025-01-12 16:52:21 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:52:22 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 4 class path changes (4 additions, 0 deletions, 0 modifications)
2025-01-12 16:52:22 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:22 [Thread-16] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:52:22 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:22 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:22 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-01-12 16:52:22 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-01-12 16:52:22 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:52:22 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:52:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:52:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:52:22 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:52:23 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:52:23 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:52:23 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:52:23 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:52:23 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:23 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:52:23 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:52:23 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 282 ms
2025-01-12 16:52:23 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:52:23 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:52:23 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:52:23 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.607 seconds (JVM running for 211.96)
2025-01-12 16:52:23 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:52:24 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-12 16:52:24 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-12 16:52:24 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-12 16:52:41 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 xx 更改密码
2025-01-12 16:52:43 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 xx 忘记密码
2025-01-12 16:53:04 [http-nio-8080-exec-10] INFO  c.o.S.controller.LoginController - 用户 linqiang_zeng 在 2025-01-12T16:53:04.836 登录系统
2025-01-12 16:53:04 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-12 16:53:08 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-12 16:53:09 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-12 16:53:10 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 x页面 页面
2025-01-12 16:53:12 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 在 2025-01-12T16:53:12.718 退出系统
2025-01-12 16:57:27 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-12 16:57:27 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:27 [Thread-20] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:57:27 [Thread-20] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-12 16:57:27 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:27 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:27 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-01-12 16:57:27 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-01-12 16:57:28 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:57:28 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:57:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:57:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:57:28 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:57:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:57:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:57:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:57:28 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:57:28 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:28 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:57:28 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:57:28 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 251 ms
2025-01-12 16:57:28 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:57:28 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:57:28 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:57:28 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.564 seconds (JVM running for 517.253)
2025-01-12 16:57:28 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-12 16:57:53 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 83 class path changes (0 additions, 83 deletions, 0 modifications)
2025-01-12 16:57:53 [Thread-24] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:53 [Thread-24] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:57:53 [Thread-24] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:53 [Thread-24] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-12 16:57:53 [Thread-24] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closing ...
2025-01-12 16:57:53 [Thread-24] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closed
2025-01-12 16:58:01 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:58:01 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:58:02 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:58:02 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:58:02 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-01-12 16:58:02 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.oracle.SpringBoot.mapper]' package. Please check your configuration.
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:58:02 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:58:02 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:58:02 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:58:02 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:58:02 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:58:02 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:58:02 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 279 ms
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:58:02 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:58:02 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} inited
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:58:02 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.62 seconds (JVM running for 551.134)
2025-01-12 16:58:02 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

   JdbcTemplateConfiguration matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

    None


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-01-12 16:58:04 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 27 class path changes (27 additions, 0 deletions, 0 modifications)
2025-01-12 16:58:04 [Thread-28] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-12 16:58:04 [Thread-28] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} closing ...
2025-01-12 16:58:04 [Thread-28] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} closed
2025-01-12 16:58:04 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 30280 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-12 16:58:04 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-12 16:58:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-12 16:58:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-12 16:58:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-12 16:58:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-12 16:58:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-12 16:58:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-12 16:58:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-12 16:58:04 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-12 16:58:04 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-12 16:58:04 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 233 ms
2025-01-12 16:58:04 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-12 16:58:04 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} inited
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-12 16:58:04 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.61 seconds (JVM running for 553.436)
2025-01-12 16:58:04 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation delta:


==========================
CONDITION EVALUATION DELTA
==========================


Positive matches:
-----------------

    None


Negative matches:
-----------------

   JdbcTemplateConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) found beans of type 'org.springframework.jdbc.core.JdbcOperations' jdbcTemplate (OnBeanCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



2025-01-12 16:58:38 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} closing ...
2025-01-12 16:58:38 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} closed
2025-01-14 16:03:59 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@73bbde8e
2025-01-14 16:03:59 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 720 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-14 16:03:59 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-14 16:03:59 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-14 16:03:59 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-14 16:04:00 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-14 16:04:00 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-14 16:04:00 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-01-14 16:04:01 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-14 16:04:01 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-14 16:04:01 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-14 16:04:01 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-14 16:04:01 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-14 16:04:01 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-14 16:04:01 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-14 16:04:01 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-14 16:04:01 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-14 16:04:01 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1677 ms
2025-01-14 16:04:01 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-14 16:04:01 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-14 16:04:02 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-14 16:04:03 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-14 16:04:03 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-14 16:04:03 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-14 16:04:03 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 4.151 seconds (JVM running for 4.971)
2025-01-14 16:04:13 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-14 16:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-14 16:04:13 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-15 16:58:07 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-15 16:58:07 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:07 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-15 16:58:07 [Thread-6] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-15 16:58:07 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:07 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:07 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-15 16:58:07 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-15 16:58:07 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 720 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-15 16:58:07 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-15 16:58:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-15 16:58:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-15 16:58:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-15 16:58:08 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-15 16:58:08 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-15 16:58:08 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-15 16:58:08 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-15 16:58:08 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:08 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-15 16:58:08 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-15 16:58:08 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 469 ms
2025-01-15 16:58:08 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-15 16:58:08 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-15 16:58:08 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-15 16:58:08 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.964 seconds (JVM running for 89655.121)
2025-01-15 16:58:08 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-15 16:58:35 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-15 16:58:35 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:35 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-15 16:58:35 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:35 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:35 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-01-15 16:58:35 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-01-15 16:58:35 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 720 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-15 16:58:35 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-15 16:58:35 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-15 16:58:35 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-15 16:58:35 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-15 16:58:35 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-15 16:58:35 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-15 16:58:35 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-15 16:58:35 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-15 16:58:35 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:35 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-15 16:58:35 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-15 16:58:35 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 298 ms
2025-01-15 16:58:35 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-15 16:58:35 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-15 16:58:35 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-15 16:58:35 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.717 seconds (JVM running for 89682.226)
2025-01-15 16:58:35 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-15 17:02:41 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-01-15 17:02:41 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-01-15 17:02:45 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@73bbde8e
2025-01-15 17:02:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-15 17:02:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-15 17:02:46 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-15 17:02:46 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-15 17:02:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-15 17:02:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-15 17:02:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-01-15 17:02:47 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-15 17:02:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-15 17:02:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-15 17:02:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-15 17:02:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-15 17:02:47 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-15 17:02:47 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-15 17:02:47 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-15 17:02:47 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-15 17:02:47 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1500 ms
2025-01-15 17:02:47 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-15 17:02:48 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-15 17:02:48 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-15 17:02:49 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-15 17:02:49 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-15 17:02:49 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-15 17:02:49 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 3.755 seconds (JVM running for 4.322)
2025-01-16 09:41:42 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-16 09:41:42 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 09:41:42 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 09:41:42 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 09:41:42 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 09:41:42 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-16 09:41:42 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-16 09:41:42 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 09:41:42 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 09:41:42 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 09:41:42 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 09:41:42 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-16 09:41:42 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 09:41:42 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 09:41:42 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 09:41:42 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 09:41:42 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 09:41:42 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 09:41:42 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 09:41:42 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 09:41:42 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 09:41:42 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 393 ms
2025-01-16 09:41:42 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 09:41:42 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-01-16 09:41:43 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 09:41:43 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 09:41:43 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 09:41:43 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 09:41:43 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.858 seconds (JVM running for 59942.632)
2025-01-16 09:41:43 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 10:09:03 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-16 10:09:03 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 10:09:03 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 10:09:03 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 10:09:03 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 10:09:03 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-01-16 10:09:03 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-01-16 10:09:03 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 10:09:03 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 10:09:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 10:09:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 10:09:04 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 10:09:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 10:09:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 10:09:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 10:09:04 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 10:09:04 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 10:09:04 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 10:09:04 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 10:09:04 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 298 ms
2025-01-16 10:09:04 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 10:09:04 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 10:09:04 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 10:09:04 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.694 seconds (JVM running for 61583.631)
2025-01-16 10:09:04 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 11:30:46 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-01-16 11:30:46 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:30:46 [Thread-12] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 11:30:46 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 11:30:46 [Thread-12] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 11:30:46 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-01-16 11:30:46 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-01-16 11:30:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 11:30:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 11:30:47 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:30:47 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:30:47 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 11:30:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 11:30:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 11:30:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 11:30:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 11:30:47 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:30:47 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:30:47 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 11:30:47 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 330 ms
2025-01-16 11:30:47 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:30:47 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 11:30:47 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 11:30:47 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.758 seconds (JVM running for 66486.569)
2025-01-16 11:30:47 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 11:59:11 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 2 class path changes (0 additions, 1 deletion, 1 modification)
2025-01-16 11:59:11 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:11 [Thread-16] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 11:59:11 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:11 [Thread-16] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:11 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-01-16 11:59:11 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-01-16 11:59:11 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 11:59:11 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 11:59:11 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:59:11 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:59:11 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-16 11:59:11 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 11:59:11 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 11:59:11 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 11:59:11 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 11:59:11 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 11:59:11 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:11 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:59:11 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 11:59:11 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:59:11 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 333 ms
2025-01-16 11:59:11 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:59:11 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-01-16 11:59:11 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 11:59:12 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 11:59:12 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:12 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 11:59:12 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.822 seconds (JVM running for 68191.068)
2025-01-16 11:59:12 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 11:59:13 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-01-16 11:59:13 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:13 [Thread-20] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 11:59:13 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:13 [Thread-20] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:13 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-01-16 11:59:13 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-01-16 11:59:13 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 31368 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 11:59:13 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 11:59:13 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:59:13 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:59:13 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-01-16 11:59:13 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 11:59:13 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 11:59:13 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 11:59:13 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 11:59:13 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 11:59:13 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:13 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:59:13 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 11:59:13 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:59:13 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 239 ms
2025-01-16 11:59:13 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:59:13 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-01-16 11:59:14 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 11:59:14 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 11:59:14 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:14 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 11:59:14 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.547 seconds (JVM running for 68193.218)
2025-01-16 11:59:14 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 11:59:17 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:59:17 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:59:17 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-16 11:59:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closing ...
2025-01-16 11:59:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closed
2025-01-16 11:59:25 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@73bbde8e
2025-01-16 11:59:26 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 2088 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 11:59:26 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 11:59:26 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-16 11:59:26 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-16 11:59:27 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 11:59:27 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 11:59:27 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-01-16 11:59:27 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 11:59:27 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 11:59:27 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 11:59:27 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 11:59:27 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 11:59:28 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:28 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 11:59:28 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 11:59:28 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 11:59:28 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1640 ms
2025-01-16 11:59:28 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 11:59:28 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-16 11:59:29 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 11:59:30 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 11:59:30 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 11:59:30 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 11:59:30 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 4.236 seconds (JVM running for 4.834)
2025-01-16 11:59:30 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 11:59:30 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 11:59:30 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-01-16 11:59:40 [http-nio-8080-exec-7] INFO  c.o.S.controller.LoginController - 用户 linqiang_zeng 在 2025-01-16T11:59:40.403 登录系统
2025-01-16 11:59:40 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 11:59:58 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:04 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:04 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:05 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:13 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:13 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:13 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 12:00:13 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 13:36:02 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 13:37:30 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 3 class path changes (0 additions, 1 deletion, 2 modifications)
2025-01-16 13:37:30 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:30 [Thread-6] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 13:37:30 [Thread-6] INFO  o.a.c.c.C.[.[localhost].[/boot] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-01-16 13:37:30 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:30 [Thread-6] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:30 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-16 13:37:30 [Thread-6] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-16 13:37:31 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 2088 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 13:37:31 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 13:37:31 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:37:31 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:37:31 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 13:37:31 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 13:37:31 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 13:37:31 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 13:37:31 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 13:37:31 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:31 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:37:31 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 13:37:31 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 369 ms
2025-01-16 13:37:31 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:37:31 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 13:37:31 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 13:37:31 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.937 seconds (JVM running for 5886.45)
2025-01-16 13:37:31 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 13:37:33 [File Watcher] INFO  o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener - Restarting due to 1 class path change (1 addition, 0 deletions, 0 modifications)
2025-01-16 13:37:33 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:33 [Thread-8] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-01-16 13:37:33 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:33 [Thread-8] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:33 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-01-16 13:37:33 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-01-16 13:37:33 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 2088 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-16 13:37:33 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-16 13:37:33 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-16 13:37:33 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-16 13:37:33 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-01-16 13:37:33 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-16 13:37:33 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-16 13:37:33 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-16 13:37:33 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-16 13:37:33 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-16 13:37:33 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:33 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-16 13:37:33 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-16 13:37:33 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-16 13:37:33 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 304 ms
2025-01-16 13:37:33 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-16 13:37:33 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-01-16 13:37:34 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-16 13:37:34 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-16 13:37:34 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-16 13:37:34 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-16 13:37:34 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 0.677 seconds (JVM running for 5888.784)
2025-01-16 13:37:34 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-01-16 13:37:34 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-16 13:37:34 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-16 13:37:34 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-16 13:37:34 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 13:37:35 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 13:37:36 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 13:37:37 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验信息上传页面 页面
2025-01-16 13:37:37 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验存储路径上传页面 页面
2025-01-16 13:37:38 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 x页面 页面
2025-01-16 13:37:38 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 Yeoh本构数据上传页面 页面
2025-01-16 13:37:39 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 二级多项式本构数据上传页面 页面
2025-01-16 14:21:18 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 14:21:19 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:21:20 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:21:21 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:21:21 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:21:26 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:22:24 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:22:31 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验信息上传页面 页面
2025-01-16 14:23:12 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 用户日志操作页面 页面
2025-01-16 14:23:14 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料搜索页面 页面
2025-01-16 14:23:14 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 帘线材料搜索页面 页面
2025-01-16 14:23:14 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 Yeoh本构材料搜索页面 页面
2025-01-16 14:23:28 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-16 14:23:30 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 x页面 页面
2025-01-16 14:25:49 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 二级多项式本构数据上传页面 页面
2025-01-16 14:25:51 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 Yeoh本构数据上传页面 页面
2025-01-16 14:25:52 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 x页面 页面
2025-01-16 14:25:56 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验存储路径上传页面 页面
2025-01-16 14:26:12 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验信息上传页面 页面
2025-01-16 14:26:13 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:26:15 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:35:08 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:35:10 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验文件上传页面 页面
2025-01-16 14:35:13 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:37:20 [http-nio-8080-exec-1] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:37:20 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:37:21 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:37:51 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:37:53 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 14:37:55 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 14:39:14 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验信息上传页面 页面
2025-01-16 14:39:24 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验存储路径上传页面 页面
2025-01-16 16:04:04 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 16:04:08 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验样品添加页面 页面
2025-01-16 16:04:09 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验信息上传页面 页面
2025-01-16 16:04:10 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 实验存储路径上传页面 页面
2025-01-16 16:04:16 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 材料配方添加页面 页面
2025-01-16 16:07:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-01-16 16:07:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-01-19 16:48:07 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@73bbde8e
2025-01-19 16:48:07 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 18396 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-19 16:48:07 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-19 16:48:07 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-19 16:48:07 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-19 16:48:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-19 16:48:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-19 16:48:08 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-01-19 16:48:09 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-19 16:48:09 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-19 16:48:09 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-19 16:48:09 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-19 16:48:09 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-19 16:48:09 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-19 16:48:09 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-19 16:48:09 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-19 16:48:09 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-19 16:48:09 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1500 ms
2025-01-19 16:48:09 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-19 16:48:09 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-19 16:48:10 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-19 16:48:11 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-19 16:48:11 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-19 16:48:11 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-19 16:48:11 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 3.872 seconds (JVM running for 4.42)
2025-01-19 16:48:11 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-19 16:48:11 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-19 16:48:11 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-19 16:48:21 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-01-19 16:48:21 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-01-19 16:48:45 [Thread-0] DEBUG o.s.b.d.r.c.RestartClassLoader - Created RestartClassLoader org.springframework.boot.devtools.restart.classloader.RestartClassLoader@a37032c
2025-01-19 16:48:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Starting SpringBootDemoApplication using Java 1.8.0_422 on bj-zenglinqiang with PID 18392 (C:\Users\<USER>\Java\ETO\target\classes started by linqiang_zeng in C:\Users\<USER>\Java\ETO)
2025-01-19 16:48:46 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-01-19 16:48:46 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-01-19 16:48:46 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-01-19 16:48:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-01-19 16:48:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-01-19 16:48:46 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-01-19 16:48:47 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-01-19 16:48:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
2025-01-19 16:48:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-01-19 16:48:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-01-19 16:48:47 [restartedMain] INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1q  5 Jul 2022]
2025-01-19 16:48:47 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-01-19 16:48:47 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-01-19 16:48:47 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-01-19 16:48:47 [restartedMain] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring embedded WebApplicationContext
2025-01-19 16:48:47 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1405 ms
2025-01-19 16:48:47 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-01-19 16:48:48 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-01-19 16:48:48 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-01-19 16:48:49 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-01-19 16:48:49 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-01-19 16:48:49 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/boot'
2025-01-19 16:48:49 [restartedMain] INFO  c.o.S.SpringBootDemoApplication - Started SpringBootDemoApplication in 3.633 seconds (JVM running for 4.172)
2025-01-19 16:53:47 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/boot] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-01-19 16:53:47 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-01-19 16:53:47 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-01-19 16:55:34 [http-nio-8080-exec-6] INFO  c.o.S.controller.LoginController - 用户 linqiang_zeng 在 2025-01-19T16:55:34.135 登录系统
2025-01-19 16:55:34 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:56:22 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:56:49 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:57:24 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:58:14 [http-nio-8080-exec-10] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:58:27 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:58:39 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:58:40 [http-nio-8080-exec-5] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:58:54 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:59:20 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:59:33 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:59:34 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:59:35 [http-nio-8080-exec-2] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 16:59:59 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 17:00:00 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 17:00:00 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 17:00:10 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 17:00:11 [http-nio-8080-exec-3] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-19 17:00:15 [http-nio-8080-exec-8] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 用户日志操作页面 页面
2025-01-19 17:00:16 [http-nio-8080-exec-9] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-20 09:09:41 [http-nio-8080-exec-1] INFO  c.o.S.controller.LoginController - 用户 linqiang_zeng 在 2025-01-20T09:09:41.412 登录系统
2025-01-20 09:09:41 [http-nio-8080-exec-4] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-20 14:25:23 [http-nio-8080-exec-3] INFO  c.o.S.controller.LoginController - 用户 linqiang_zeng 在 2025-01-20T14:25:23.673 登录系统
2025-01-20 14:25:23 [http-nio-8080-exec-7] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
2025-01-20 14:36:07 [http-nio-8080-exec-6] INFO  c.o.S.controller.LayuiController - 用户 linqiang_zeng 访问了 主页面 页面
