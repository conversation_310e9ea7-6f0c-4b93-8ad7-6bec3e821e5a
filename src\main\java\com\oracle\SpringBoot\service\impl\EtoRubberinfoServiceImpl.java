package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoRubberinfo;
import com.oracle.SpringBoot.mapper.EtoRubberinfoMapper;
import com.oracle.SpringBoot.service.IEtoRubberinfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO胶料信息服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoRubberinfoServiceImpl extends ServiceImpl<EtoRubberinfoMapper, EtoRubberinfo> implements IEtoRubberinfoService {

    @Override
    public EtoRubberinfo getBySchemeId(Integer schemeId) {
        return this.lambdaQuery()
                .eq(EtoRubberinfo::getSchemeId, schemeId)
                .one();
    }
}
