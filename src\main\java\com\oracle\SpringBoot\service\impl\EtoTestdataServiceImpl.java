package com.oracle.SpringBoot.service.impl;

import com.oracle.SpringBoot.entity.EtoTestdata;
import com.oracle.SpringBoot.mapper.EtoTestdataMapper;
import com.oracle.SpringBoot.service.IEtoTestdataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ETO测试数据服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class EtoTestdataServiceImpl extends ServiceImpl<EtoTestdataMapper, EtoTestdata> implements IEtoTestdataService {

    @Override
    public EtoTestdata getBySchemeId(Integer schemeId) {
        return this.lambdaQuery()
                .eq(EtoTestdata::getSchemeId, schemeId)
                .one();
    }
}
