package com.oracle.SpringBoot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.oracle.SpringBoot.entity.PartSidewall;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 胎侧部件Mapper接口
 */
@Mapper
public interface PartSidewallMapper extends BaseMapper<PartSidewall> {
    
    /**
     * 根据部件名称查询
     */
    @Select("SELECT * FROM PartSidewall WHERE PART_NAME = #{partName} AND FLAG = 1")
    PartSidewall selectByPartName(String partName);
    
    /**
     * 根据SAP代码查询
     */
    @Select("SELECT * FROM PartSidewall WHERE PART_SAPCODE = #{sapCode} AND FLAG = 1")
    List<PartSidewall> selectBySapCode(String sapCode);
    
    /**
     * 获取所有有效的胎侧部件
     */
    @Select("SELECT * FROM PartSidewall WHERE FLAG = 1 ORDER BY CREATE_TIME DESC")
    List<PartSidewall> selectAllActive();
    
    /**
     * 根据胎侧胶料查询
     */
    @Select("SELECT * FROM PartSidewall WHERE SidewallCompound = #{compound} AND FLAG = 1")
    List<PartSidewall> selectBySidewallCompound(String compound);
    
    /**
     * 根据宽度范围查询
     */
    @Select("SELECT * FROM PartSidewall WHERE Width BETWEEN #{minWidth} AND #{maxWidth} AND FLAG = 1")
    List<PartSidewall> selectByWidthRange(Double minWidth, Double maxWidth);
}
