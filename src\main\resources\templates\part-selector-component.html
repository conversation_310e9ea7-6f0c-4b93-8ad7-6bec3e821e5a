<!-- 半部件选择组件 -->
<div class="part-selector-container" style="display: none;">
    <div class="part-selector-header">
        <h3>半部件配置</h3>
        <div class="part-selector-actions">
            <button type="button" class="layui-btn layui-btn-sm" id="loadTemplate">
                <i class="fas fa-download"></i> 加载模板
            </button>
            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="saveTemplate">
                <i class="fas fa-save"></i> 保存为模板
            </button>
        </div>
    </div>
    
    <!-- 部件配置表格 -->
    <div class="part-config-table">
        <table class="layui-table" id="partConfigTable">
            <thead>
                <tr>
                    <th>部件类型</th>
                    <th>部件名称</th>
                    <th>SAP代码</th>
                    <th>数量</th>
                    <th>位置</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 胎面 -->
                <tr data-part-type="tread">
                    <td>胎面 (Tread)</td>
                    <td>
                        <select class="layui-input part-selector" name="tread_part" lay-filter="partSelect">
                            <option value="">请选择胎面部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="1" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="位置"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('tread', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 胎侧 -->
                <tr data-part-type="sidewall">
                    <td>胎侧 (Sidewall)</td>
                    <td>
                        <select class="layui-input part-selector" name="sidewall_part" lay-filter="partSelect">
                            <option value="">请选择胎侧部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="2" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="左右" value="左右"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('sidewall', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 胎体 -->
                <tr data-part-type="carcass">
                    <td>胎体 (Carcass)</td>
                    <td>
                        <select class="layui-input part-selector" name="carcass_part" lay-filter="partSelect">
                            <option value="">请选择胎体部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="1" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="位置"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('carcass', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 内衬层 -->
                <tr data-part-type="innerliner">
                    <td>内衬层 (Inner Liner)</td>
                    <td>
                        <select class="layui-input part-selector" name="innerliner_part" lay-filter="partSelect">
                            <option value="">请选择内衬层部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="1" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="位置"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('innerliner', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 冠带层 -->
                <tr data-part-type="capply">
                    <td>冠带层 (Cap Ply)</td>
                    <td>
                        <select class="layui-input part-selector" name="capply_part" lay-filter="partSelect">
                            <option value="">请选择冠带层部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="1" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="位置"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('capply', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 带束层 -->
                <tr data-part-type="beltlayer">
                    <td>带束层 (Belt Layer)</td>
                    <td>
                        <select class="layui-input part-selector" name="beltlayer_part" lay-filter="partSelect">
                            <option value="">请选择带束层部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="2" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="1#/2#" value="1#/2#"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('beltlayer', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 钢丝圈 -->
                <tr data-part-type="beadwire">
                    <td>钢丝圈 (Bead Wire)</td>
                    <td>
                        <select class="layui-input part-selector" name="beadwire_part" lay-filter="partSelect">
                            <option value="">请选择钢丝圈部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="2" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="左右" value="左右"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('beadwire', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                
                <!-- 三角胶 -->
                <tr data-part-type="apex">
                    <td>三角胶 (Apex)</td>
                    <td>
                        <select class="layui-input part-selector" name="apex_part" lay-filter="partSelect">
                            <option value="">请选择三角胶部件</option>
                        </select>
                    </td>
                    <td><span class="sap-code">-</span></td>
                    <td><input type="number" class="layui-input quantity-input" value="2" min="1"></td>
                    <td><input type="text" class="layui-input position-input" placeholder="左右" value="左右"></td>
                    <td><span class="status-badge active">启用</span></td>
                    <td>
                        <button type="button" class="layui-btn layui-btn-xs" onclick="viewPartDetails('apex', this)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePart(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 添加自定义部件按钮 -->
    <div class="part-selector-footer">
        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="addCustomPart">
            <i class="fas fa-plus"></i> 添加自定义部件
        </button>
        <button type="button" class="layui-btn layui-btn-sm" id="validateCompatibility">
            <i class="fas fa-check-circle"></i> 验证兼容性
        </button>
    </div>
</div>

<style>
.part-selector-container {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #EBEEF5;
    border-radius: 8px;
    background: #fff;
}

.part-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #EBEEF5;
}

.part-selector-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.part-selector-actions {
    display: flex;
    gap: 10px;
}

.part-config-table {
    overflow-x: auto;
}

.part-config-table table {
    width: 100%;
    min-width: 800px;
}

.part-config-table td {
    vertical-align: middle;
}

.part-selector {
    min-width: 150px;
}

.quantity-input, .position-input {
    width: 80px;
    text-align: center;
}

.sap-code {
    font-family: monospace;
    color: #666;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: white;
}

.status-badge.active {
    background-color: #67C23A;
}

.status-badge.inactive {
    background-color: #909399;
}

.part-selector-footer {
    margin-top: 20px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #EBEEF5;
}
</style>
