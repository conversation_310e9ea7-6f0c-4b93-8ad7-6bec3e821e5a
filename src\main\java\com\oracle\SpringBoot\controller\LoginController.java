package com.oracle.SpringBoot.controller;

import com.oracle.SpringBoot.service.impl.UsersServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;

@RestController
public class LoginController {
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private UsersServiceImpl usersServiceImpl;

    @PostMapping("/loginInfo")
    public ResponseEntity<String> login(@RequestParam("username") String username,
                                        @RequestParam("password") String password,
                                        RedirectAttributes redirectAttributes,
                                        Model model, HttpServletRequest request) {
        boolean isValid = usersServiceImpl.validateUsers(username, password);
        boolean status = usersServiceImpl.validateStatus(username, password);
        if (isValid) {
            if (status) {
                HttpSession session = request.getSession();
                session.setAttribute("username", username);
                // 根据用户权限设置是否具有上传功能的权限
                boolean Verificationlevel = usersServiceImpl.Verificationlevel(username);
                boolean verificationlevel2 = usersServiceImpl.Verificationlevel2(username);
                session.setAttribute("Level_1", Verificationlevel);
                session.setAttribute("Level_2", verificationlevel2);

                // 添加登录日志记录
                logger.info("用户 {} 在 {} 登录系统", username, LocalDateTime.now());

                return ResponseEntity.status(HttpStatus.FOUND)
                        .header(HttpHeaders.LOCATION, "/boot/index")
                        .build();
            } else {
                redirectAttributes.addFlashAttribute("error2", "用户状态异常");
                return ResponseEntity.status(HttpStatus.FOUND)
                        .header(HttpHeaders.LOCATION, "/boot/loginETO") 
                        .build();
            }
        } else {
            redirectAttributes.addFlashAttribute("error", "用户名或密码错误");
            return ResponseEntity.status(HttpStatus.FOUND)
                    .header(HttpHeaders.LOCATION, "/boot/loginETO")
                    .build();
        }
    }

    @PostMapping("/changeP")
    public ResponseEntity<String> changePassword(@RequestParam("password") String password,
                                                 @RequestParam("username") String username,
                                                 @RequestParam("password1") String password1,
                                                 @RequestParam("password2") String password2,
                                                 RedirectAttributes redirectAttributes,
                                                 Model model, HttpServletRequest request) {

        boolean isValid = usersServiceImpl.validateUsers(username, password);
        boolean status = usersServiceImpl.validateStatus(username, password);
        if (isValid) {
            if (status) {
                if (!password1.equals(password2)) {
                    redirectAttributes.addFlashAttribute("error11", "新密码输入不一致");
                } else {
                    usersServiceImpl.updatePassword(username, password, password1);
                    redirectAttributes.addFlashAttribute("success", "密码修改成功");
                    return ResponseEntity.status(HttpStatus.FOUND)
                            .header(HttpHeaders.LOCATION, "/boot/loginETO")
                            .build();
                }
            } else {
                redirectAttributes.addFlashAttribute("error22", "用户已注销");
            }
            return ResponseEntity.status(HttpStatus.FOUND)
                    .header(HttpHeaders.LOCATION, "/boot/changePassword")
                    .build();
        }

        redirectAttributes.addFlashAttribute("error33", "账号或密码不正确好吗");
        return ResponseEntity.status(HttpStatus.FOUND)
                .header(HttpHeaders.LOCATION, "/boot/changePassword")
                .build();
    }
}
