package com.oracle.SpringBoot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.oracle.SpringBoot.entity.EtoConceptCompetitor;
import com.oracle.SpringBoot.entity.EtoConceptCompetitorValue;
import com.oracle.SpringBoot.entity.EtoConceptParam;
import com.oracle.SpringBoot.entity.EtoConceptTarget;
import com.oracle.SpringBoot.mapper.EtoConceptCompetitorMapper;
import com.oracle.SpringBoot.mapper.EtoConceptCompetitorValueMapper;
import com.oracle.SpringBoot.mapper.EtoConceptParamMapper;
import com.oracle.SpringBoot.mapper.EtoConceptTargetMapper;
import com.oracle.SpringBoot.service.EtoConceptService;
import com.oracle.SpringBoot.vo.CompetitorFormVO;
import com.oracle.SpringBoot.vo.ConceptDataVO;
import com.oracle.SpringBoot.vo.TargetFormVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ETO概念服务实现类
 */
@Service
public class EtoConceptServiceImpl implements EtoConceptService {
    
    @Autowired
    private EtoConceptParamMapper paramMapper;
    
    @Autowired
    private EtoConceptTargetMapper targetMapper;
    
    @Autowired
    private EtoConceptCompetitorMapper competitorMapper;
    
    @Autowired
    private EtoConceptCompetitorValueMapper competitorValueMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addParam(EtoConceptParam param) {
        // 设置创建时间
        if (param.getCreateTime() == null) {
            param.setCreateTime(LocalDateTime.now());
        }
        if (param.getUpdateTime() == null) {
            param.setUpdateTime(LocalDateTime.now());
        }
        
        // 保存参数
        paramMapper.insert(param);
        return param.getId();
    }
    
    @Override
    public List<EtoConceptParam> getProjectParams(Long projectId) {
        // 查询项目参数列表
        LambdaQueryWrapper<EtoConceptParam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EtoConceptParam::getProjectId, projectId);
        // 确保按照ID排序，保证参数顺序稳定
        queryWrapper.orderByAsc(EtoConceptParam::getId);
        return paramMapper.selectList(queryWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setTargetValues(TargetFormVO targetForm) {
        Long projectId = targetForm.getProjectId();
        Map<Long, String> targetValues = targetForm.getTargetValues();
        
        if (targetValues == null || targetValues.isEmpty()) {
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 获取现有的目标值
        LambdaQueryWrapper<EtoConceptTarget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EtoConceptTarget::getProjectId, projectId);
        List<EtoConceptTarget> existingTargets = targetMapper.selectList(queryWrapper);
        
        // 将现有目标值转换为Map，方便查询
        Map<Long, EtoConceptTarget> existingTargetMap = existingTargets.stream()
            .collect(Collectors.toMap(EtoConceptTarget::getParamId, target -> target));
        
        // 处理每个目标值
        for (Map.Entry<Long, String> entry : targetValues.entrySet()) {
            Long paramId = entry.getKey();
            String targetValue = entry.getValue();
            
            if (existingTargetMap.containsKey(paramId)) {
                // 更新现有目标值
                EtoConceptTarget target = existingTargetMap.get(paramId);
                target.setTargetValue(targetValue);
                target.setUpdateTime(now);
                targetMapper.updateById(target);
                
                // 从Map中移除，表示已处理
                existingTargetMap.remove(paramId);
            } else {
                // 添加新的目标值
            EtoConceptTarget target = new EtoConceptTarget();
            target.setProjectId(projectId);
            target.setParamId(paramId);
            target.setTargetValue(targetValue);
            target.setCreateTime(now);
            target.setUpdateTime(now);
            targetMapper.insert(target);
            }
        }
        
        // 注意：不删除未提及的目标值，保留它们
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addCompetitor(CompetitorFormVO competitorForm) {
        Long projectId = competitorForm.getProjectId();
        
        // 保存竞品信息
        EtoConceptCompetitor competitor = new EtoConceptCompetitor();
        competitor.setProjectId(projectId);
        competitor.setCompetitorName(competitorForm.getCompetitorName());
        competitor.setCreateTime(LocalDateTime.now());
        competitor.setUpdateTime(LocalDateTime.now());
        
        competitorMapper.insert(competitor);
        Long competitorId = competitor.getId();
        
        // 保存参数值
        if (competitorForm.getParamValues() != null && !competitorForm.getParamValues().isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            
            for (CompetitorFormVO.ParamValueVO paramValue : competitorForm.getParamValues()) {
                if (paramValue.getParamId() == null || paramValue.getValue() == null) {
                    continue;
                }
                
                EtoConceptCompetitorValue competitorValue = new EtoConceptCompetitorValue();
                competitorValue.setCompetitorId(competitorId);
                competitorValue.setParamId(paramValue.getParamId());
                competitorValue.setParamValue(paramValue.getValue());
                competitorValue.setCreateTime(now);
                competitorValue.setUpdateTime(now);
                
                competitorValueMapper.insert(competitorValue);
            }
        }
        
        return competitorId;
    }
    
    @Override
    public ConceptDataVO getProjectConceptData(Long projectId) {
        ConceptDataVO conceptData = new ConceptDataVO();
        
        // 1. 获取参数列表
        List<EtoConceptParam> params = getProjectParams(projectId);
        conceptData.setParams(params);
        
        if (params.isEmpty()) {
            conceptData.setTarget(new HashMap<>());
            conceptData.setCompetitors(new ArrayList<>());
            return conceptData;
        }
        
        // 2. 获取目标值
        LambdaQueryWrapper<EtoConceptTarget> targetWrapper = new LambdaQueryWrapper<>();
        targetWrapper.eq(EtoConceptTarget::getProjectId, projectId);
        List<EtoConceptTarget> targets = targetMapper.selectList(targetWrapper);
        
        // 将目标值转换为Map格式 - 确保使用参数ID作为键
        Map<Long, String> targetMap = new HashMap<>();
        for (EtoConceptTarget target : targets) {
            if (target.getParamId() != null) {
                targetMap.put(target.getParamId(), target.getTargetValue());
            }
        }
        conceptData.setTarget(targetMap);
        
        // 3. 获取竞品列表
        LambdaQueryWrapper<EtoConceptCompetitor> competitorWrapper = new LambdaQueryWrapper<>();
        competitorWrapper.eq(EtoConceptCompetitor::getProjectId, projectId);
        List<EtoConceptCompetitor> competitors = competitorMapper.selectList(competitorWrapper);
        
        if (!competitors.isEmpty()) {
            // 提取所有竞品ID
            List<Long> competitorIds = competitors.stream()
                .map(EtoConceptCompetitor::getId)
                .collect(Collectors.toList());
            
            // 4. 获取竞品参数值
            LambdaQueryWrapper<EtoConceptCompetitorValue> valueWrapper = new LambdaQueryWrapper<>();
            valueWrapper.in(EtoConceptCompetitorValue::getCompetitorId, competitorIds);
            List<EtoConceptCompetitorValue> competitorValues = competitorValueMapper.selectList(valueWrapper);
            
            // 按竞品ID分组参数值
            Map<Long, List<EtoConceptCompetitorValue>> valueMap = competitorValues.stream()
                .collect(Collectors.groupingBy(EtoConceptCompetitorValue::getCompetitorId));
            
            // 构建竞品VO列表
            List<ConceptDataVO.CompetitorVO> competitorVOList = new ArrayList<>();
            for (EtoConceptCompetitor competitor : competitors) {
                ConceptDataVO.CompetitorVO competitorVO = new ConceptDataVO.CompetitorVO();
                competitorVO.setId(competitor.getId());
                competitorVO.setName(competitor.getCompetitorName());
                
                // 将参数值转换为Map格式 - 确保使用参数ID作为键
                Map<Long, String> valueVOMap = new HashMap<>();
                List<EtoConceptCompetitorValue> values = valueMap.getOrDefault(competitor.getId(), new ArrayList<>());
                for (EtoConceptCompetitorValue value : values) {
                    if (value.getParamId() != null) {
                    valueVOMap.put(value.getParamId(), value.getParamValue());
                    }
                }
                competitorVO.setValues(valueVOMap);
                
                competitorVOList.add(competitorVO);
            }
            
            conceptData.setCompetitors(competitorVOList);
        } else {
            conceptData.setCompetitors(new ArrayList<>());
        }
        
        return conceptData;
    }
    
    @Override
    public List<EtoConceptCompetitor> getCompetitorsByProjectId(Long projectId) {
        LambdaQueryWrapper<EtoConceptCompetitor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EtoConceptCompetitor::getProjectId, projectId);
        return competitorMapper.selectList(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCompetitorValues(List<Long> competitorIds) {
        if (competitorIds == null || competitorIds.isEmpty()) {
            return;
        }
        
        LambdaQueryWrapper<EtoConceptCompetitorValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(EtoConceptCompetitorValue::getCompetitorId, competitorIds);
        competitorValueMapper.delete(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCompetitors(Long projectId) {
        if (projectId == null) {
            return;
        }
        
        LambdaQueryWrapper<EtoConceptCompetitor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EtoConceptCompetitor::getProjectId, projectId);
        competitorMapper.delete(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTargets(Long projectId) {
        if (projectId == null) {
            return;
        }
        
        LambdaQueryWrapper<EtoConceptTarget> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EtoConceptTarget::getProjectId, projectId);
        targetMapper.delete(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteParams(Long projectId) {
        if (projectId == null) {
            return;
        }
        
        LambdaQueryWrapper<EtoConceptParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EtoConceptParam::getProjectId, projectId);
        paramMapper.delete(wrapper);
    }
    
    @Override
    public EtoConceptParam getParamById(Long paramId) {
        if (paramId == null) {
            return null;
        }
        return paramMapper.selectById(paramId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteParamById(Long paramId) {
        if (paramId == null) {
            return;
        }
        paramMapper.deleteById(paramId);
    }
} 