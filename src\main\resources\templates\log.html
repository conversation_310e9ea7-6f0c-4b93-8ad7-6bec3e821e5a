<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>操作日志</title>
    <link rel="stylesheet" href="./layui/css/layui.css" type="text/css">
    <style>
        .layui-body {
            min-height: calc(100vh - 110px);
        }
        .layui-form-label {
            font-size: 15px; /* 设置标签文字的字号为18像素 */
            font-weight: bold; /* 设置标签文字加粗显示 */
        }
        .layui-logo img {
            border-radius: 50%; /* 将图片显示为圆形 */
            border-radius: 50%; /* 将图片显示为圆形 */
            margin-right: 10px; /* 将圆形图片向左移动10像素 */
        .layui-table {
            border: 2px solid #000; /* 设置表格边框为2像素粗的黑色实线 */
        }
        .layui-table th, .layui-table td {
            border: 2px solid #000; /* 设置表格每个单元格的边框为2像素粗的黑色实线 */
        }

    </style>
</head>
<body>
<div class="layui-layout layui-layout-admin">
    <div class="layui-header">
        <a href="index"><div class="layui-logo layui-hide-xs layui-bg-black" style=" font-size: 15px; font-weight: bold;"><img src="./images/26.png" height="50">材料库主页</div></a>
        <ul class="layui-nav layui-layout-right">
            <li class="layui-nav-item layui-hide layui-show-md-inline-block">
                <a href="javascript:;">
                    <img id="logoid" src="./images/people1.png" height="30" alt="用户">
                </a>
                <dl class="layui-nav-child">
                    <dd><a href="quit">退出</a></dd>
                </dl>
            </li>
            <li class="layui-nav-item" lay-header-event="menuRight" lay-unselect>
                <a href="javascript:;">
                    <i class="layui-icon layui-icon-more-vertical"></i>
                </a>
            </li>
        </ul>
    </div>
    <div class="layui-side layui-bg-black">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree" lay-filter="test">
                <#if Level_1>
                <li class="layui-nav-item ">
                    <a class="" href="javascript:;">橡胶材料添加</a>
                    <dl class="layui-nav-child">
                        <dd><a href="RubberExperimentalFormulaAddition">橡胶实验配方添加</a></dd>
                        <dd><a href="RubberExperimentalSampleAddition">橡胶实验样品添加</a></dd>
                        <dd><a href="RubberExperimentInformationUpload">橡胶试验信息添加</a></dd>
                        <dd><a href="RubberExperimentStoragePathUpload">橡胶试验文件信息添加</a></dd>
                        <dd><a href="RubberExperimentDataUpload">橡胶试验数据上传</a></dd>
                        <dd><a href="RubberYeohConstitutiveDataUpload">Yeoh本构数据添加</a></dd>
                        <dd><a href="RubberPolynomialConstitutiveDataUpload">二次多项式本构数据添加</a></dd>

                    </dl>
                </li>
            </#if>

            <#if Level_1>
            <li class="layui-nav-item ">
                <a class="" href="javascript:;">帘线材料添加</a>
                <dl class="layui-nav-child">
                    <dd><a href="CordExperimentInformationUpload">帘线实验信息添加</a></dd>
                    <dd><a href="CordExperimentalDataUpload">帘线实验数据添加</a></dd>
                </dl>
            </li>
        </#if>
            <li class="layui-nav-item">
                <a href="javascript:;">查找材料</a>
                <dl class="layui-nav-child">
                    <dd><a href="select">材料数据检索</a></dd>
                    <dd><a href="selectcord">帘线材料数据检索</a></dd>
                    <dd><a href="selectyeoh" >Yeoh材料数据检索</a></dd>

                </dl>
            </li>
            <#if Level_1>
            <#if Level_2>
            <li class="layui-nav-item layui-nav-itemed" ><a href="log" style="color: red; font-weight: bold;">操作日志</a></li>
        </#if>
    </#if>
    </ul>
</div>
</div>
<div class="layui-body">
    <h1 style="text-align: center; color:purple;padding: 15px;">操作日志</h1>
    <div style="padding: 50px; margin-top: 10px;">
        <button id="backupButton" class="layui-btn layui-btn-normal">备份日志</button>
        <table id="logTable" lay-filter="logTable"></table>
    </div>
</div>
<div class="layui-footer">
    <p style="text-align: center;">版权所有 &copy; 山东玲珑轮胎股份有限公司，保留所有权利&nbsp;&nbsp;All Rights Reserved, Version 1.0.0</p>
</div>
</div>
<script src="./js/jquery.min.js"></script>
<script src="./layui/layui.js"></script>
<script>
    layui.use(['table'], function () {
        var table = layui.table;
        // 获取日志数据
        $.ajax({
            url: "/boot/logs",
            method: "GET",
            success: function (data) {
                // 反转日志数据
                data.reverse();
                // 渲染表格
                table.render({
                elem: '#logTable',
                cols: [[
                    { field: 'line', title: '日志行', minWidth: 200 }
                ]],
                data: data.map(function (line, index) {
                    return { line: line };
                }),
                page: true,
                limit: 15, // 设置每页显示15条数据
                limits: [15, 30, 45, 60] // 设置可选择的页数选项
            });
            },
            error: function (xhr, status, error) {
                console.error(error);
            }
        });
    });

    // 记录当天是否已经备份过
    var hasBackedUpToday = false;

    // 添加备份按钮的点击事件
    $('#backupButton').click(function () {
        if (!hasBackedUpToday) {
            $.ajax({
                url: '/boot/SpringBoot/log/backup-manually',
                type: 'POST',
                success: function (response) {
                    layui.layer.msg(response);//备份成功
                    console.log(response);
                    hasBackedUpToday = true;
                },
                error: function (error) {
                    layui.layer.msg('备份失败：' + error.responseText);//备份失败
                }
            });
        } else {
            layui.layer.msg('当天已经备份过，不能重复备份');//重复备份
        }
    });
</script>
</body>
</html>
